<?php
/**
 * La classe qui gère l'API REST pour les schémas structurés.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 */

/**
 * La classe qui gère l'API REST pour les schémas structurés.
 *
 * Cette classe gère les endpoints de l'API REST pour les schémas structurés.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/structured-schemas
 * <AUTHOR> SEO Team
 */
class Boss_Structured_Schemas_API {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Instance de la classe de gestion des schémas.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_Manager    $schema_manager    Gère les schémas.
     */
    protected $schema_manager;

    /**
     * Instance de la classe de gestion des règles.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_Rules    $rules_manager    Gère les règles d'application.
     */
    protected $rules_manager;

    /**
     * Instance de la classe d'intégration IA.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Structured_Schemas_AI    $ai_manager    Gère l'intégration IA.
     */
    protected $ai_manager;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string                             $plugin_name     Le nom du plugin.
     * @param    string                             $version         La version du plugin.
     * @param    Boss_Optimizer_Settings            $settings        Instance de la classe de paramètres (peut être null).
     * @param    Boss_Structured_Schemas_Manager    $schema_manager  Instance de la classe de gestion des schémas (peut être null).
     * @param    Boss_Structured_Schemas_Rules      $rules_manager   Instance de la classe de gestion des règles (peut être null).
     * @param    Boss_Structured_Schemas_AI         $ai_manager      Instance de la classe d'intégration IA (peut être null).
     */
    public function __construct( $plugin_name, $version, $settings = null, $schema_manager = null, $rules_manager = null, $ai_manager = null ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;
        $this->schema_manager = $schema_manager;
        $this->rules_manager = $rules_manager;
        $this->ai_manager = $ai_manager;

        $this->register_routes();
    }

    /**
     * Enregistre les routes de l'API REST.
     *
     * @since    1.2.0
     */
    public function register_routes() {
        add_action( 'rest_api_init', array( $this, 'register_api_routes' ) );
    }

    /**
     * Enregistre les routes de l'API REST.
     *
     * @since    1.2.0
     */
    public function register_api_routes() {
        register_rest_route( 'boss-seo/v1', '/schemas', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_schemas' ),
                'permission_callback' => array( $this, 'check_permissions' )
            ),
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array( $this, 'create_schema' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );

        register_rest_route( 'boss-seo/v1', '/schemas/(?P<id>\d+)', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_schema' ),
                'permission_callback' => array( $this, 'check_permissions' )
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'update_schema' ),
                'permission_callback' => array( $this, 'check_permissions' )
            ),
            array(
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => array( $this, 'delete_schema' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );

        register_rest_route( 'boss-seo/v1', '/schemas/types', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_schema_types' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );

        register_rest_route( 'boss-seo/v1', '/schemas/properties/(?P<type>[a-zA-Z0-9_-]+)', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_schema_properties' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );

        register_rest_route( 'boss-seo/v1', '/rules', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_rules' ),
                'permission_callback' => array( $this, 'check_permissions' )
            ),
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array( $this, 'create_rule' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );

        register_rest_route( 'boss-seo/v1', '/rules/(?P<id>\d+)', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_rule' ),
                'permission_callback' => array( $this, 'check_permissions' )
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'update_rule' ),
                'permission_callback' => array( $this, 'check_permissions' )
            ),
            array(
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => array( $this, 'delete_rule' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );

        register_rest_route( 'boss-seo/v1', '/ai/generate', array(
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array( $this, 'generate_schema_with_ai' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );

        register_rest_route( 'boss-seo/v1', '/test', array(
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array( $this, 'test_schema' ),
                'permission_callback' => array( $this, 'check_permissions' )
            )
        ) );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère tous les schémas.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_schemas( $request ) {
        $args = array();

        // Filtrer par type
        if ( $request->get_param( 'type' ) ) {
            $args['type'] = sanitize_text_field( $request->get_param( 'type' ) );
        }

        // Filtrer par statut actif
        if ( $request->get_param( 'active' ) !== null ) {
            $args['active'] = (bool) $request->get_param( 'active' );
        }

        // Pagination
        if ( $request->get_param( 'limit' ) ) {
            $args['limit'] = intval( $request->get_param( 'limit' ) );
        }

        if ( $request->get_param( 'offset' ) ) {
            $args['offset'] = intval( $request->get_param( 'offset' ) );
        }

        // Tri
        if ( $request->get_param( 'orderby' ) ) {
            $args['orderby'] = sanitize_text_field( $request->get_param( 'orderby' ) );
        }

        if ( $request->get_param( 'order' ) ) {
            $args['order'] = sanitize_text_field( $request->get_param( 'order' ) );
        }

        $schemas = $this->schema_manager->get_schemas( $args );

        return rest_ensure_response( $schemas );
    }

    /**
     * Récupère un schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_schema( $request ) {
        $schema_id = intval( $request->get_param( 'id' ) );
        $schema = $this->schema_manager->get_schema( $schema_id );

        if ( is_wp_error( $schema ) ) {
            return new WP_Error( $schema->get_error_code(), $schema->get_error_message(), array( 'status' => 404 ) );
        }

        return rest_ensure_response( $schema );
    }

    /**
     * Crée un schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function create_schema( $request ) {
        $schema_data = $request->get_json_params();
        $schema_id = $this->schema_manager->create_schema( $schema_data );

        if ( is_wp_error( $schema_id ) ) {
            return new WP_Error( $schema_id->get_error_code(), $schema_id->get_error_message(), array( 'status' => 400 ) );
        }

        $schema = $this->schema_manager->get_schema( $schema_id );

        return rest_ensure_response( $schema );
    }

    /**
     * Met à jour un schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_schema( $request ) {
        $schema_id = intval( $request->get_param( 'id' ) );
        $schema_data = $request->get_json_params();
        $result = $this->schema_manager->update_schema( $schema_id, $schema_data );

        if ( is_wp_error( $result ) ) {
            return new WP_Error( $result->get_error_code(), $result->get_error_message(), array( 'status' => 400 ) );
        }

        $schema = $this->schema_manager->get_schema( $schema_id );

        return rest_ensure_response( $schema );
    }

    /**
     * Supprime un schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_schema( $request ) {
        $schema_id = intval( $request->get_param( 'id' ) );
        $result = $this->schema_manager->delete_schema( $schema_id );

        if ( is_wp_error( $result ) ) {
            return new WP_Error( $result->get_error_code(), $result->get_error_message(), array( 'status' => 400 ) );
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Schéma supprimé avec succès.', 'boss-seo' )
        ) );
    }

    /**
     * Récupère les types de schémas disponibles.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_schema_types( $request ) {
        $types = $this->schema_manager->get_schema_types();

        return rest_ensure_response( $types );
    }

    /**
     * Récupère les propriétés d'un type de schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_schema_properties( $request ) {
        $type = sanitize_text_field( $request->get_param( 'type' ) );
        $properties = $this->schema_manager->get_schema_properties( $type );

        return rest_ensure_response( $properties );
    }

    /**
     * Récupère toutes les règles.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_rules( $request ) {
        $args = array();

        // Filtrer par schéma
        if ( $request->get_param( 'schema_id' ) ) {
            $args['schema_id'] = intval( $request->get_param( 'schema_id' ) );
        }

        // Filtrer par statut actif
        if ( $request->get_param( 'active' ) !== null ) {
            $args['active'] = (bool) $request->get_param( 'active' );
        }

        // Pagination
        if ( $request->get_param( 'limit' ) ) {
            $args['limit'] = intval( $request->get_param( 'limit' ) );
        }

        if ( $request->get_param( 'offset' ) ) {
            $args['offset'] = intval( $request->get_param( 'offset' ) );
        }

        // Tri
        if ( $request->get_param( 'orderby' ) ) {
            $args['orderby'] = sanitize_text_field( $request->get_param( 'orderby' ) );
        }

        if ( $request->get_param( 'order' ) ) {
            $args['order'] = sanitize_text_field( $request->get_param( 'order' ) );
        }

        $rules = $this->rules_manager->get_rules( $args );

        return rest_ensure_response( $rules );
    }

    /**
     * Récupère une règle.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_rule( $request ) {
        $rule_id = intval( $request->get_param( 'id' ) );
        $rule = $this->rules_manager->get_rule( $rule_id );

        if ( is_wp_error( $rule ) ) {
            return new WP_Error( $rule->get_error_code(), $rule->get_error_message(), array( 'status' => 404 ) );
        }

        return rest_ensure_response( $rule );
    }

    /**
     * Crée une règle.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function create_rule( $request ) {
        $rule_data = $request->get_json_params();
        $rule_id = $this->rules_manager->create_rule( $rule_data );

        if ( is_wp_error( $rule_id ) ) {
            return new WP_Error( $rule_id->get_error_code(), $rule_id->get_error_message(), array( 'status' => 400 ) );
        }

        $rule = $this->rules_manager->get_rule( $rule_id );

        return rest_ensure_response( $rule );
    }

    /**
     * Met à jour une règle.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_rule( $request ) {
        $rule_id = intval( $request->get_param( 'id' ) );
        $rule_data = $request->get_json_params();
        $result = $this->rules_manager->update_rule( $rule_id, $rule_data );

        if ( is_wp_error( $result ) ) {
            return new WP_Error( $result->get_error_code(), $result->get_error_message(), array( 'status' => 400 ) );
        }

        $rule = $this->rules_manager->get_rule( $rule_id );

        return rest_ensure_response( $rule );
    }

    /**
     * Supprime une règle.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_rule( $request ) {
        $rule_id = intval( $request->get_param( 'id' ) );
        $result = $this->rules_manager->delete_rule( $rule_id );

        if ( is_wp_error( $result ) ) {
            return new WP_Error( $result->get_error_code(), $result->get_error_message(), array( 'status' => 400 ) );
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Règle supprimée avec succès.', 'boss-seo' )
        ) );
    }

    /**
     * Génère un schéma avec l'IA.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_schema_with_ai( $request ) {
        $post_id = intval( $request->get_param( 'post_id' ) );
        $schema_type = sanitize_text_field( $request->get_param( 'schema_type' ) );

        $schema = $this->ai_manager->generate_schema( $post_id, $schema_type );

        if ( is_wp_error( $schema ) ) {
            return new WP_Error( $schema->get_error_code(), $schema->get_error_message(), array( 'status' => 400 ) );
        }

        // Si l'option save est activée, créer le schéma
        if ( $request->get_param( 'save' ) ) {
            $schema_id = $this->ai_manager->create_schema_from_ai( $post_id, $schema );

            if ( is_wp_error( $schema_id ) ) {
                return new WP_Error( $schema_id->get_error_code(), $schema_id->get_error_message(), array( 'status' => 400 ) );
            }

            return rest_ensure_response( array(
                'success' => true,
                'schema_id' => $schema_id,
                'schema' => $schema,
                'message' => __( 'Schéma généré et enregistré avec succès.', 'boss-seo' )
            ) );
        }

        return rest_ensure_response( array(
            'success' => true,
            'schema' => $schema,
            'message' => __( 'Schéma généré avec succès.', 'boss-seo' )
        ) );
    }

    /**
     * Teste un schéma.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function test_schema( $request ) {
        $schema = $request->get_json_params();

        // Vérifier que le schéma est valide
        if ( ! isset( $schema['@type'] ) ) {
            return new WP_Error( 'invalid_schema', __( 'Le schéma n\'est pas valide.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Vérifier que le contexte est présent
        if ( ! isset( $schema['@context'] ) ) {
            $schema['@context'] = 'https://schema.org';
        }

        // Tester le schéma avec l'API Google
        $google_api_url = 'https://search.google.com/test/rich-results/perform';
        $response = wp_remote_post( $google_api_url, array(
            'body' => json_encode( $schema ),
            'headers' => array(
                'Content-Type' => 'application/json'
            )
        ) );

        if ( is_wp_error( $response ) ) {
            return new WP_Error( 'test_failed', __( 'Échec du test du schéma.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        $body = wp_remote_retrieve_body( $response );
        $result = json_decode( $body, true );

        return rest_ensure_response( array(
            'success' => true,
            'result' => $result,
            'message' => __( 'Test du schéma effectué avec succès.', 'boss-seo' )
        ) );
    }
}
