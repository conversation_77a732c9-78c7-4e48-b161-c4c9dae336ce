<?php
/**
 * Classe pour la gestion des sitemaps avancés.
 *
 * Cette classe gère les fonctionnalités de sitemaps avancés pour le plugin Boss SEO.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Advanced_Sitemaps {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * L'option pour les paramètres du sitemap.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $sitemap_option    L'option pour les paramètres du sitemap.
     */
    protected $sitemap_option;

    /**
     * L'historique des générations de sitemaps.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $sitemap_history_option    L'option pour l'historique des générations de sitemaps.
     */
    protected $sitemap_history_option;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->sitemap_option = $plugin_name . '_advanced_sitemap_settings';
        $this->sitemap_history_option = $plugin_name . '_sitemap_generation_history';
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Hooks pour les sitemaps
        add_action( 'init', array( $this, 'register_sitemaps' ) );

        // Hook pour la planification de la mise à jour des sitemaps
        add_action( 'boss_seo_update_sitemaps', array( $this, 'update_all_sitemaps' ) );

        // Hook pour l'activation de la planification
        add_action( 'boss_seo_activate_sitemap_schedule', array( $this, 'activate_sitemap_schedule' ) );

        // Hook pour la désactivation de la planification
        add_action( 'boss_seo_deactivate_sitemap_schedule', array( $this, 'deactivate_sitemap_schedule' ) );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_advanced_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_advanced_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/regenerate',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'regenerate_advanced_sitemap' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_sitemap_generation_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/ping',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'ping_search_engines' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/custom-urls',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Enregistre les sitemaps.
     *
     * @since    1.2.0
     */
    public function register_sitemaps() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Vérifier si les sitemaps sont activés
        if ( ! isset( $settings['enabled'] ) || ! $settings['enabled'] ) {
            return;
        }

        // Enregistrer les règles de réécriture pour les sitemaps
        add_action( 'wp_loaded', array( $this, 'add_sitemap_rewrite_rules' ) );

        // Ajouter les actions pour servir les sitemaps
        add_action( 'template_redirect', array( $this, 'serve_sitemaps' ) );
    }

    /**
     * Ajoute les règles de réécriture pour les sitemaps.
     *
     * @since    1.2.0
     */
    public function add_sitemap_rewrite_rules() {
        // Règle pour le sitemap principal
        add_rewrite_rule( '^sitemap\.xml$', 'index.php?boss_sitemap=index', 'top' );

        // Règle pour le sitemap d'images
        add_rewrite_rule( '^sitemap-image\.xml$', 'index.php?boss_sitemap=image', 'top' );

        // Règle pour le sitemap de vidéos
        add_rewrite_rule( '^sitemap-video\.xml$', 'index.php?boss_sitemap=video', 'top' );

        // Règle pour le sitemap de Web Stories
        add_rewrite_rule( '^sitemap-stories\.xml$', 'index.php?boss_sitemap=stories', 'top' );

        // Règle pour le sitemap de news
        add_rewrite_rule( '^sitemap-news\.xml$', 'index.php?boss_sitemap=news', 'top' );

        // Règle pour les sitemaps personnalisés
        add_rewrite_rule( '^sitemap-custom\.xml$', 'index.php?boss_sitemap=custom', 'top' );

        // Règle pour les sitemaps de taxonomies
        add_rewrite_rule( '^sitemap-tax-([^/]+)\.xml$', 'index.php?boss_sitemap=tax&boss_sitemap_tax=$matches[1]', 'top' );

        // Ajouter les variables de requête
        add_filter( 'query_vars', array( $this, 'add_sitemap_query_vars' ) );

        // Vider les règles de réécriture
        flush_rewrite_rules();
    }

    /**
     * Ajoute les variables de requête pour les sitemaps.
     *
     * @since    1.2.0
     * @param    array    $vars    Les variables de requête.
     * @return   array             Les variables de requête modifiées.
     */
    public function add_sitemap_query_vars( $vars ) {
        $vars[] = 'boss_sitemap';
        $vars[] = 'boss_sitemap_tax';
        return $vars;
    }

    /**
     * Sert les sitemaps.
     *
     * @since    1.2.0
     */
    public function serve_sitemaps() {
        $sitemap_type = get_query_var( 'boss_sitemap', '' );

        if ( empty( $sitemap_type ) ) {
            return;
        }

        // Définir l'en-tête Content-Type
        header( 'Content-Type: application/xml; charset=UTF-8' );

        // Désactiver la mise en cache
        header( 'Cache-Control: no-cache, no-store, must-revalidate' );
        header( 'Pragma: no-cache' );
        header( 'Expires: 0' );

        // Servir le sitemap approprié
        switch ( $sitemap_type ) {
            case 'index':
                $this->generate_index_sitemap();
                break;
            case 'image':
                $this->generate_image_sitemap();
                break;
            case 'video':
                $this->generate_video_sitemap();
                break;
            case 'stories':
                $this->generate_stories_sitemap();
                break;
            case 'news':
                $this->generate_news_sitemap();
                break;
            case 'custom':
                $this->generate_custom_sitemap();
                break;
            case 'tax':
                $tax = get_query_var( 'boss_sitemap_tax', '' );
                $this->generate_taxonomy_sitemap( $tax );
                break;
            default:
                // Sitemap non reconnu
                status_header( 404 );
                break;
        }

        exit;
    }

    /**
     * Génère le sitemap d'index.
     *
     * @since    1.2.0
     */
    public function generate_index_sitemap() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Commencer le XML
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Ajouter les références aux autres sitemaps
        $site_url = get_site_url();
        $last_mod = date( 'c' );

        // Sitemap d'images
        if ( isset( $settings['enableImageSitemap'] ) && $settings['enableImageSitemap'] ) {
            echo "\t<sitemap>\n";
            echo "\t\t<loc>{$site_url}/sitemap-image.xml</loc>\n";
            echo "\t\t<lastmod>{$last_mod}</lastmod>\n";
            echo "\t</sitemap>\n";
        }

        // Sitemap de vidéos
        if ( isset( $settings['enableVideoSitemap'] ) && $settings['enableVideoSitemap'] ) {
            echo "\t<sitemap>\n";
            echo "\t\t<loc>{$site_url}/sitemap-video.xml</loc>\n";
            echo "\t\t<lastmod>{$last_mod}</lastmod>\n";
            echo "\t</sitemap>\n";
        }

        // Sitemap de Web Stories
        if ( isset( $settings['enableStoriesSitemap'] ) && $settings['enableStoriesSitemap'] ) {
            echo "\t<sitemap>\n";
            echo "\t\t<loc>{$site_url}/sitemap-stories.xml</loc>\n";
            echo "\t\t<lastmod>{$last_mod}</lastmod>\n";
            echo "\t</sitemap>\n";
        }

        // Sitemap de news
        if ( isset( $settings['enableNewsSitemap'] ) && $settings['enableNewsSitemap'] ) {
            echo "\t<sitemap>\n";
            echo "\t\t<loc>{$site_url}/sitemap-news.xml</loc>\n";
            echo "\t\t<lastmod>{$last_mod}</lastmod>\n";
            echo "\t</sitemap>\n";
        }

        // Sitemap personnalisé
        if ( isset( $settings['enableCustomSitemap'] ) && $settings['enableCustomSitemap'] ) {
            echo "\t<sitemap>\n";
            echo "\t\t<loc>{$site_url}/sitemap-custom.xml</loc>\n";
            echo "\t\t<lastmod>{$last_mod}</lastmod>\n";
            echo "\t</sitemap>\n";
        }

        // Sitemaps de taxonomies
        if ( isset( $settings['enableTaxonomySitemaps'] ) && $settings['enableTaxonomySitemaps'] ) {
            $taxonomies = get_taxonomies( array( 'public' => true ), 'objects' );
            foreach ( $taxonomies as $taxonomy ) {
                echo "\t<sitemap>\n";
                echo "\t\t<loc>{$site_url}/sitemap-tax-{$taxonomy->name}.xml</loc>\n";
                echo "\t\t<lastmod>{$last_mod}</lastmod>\n";
                echo "\t</sitemap>\n";
            }
        }

        // Terminer le XML
        echo '</sitemapindex>';

        // Enregistrer l'historique de génération
        $this->log_sitemap_generation( 'index' );
    }

    /**
     * Génère le sitemap d'images.
     *
     * @since    1.2.0
     */
    public function generate_image_sitemap() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Commencer le XML
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">' . "\n";

        // Récupérer les types de contenu à inclure
        $post_types = isset( $settings['includedPostTypes'] ) ? $settings['includedPostTypes'] : array( 'post', 'page' );

        // Récupérer les articles avec des images
        $args = array(
            'post_type'      => $post_types,
            'post_status'    => 'publish',
            'posts_per_page' => -1,
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $post_id = get_the_ID();
                $permalink = get_permalink();
                $last_modified = get_the_modified_date( 'c' );

                // Récupérer les images du contenu
                $images = $this->get_images_from_post( $post_id );

                // Récupérer l'image mise en avant
                $featured_image_id = get_post_thumbnail_id( $post_id );
                if ( $featured_image_id ) {
                    $featured_image = wp_get_attachment_image_src( $featured_image_id, 'full' );
                    if ( $featured_image ) {
                        $images[] = array(
                            'loc'   => $featured_image[0],
                            'title' => get_the_title( $featured_image_id ),
                            'alt'   => get_post_meta( $featured_image_id, '_wp_attachment_image_alt', true ),
                        );
                    }
                }

                // Ajouter l'URL seulement si elle contient des images
                if ( ! empty( $images ) ) {
                    echo "\t<url>\n";
                    echo "\t\t<loc>" . esc_url( $permalink ) . "</loc>\n";
                    echo "\t\t<lastmod>" . esc_html( $last_modified ) . "</lastmod>\n";

                    // Ajouter les images
                    foreach ( $images as $image ) {
                        echo "\t\t<image:image>\n";
                        echo "\t\t\t<image:loc>" . esc_url( $image['loc'] ) . "</image:loc>\n";

                        if ( ! empty( $image['title'] ) ) {
                            echo "\t\t\t<image:title>" . esc_html( $image['title'] ) . "</image:title>\n";
                        }

                        if ( ! empty( $image['alt'] ) ) {
                            echo "\t\t\t<image:caption>" . esc_html( $image['alt'] ) . "</image:caption>\n";
                        }

                        echo "\t\t</image:image>\n";
                    }

                    echo "\t</url>\n";
                }
            }
        }

        wp_reset_postdata();

        // Terminer le XML
        echo '</urlset>';

        // Enregistrer l'historique de génération
        $this->log_sitemap_generation( 'image' );
    }

    /**
     * Génère le sitemap de vidéos.
     *
     * @since    1.2.0
     */
    public function generate_video_sitemap() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Commencer le XML
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">' . "\n";

        // Récupérer les types de contenu à inclure
        $post_types = isset( $settings['includedPostTypes'] ) ? $settings['includedPostTypes'] : array( 'post', 'page' );

        // Récupérer les articles avec des vidéos
        $args = array(
            'post_type'      => $post_types,
            'post_status'    => 'publish',
            'posts_per_page' => -1,
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $post_id = get_the_ID();
                $permalink = get_permalink();
                $last_modified = get_the_modified_date( 'c' );

                // Récupérer les vidéos du contenu
                $videos = $this->get_videos_from_post( $post_id );

                // Ajouter l'URL seulement si elle contient des vidéos
                if ( ! empty( $videos ) ) {
                    echo "\t<url>\n";
                    echo "\t\t<loc>" . esc_url( $permalink ) . "</loc>\n";
                    echo "\t\t<lastmod>" . esc_html( $last_modified ) . "</lastmod>\n";

                    // Ajouter les vidéos
                    foreach ( $videos as $video ) {
                        echo "\t\t<video:video>\n";
                        echo "\t\t\t<video:thumbnail_loc>" . esc_url( $video['thumbnail_loc'] ) . "</video:thumbnail_loc>\n";
                        echo "\t\t\t<video:title>" . esc_html( $video['title'] ) . "</video:title>\n";
                        echo "\t\t\t<video:description>" . esc_html( $video['description'] ) . "</video:description>\n";
                        echo "\t\t\t<video:content_loc>" . esc_url( $video['content_loc'] ) . "</video:content_loc>\n";

                        if ( ! empty( $video['duration'] ) ) {
                            echo "\t\t\t<video:duration>" . intval( $video['duration'] ) . "</video:duration>\n";
                        }

                        echo "\t\t</video:video>\n";
                    }

                    echo "\t</url>\n";
                }
            }
        }

        wp_reset_postdata();

        // Terminer le XML
        echo '</urlset>';

        // Enregistrer l'historique de génération
        $this->log_sitemap_generation( 'video' );
    }

    /**
     * Génère le sitemap de Web Stories.
     *
     * @since    1.2.0
     */
    public function generate_stories_sitemap() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Commencer le XML
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Vérifier si le plugin Web Stories est activé
        if ( post_type_exists( 'web-story' ) ) {
            // Récupérer les Web Stories
            $args = array(
                'post_type'      => 'web-story',
                'post_status'    => 'publish',
                'posts_per_page' => -1,
            );

            $query = new WP_Query( $args );

            if ( $query->have_posts() ) {
                while ( $query->have_posts() ) {
                    $query->the_post();

                    $permalink = get_permalink();
                    $last_modified = get_the_modified_date( 'c' );

                    echo "\t<url>\n";
                    echo "\t\t<loc>" . esc_url( $permalink ) . "</loc>\n";
                    echo "\t\t<lastmod>" . esc_html( $last_modified ) . "</lastmod>\n";
                    echo "\t\t<changefreq>weekly</changefreq>\n";
                    echo "\t\t<priority>0.8</priority>\n";
                    echo "\t</url>\n";
                }
            }

            wp_reset_postdata();
        }

        // Terminer le XML
        echo '</urlset>';

        // Enregistrer l'historique de génération
        $this->log_sitemap_generation( 'stories' );
    }

    /**
     * Génère le sitemap de news.
     *
     * @since    1.2.0
     */
    public function generate_news_sitemap() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Commencer le XML
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">' . "\n";

        // Récupérer les types de contenu à inclure
        $post_types = isset( $settings['newsPostTypes'] ) ? $settings['newsPostTypes'] : array( 'post' );

        // Récupérer les articles récents (moins de 48 heures)
        $args = array(
            'post_type'      => $post_types,
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'date_query'     => array(
                array(
                    'after'     => '2 days ago',
                    'inclusive' => true,
                ),
            ),
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();

                $permalink = get_permalink();
                $publication_date = get_the_date( 'c' );
                $title = get_the_title();

                // Récupérer les catégories
                $categories = get_the_category();
                $category_names = array();

                if ( ! empty( $categories ) ) {
                    foreach ( $categories as $category ) {
                        $category_names[] = $category->name;
                    }
                }

                $category_string = ! empty( $category_names ) ? implode( ', ', $category_names ) : 'General';

                echo "\t<url>\n";
                echo "\t\t<loc>" . esc_url( $permalink ) . "</loc>\n";
                echo "\t\t<news:news>\n";
                echo "\t\t\t<news:publication>\n";
                echo "\t\t\t\t<news:name>" . esc_html( get_bloginfo( 'name' ) ) . "</news:name>\n";
                echo "\t\t\t\t<news:language>" . esc_html( get_locale() ) . "</news:language>\n";
                echo "\t\t\t</news:publication>\n";
                echo "\t\t\t<news:publication_date>" . esc_html( $publication_date ) . "</news:publication_date>\n";
                echo "\t\t\t<news:title>" . esc_html( $title ) . "</news:title>\n";
                echo "\t\t\t<news:keywords>" . esc_html( $category_string ) . "</news:keywords>\n";
                echo "\t\t</news:news>\n";
                echo "\t</url>\n";
            }
        }

        wp_reset_postdata();

        // Terminer le XML
        echo '</urlset>';

        // Enregistrer l'historique de génération
        $this->log_sitemap_generation( 'news' );
    }

    /**
     * Génère le sitemap personnalisé.
     *
     * @since    1.2.0
     */
    public function generate_custom_sitemap() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Récupérer les URLs personnalisées
        $custom_urls = isset( $settings['customUrls'] ) ? $settings['customUrls'] : array();

        // Commencer le XML
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Ajouter les URLs personnalisées
        foreach ( $custom_urls as $url ) {
            echo "\t<url>\n";
            echo "\t\t<loc>" . esc_url( $url['loc'] ) . "</loc>\n";

            if ( ! empty( $url['lastmod'] ) ) {
                echo "\t\t<lastmod>" . esc_html( $url['lastmod'] ) . "</lastmod>\n";
            }

            if ( ! empty( $url['changefreq'] ) ) {
                echo "\t\t<changefreq>" . esc_html( $url['changefreq'] ) . "</changefreq>\n";
            }

            if ( ! empty( $url['priority'] ) ) {
                echo "\t\t<priority>" . esc_html( $url['priority'] ) . "</priority>\n";
            }

            echo "\t</url>\n";
        }

        // Terminer le XML
        echo '</urlset>';

        // Enregistrer l'historique de génération
        $this->log_sitemap_generation( 'custom' );
    }

    /**
     * Génère le sitemap de taxonomie.
     *
     * @since    1.2.0
     * @param    string    $taxonomy    Le nom de la taxonomie.
     */
    public function generate_taxonomy_sitemap( $taxonomy ) {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );

        // Commencer le XML
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Vérifier si la taxonomie existe
        if ( taxonomy_exists( $taxonomy ) ) {
            // Récupérer les termes de la taxonomie
            $terms = get_terms( array(
                'taxonomy'   => $taxonomy,
                'hide_empty' => true,
            ) );

            if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                foreach ( $terms as $term ) {
                    $permalink = get_term_link( $term );

                    if ( ! is_wp_error( $permalink ) ) {
                        echo "\t<url>\n";
                        echo "\t\t<loc>" . esc_url( $permalink ) . "</loc>\n";

                        // Ajouter la date de dernière modification
                        $last_post = get_posts( array(
                            'posts_per_page' => 1,
                            'tax_query'      => array(
                                array(
                                    'taxonomy' => $taxonomy,
                                    'field'    => 'term_id',
                                    'terms'    => $term->term_id,
                                ),
                            ),
                            'orderby'        => 'modified',
                            'order'          => 'DESC',
                        ) );

                        if ( ! empty( $last_post ) ) {
                            echo "\t\t<lastmod>" . esc_html( get_the_modified_date( 'c', $last_post[0]->ID ) ) . "</lastmod>\n";
                        }

                        // Ajouter la fréquence de changement et la priorité
                        $change_freq = isset( $settings['defaultChangeFreq'] ) ? $settings['defaultChangeFreq'] : 'weekly';
                        $priority = isset( $settings['defaultPriority'] ) ? $settings['defaultPriority'] : 0.7;

                        echo "\t\t<changefreq>" . esc_html( $change_freq ) . "</changefreq>\n";
                        echo "\t\t<priority>" . esc_html( $priority ) . "</priority>\n";

                        echo "\t</url>\n";
                    }
                }
            }
        }

        // Terminer le XML
        echo '</urlset>';

        // Enregistrer l'historique de génération
        $this->log_sitemap_generation( 'tax-' . $taxonomy );
    }
    
    /**
     * Récupère les images d'un post.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @return   array                 Les images du post.
     */
    public function get_images_from_post( $post_id ) {
        $images = array();
        
        // Récupérer le contenu du post
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            return $images;
        }
        
        $content = $post->post_content;
        
        // Analyser le contenu pour trouver les images
        if ( preg_match_all( '/<img [^>]+>/', $content, $matches ) ) {
            foreach ( $matches[0] as $img ) {
                // Récupérer l'URL de l'image
                if ( preg_match( '/src=[\'"](.*?)[\'"]/', $img, $src ) ) {
                    $image_url = $src[1];
                    
                    // Récupérer le titre de l'image
                    $title = '';
                    if ( preg_match( '/title=[\'"](.*?)[\'"]/', $img, $title_match ) ) {
                        $title = $title_match[1];
                    }
                    
                    // Récupérer le texte alternatif
                    $alt = '';
                    if ( preg_match( '/alt=[\'"](.*?)[\'"]/', $img, $alt_match ) ) {
                        $alt = $alt_match[1];
                    }
                    
                    $images[] = array(
                        'loc'  => $image_url,
                        'title' => $title,
                        'alt'  => $alt,
                    );
                }
            }
        }
        
        // Récupérer les images des galeries
        if ( preg_match_all( '/\[gallery.*ids=[\'"](.*?)[\'"]/s', $content, $galleries ) ) {
            foreach ( $galleries[1] as $gallery ) {
                $ids = explode( ',', $gallery );
                
                foreach ( $ids as $id ) {
                    $attachment = wp_get_attachment_image_src( $id, 'full' );
                    
                    if ( $attachment ) {
                        $images[] = array(
                            'loc'   => $attachment[0],
                            'title' => get_the_title( $id ),
                            'alt'   => get_post_meta( $id, '_wp_attachment_image_alt', true ),
                        );
                    }
                }
            }
        }
        
        // Récupérer toutes les pièces jointes du post
        $attachments = get_children( array(
            'post_parent'    => $post_id,
            'post_type'      => 'attachment',
            'post_mime_type' => 'image',
            'numberposts'    => -1,
        ) );
        
        if ( $attachments ) {
            foreach ( $attachments as $attachment ) {
                $attachment_url = wp_get_attachment_url( $attachment->ID );
                
                // Vérifier si l'image est déjà dans la liste
                $found = false;
                foreach ( $images as $image ) {
                    if ( $image['loc'] === $attachment_url ) {
                        $found = true;
                        break;
                    }
                }
                
                if ( ! $found ) {
                    $images[] = array(
                        'loc'   => $attachment_url,
                        'title' => $attachment->post_title,
                        'alt'   => get_post_meta( $attachment->ID, '_wp_attachment_image_alt', true ),
                    );
                }
            }
        }
        
        return $images;
    }
    
    /**
     * Récupère les vidéos d'un post.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @return   array                 Les vidéos du post.
     */
    public function get_videos_from_post( $post_id ) {
        $videos = array();
        
        // Récupérer le contenu du post
        $post = get_post( $post_id );
        
        if ( ! $post ) {
            return $videos;
        }
        
        $content = $post->post_content;
        
        // Rechercher les embeds de vidéos YouTube
        if ( preg_match_all( '#(?:https?:)?//(?:www\.)?(?:youtube\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})#i', $content, $youtube_matches ) ) {
            foreach ( $youtube_matches[1] as $youtube_id ) {
                $videos[] = array(
                    'thumbnail_loc' => 'https://img.youtube.com/vi/' . $youtube_id . '/maxresdefault.jpg',
                    'title'         => get_the_title( $post_id ),
                    'description'   => wp_trim_words( strip_tags( $post->post_content ), 30 ),
                    'content_loc'   => '',
                    'player_loc'    => 'https://www.youtube.com/embed/' . $youtube_id,
                    'duration'      => 0, // YouTube ne fournit pas cette information
                    'tags'          => $this->get_post_tags( $post_id ),
                );
            }
        }
        
        // Rechercher les shortcodes de vidéos WordPress
        if ( preg_match_all( '/\[video.*src=[\'"]([^\'"]+)[\'"]/i', $content, $wp_video_matches ) ) {
            foreach ( $wp_video_matches[1] as $video_src ) {
                $videos[] = array(
                    'thumbnail_loc' => '', // Pas de miniature disponible directement
                    'title'         => get_the_title( $post_id ),
                    'description'   => wp_trim_words( strip_tags( $post->post_content ), 30 ),
                    'content_loc'   => $video_src,
                    'player_loc'    => '',
                    'duration'      => 0, // Pas d'information disponible
                    'tags'          => $this->get_post_tags( $post_id ),
                );
            }
        }
        
        // Rechercher les embeds de vidéos Vimeo
        if ( preg_match_all( '#(?:https?:)?//(?:www\.)?vimeo\.com/(?:channels/(?:\w+/)?|groups/(?:[^/]+)/videos/|album/(?:\d+)/video/|)(\d+)(?:$|/|\?)#i', $content, $vimeo_matches ) ) {
            foreach ( $vimeo_matches[1] as $vimeo_id ) {
                // Pour Vimeo, on n'a pas directement accès aux miniatures sans l'API
                $videos[] = array(
                    'thumbnail_loc' => '', // Nécessiterait l'API de Vimeo
                    'title'         => get_the_title( $post_id ),
                    'description'   => wp_trim_words( strip_tags( $post->post_content ), 30 ),
                    'content_loc'   => '',
                    'player_loc'    => 'https://player.vimeo.com/video/' . $vimeo_id,
                    'duration'      => 0, // Nécessiterait l'API de Vimeo
                    'tags'          => $this->get_post_tags( $post_id ),
                );
            }
        }
        
        return $videos;
    }
    
    /**
     * Récupère les tags d'un post.
     *
     * @since    1.2.0
     * @param    int       $post_id    L'ID du post.
     * @return   array                 Les tags du post.
     */
    public function get_post_tags( $post_id ) {
        $tags = array();
        
        $post_tags = get_the_tags( $post_id );
        
        if ( $post_tags ) {
            foreach ( $post_tags as $tag ) {
                $tags[] = $tag->name;
            }
        }
        
        return $tags;
    }
    
    /**
     * Enregistre l'historique de génération des sitemaps.
     *
     * @since    1.2.0
     * @param    string    $type       Le type de sitemap.
     * @param    string    $status     Le statut de la génération (success, warning, error).
     * @param    string    $message    Le message d'information.
     */
    public function log_sitemap_generation( $type, $status = 'success', $message = '' ) {
        $history = get_option( $this->sitemap_history_option, array() );
        
        // Si l'historique est vide, initialiser un tableau
        if ( ! is_array( $history ) ) {
            $history = array();
        }
        
        // Limiter la taille de l'historique à 100 entrées
        if ( count( $history ) >= 100 ) {
            $history = array_slice( $history, -99 );
        }
        
        // Ajouter l'entrée d'historique
        $history[] = array(
            'timestamp' => current_time( 'mysql' ),
            'type'      => $type,
            'status'    => $status,
            'message'   => $message,
        );
        
        update_option( $this->sitemap_history_option, $history );
    }
    
    /**
     * Met à jour tous les sitemaps.
     *
     * @since    1.2.0
     */
    public function update_all_sitemaps() {
        // Récupérer les paramètres du sitemap
        $settings = get_option( $this->sitemap_option, array() );
        
        // Vérifier si les sitemaps sont activés
        if ( ! isset( $settings['enabled'] ) || ! $settings['enabled'] ) {
            return;
        }
        
        try {
            // Générer le sitemap d'index
            $this->generate_sitemap_file( 'index' );
            
            // Générer les sitemaps spécifiques
            if ( isset( $settings['enableImageSitemap'] ) && $settings['enableImageSitemap'] ) {
                $this->generate_sitemap_file( 'image' );
            }
            
            if ( isset( $settings['enableVideoSitemap'] ) && $settings['enableVideoSitemap'] ) {
                $this->generate_sitemap_file( 'video' );
            }
            
            if ( isset( $settings['enableStoriesSitemap'] ) && $settings['enableStoriesSitemap'] ) {
                $this->generate_sitemap_file( 'stories' );
}
} catch ( Exception $e ) {
            // Gérer l'exception, par exemple, journaliser l'erreur
            error_log( 'Erreur lors de la mise à jour des sitemaps : ' . $e->getMessage() );
        }

}
}