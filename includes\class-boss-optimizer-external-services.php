<?php
/**
 * Classe pour la gestion des services externes
 *
 * @package Boss_Optimizer
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Classe pour gérer les services externes
 */
class Boss_Optimizer_External_Services {
    /**
     * Instance unique de la classe
     *
     * @var Boss_Optimizer_External_Services
     */
    private static $instance = null;

    /**
     * Obtenir l'instance unique de la classe
     *
     * @return Boss_Optimizer_External_Services
     */
    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructeur
     */
    private function __construct() {
        // Enregistrer les endpoints REST API
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
    }

    /**
     * Enregistre les routes REST API
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/external-services/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/verify-api-key',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'verify_api_key' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/available',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_available_services' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/status',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_services_status' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/connect-google-search-console',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'connect_google_search_console' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/disconnect-google-search-console',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'disconnect_google_search_console' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/external-services/google-auth-url',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_google_auth_url' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur
     *
     * @return bool
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les paramètres des services externes
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_settings( $request ) {
        // Récupérer les paramètres des services externes
        $settings = get_option( 'boss_optimizer_external_services', array() );

        // Valeurs par défaut
        $default_settings = array(
            'google_pagespeed' => array(
                'enabled' => false,
                'api_key' => '',
            ),
            'google_search_console' => array(
                'enabled' => false,
                'connected' => false,
                'auth_token' => '',
                'refresh_token' => '',
                'site_url' => '',
            ),
            'image_optimization' => array(
                'service' => 'tinypng',
                'enabled' => false,
                'api_key' => '',
            ),
            'semrush' => array(
                'enabled' => false,
                'api_key' => '',
            ),
            'moz' => array(
                'enabled' => false,
                'access_id' => '',
                'secret_key' => '',
            ),
            'ahrefs' => array(
                'enabled' => false,
                'api_key' => '',
            ),
            'majestic' => array(
                'enabled' => false,
                'api_key' => '',
            ),
        );

        // Fusionner les paramètres avec les valeurs par défaut
        $settings = wp_parse_args( $settings, $default_settings );

        // Masquer les clés API sensibles
        foreach ( $settings as $service => $service_settings ) {
            if ( isset( $service_settings['api_key'] ) && ! empty( $service_settings['api_key'] ) ) {
                $settings[ $service ]['api_key'] = $this->mask_api_key( $service_settings['api_key'] );
            }

            if ( isset( $service_settings['secret_key'] ) && ! empty( $service_settings['secret_key'] ) ) {
                $settings[ $service ]['secret_key'] = $this->mask_api_key( $service_settings['secret_key'] );
            }

            if ( isset( $service_settings['access_id'] ) && ! empty( $service_settings['access_id'] ) ) {
                $settings[ $service ]['access_id'] = $this->mask_api_key( $service_settings['access_id'] );
            }

            if ( isset( $service_settings['auth_token'] ) && ! empty( $service_settings['auth_token'] ) ) {
                $settings[ $service ]['auth_token'] = $this->mask_api_key( $service_settings['auth_token'] );
            }

            if ( isset( $service_settings['refresh_token'] ) && ! empty( $service_settings['refresh_token'] ) ) {
                $settings[ $service ]['refresh_token'] = $this->mask_api_key( $service_settings['refresh_token'] );
            }
        }

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres des services externes
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function save_settings( $request ) {
        // Récupérer les paramètres
        $settings = isset( $request['settings'] ) ? $request['settings'] : array();

        if ( empty( $settings ) ) {
            return new WP_Error( 'missing_settings', __( 'Paramètres manquants', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les paramètres actuels
        $current_settings = get_option( 'boss_optimizer_external_services', array() );

        // Fusionner les paramètres
        $new_settings = wp_parse_args( $settings, $current_settings );

        // Restaurer les clés API masquées
        foreach ( $new_settings as $service => $service_settings ) {
            if ( isset( $service_settings['api_key'] ) && $this->is_masked_key( $service_settings['api_key'] ) ) {
                $new_settings[ $service ]['api_key'] = isset( $current_settings[ $service ]['api_key'] ) ? $current_settings[ $service ]['api_key'] : '';
            }

            if ( isset( $service_settings['secret_key'] ) && $this->is_masked_key( $service_settings['secret_key'] ) ) {
                $new_settings[ $service ]['secret_key'] = isset( $current_settings[ $service ]['secret_key'] ) ? $current_settings[ $service ]['secret_key'] : '';
            }

            if ( isset( $service_settings['access_id'] ) && $this->is_masked_key( $service_settings['access_id'] ) ) {
                $new_settings[ $service ]['access_id'] = isset( $current_settings[ $service ]['access_id'] ) ? $current_settings[ $service ]['access_id'] : '';
            }

            if ( isset( $service_settings['auth_token'] ) && $this->is_masked_key( $service_settings['auth_token'] ) ) {
                $new_settings[ $service ]['auth_token'] = isset( $current_settings[ $service ]['auth_token'] ) ? $current_settings[ $service ]['auth_token'] : '';
            }

            if ( isset( $service_settings['refresh_token'] ) && $this->is_masked_key( $service_settings['refresh_token'] ) ) {
                $new_settings[ $service ]['refresh_token'] = isset( $current_settings[ $service ]['refresh_token'] ) ? $current_settings[ $service ]['refresh_token'] : '';
            }
        }

        // Enregistrer les paramètres
        update_option( 'boss_optimizer_external_services', $new_settings );

        // Mettre à jour les options spécifiques pour certains services
        if ( isset( $new_settings['google_pagespeed']['api_key'] ) ) {
            update_option( 'boss_optimizer_pagespeed_api_key', $new_settings['google_pagespeed']['api_key'] );
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Paramètres enregistrés avec succès', 'boss-seo' ),
        ) );
    }

    /**
     * Vérifie une clé API
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function verify_api_key( $request ) {
        // Récupérer les paramètres
        $service = isset( $request['service'] ) ? sanitize_text_field( $request['service'] ) : '';
        $api_key = isset( $request['api_key'] ) ? sanitize_text_field( $request['api_key'] ) : '';

        if ( empty( $service ) || empty( $api_key ) ) {
            return new WP_Error( 'missing_parameters', __( 'Service ou clé API manquant', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Vérifier la clé API en fonction du service
        switch ( $service ) {
            case 'google_pagespeed':
                $result = $this->verify_pagespeed_api_key( $api_key );
                break;

            case 'semrush':
                $result = $this->verify_semrush_api_key( $api_key );
                break;

            case 'moz':
                $access_id = isset( $request['access_id'] ) ? sanitize_text_field( $request['access_id'] ) : '';
                $result = $this->verify_moz_api_key( $access_id, $api_key );
                break;

            case 'ahrefs':
                $result = $this->verify_ahrefs_api_key( $api_key );
                break;

            case 'majestic':
                $result = $this->verify_majestic_api_key( $api_key );
                break;

            case 'tinypng':
                $result = $this->verify_tinypng_api_key( $api_key );
                break;

            default:
                $result = array(
                    'success' => false,
                    'message' => __( 'Service non pris en charge', 'boss-seo' ),
                );
                break;
        }

        return rest_ensure_response( $result );
    }

    /**
     * Récupère la liste des services disponibles
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_available_services( $request ) {
        $services = array(
            'google_pagespeed' => array(
                'name' => __( 'Google PageSpeed Insights', 'boss-seo' ),
                'description' => __( 'Analyse les performances de votre site', 'boss-seo' ),
                'url' => 'https://developers.google.com/speed/docs/insights/v5/get-started',
                'icon' => 'dashicons-performance',
            ),
            'google_search_console' => array(
                'name' => __( 'Google Search Console', 'boss-seo' ),
                'description' => __( 'Surveille la présence de votre site dans les résultats de recherche Google', 'boss-seo' ),
                'url' => 'https://search.google.com/search-console/about',
                'icon' => 'dashicons-google',
            ),
            'tinypng' => array(
                'name' => __( 'TinyPNG', 'boss-seo' ),
                'description' => __( 'Optimise les images de votre site', 'boss-seo' ),
                'url' => 'https://tinypng.com/developers',
                'icon' => 'dashicons-format-image',
            ),
            'semrush' => array(
                'name' => __( 'SEMrush', 'boss-seo' ),
                'description' => __( 'Analyse les mots-clés et la concurrence', 'boss-seo' ),
                'url' => 'https://www.semrush.com/api-documentation/',
                'icon' => 'dashicons-chart-line',
            ),
            'moz' => array(
                'name' => __( 'Moz', 'boss-seo' ),
                'description' => __( 'Analyse l\'autorité de domaine et les backlinks', 'boss-seo' ),
                'url' => 'https://moz.com/products/api',
                'icon' => 'dashicons-chart-bar',
            ),
            'ahrefs' => array(
                'name' => __( 'Ahrefs', 'boss-seo' ),
                'description' => __( 'Analyse les backlinks et les mots-clés', 'boss-seo' ),
                'url' => 'https://ahrefs.com/api/documentation',
                'icon' => 'dashicons-admin-links',
            ),
            'majestic' => array(
                'name' => __( 'Majestic', 'boss-seo' ),
                'description' => __( 'Analyse les backlinks et la confiance du site', 'boss-seo' ),
                'url' => 'https://developer-support.majestic.com/api/commands/',
                'icon' => 'dashicons-visibility',
            ),
        );

        return rest_ensure_response( $services );
    }

    /**
     * Récupère le statut des services
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_services_status( $request ) {
        // Récupérer les paramètres des services externes
        $settings = get_option( 'boss_optimizer_external_services', array() );

        // Statut par défaut
        $status = array(
            'google_pagespeed' => array(
                'connected' => false,
                'status' => 'inactive',
            ),
            'google_search_console' => array(
                'connected' => false,
                'status' => 'inactive',
            ),
            'image_optimization' => array(
                'connected' => false,
                'status' => 'inactive',
            ),
            'semrush' => array(
                'connected' => false,
                'status' => 'inactive',
            ),
            'moz' => array(
                'connected' => false,
                'status' => 'inactive',
            ),
            'ahrefs' => array(
                'connected' => false,
                'status' => 'inactive',
            ),
            'majestic' => array(
                'connected' => false,
                'status' => 'inactive',
            ),
        );

        // Mettre à jour le statut en fonction des paramètres
        foreach ( $settings as $service => $service_settings ) {
            if ( isset( $service_settings['enabled'] ) && $service_settings['enabled'] ) {
                $status[ $service ]['status'] = 'active';

                // Vérifier si le service est connecté
                if ( $service === 'google_search_console' ) {
                    $status[ $service ]['connected'] = isset( $service_settings['connected'] ) && $service_settings['connected'];
                } else {
                    $has_api_key = false;

                    if ( isset( $service_settings['api_key'] ) && ! empty( $service_settings['api_key'] ) ) {
                        $has_api_key = true;
                    }

                    if ( $service === 'moz' ) {
                        $has_api_key = $has_api_key && isset( $service_settings['access_id'] ) && ! empty( $service_settings['access_id'] );
                    }

                    $status[ $service ]['connected'] = $has_api_key;
                }
            }
        }

        return rest_ensure_response( $status );
    }

    /**
     * Connecte un compte Google Search Console
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function connect_google_search_console( $request ) {
        // Récupérer le code d'autorisation
        $auth_code = isset( $request['auth_code'] ) ? sanitize_text_field( $request['auth_code'] ) : '';

        if ( empty( $auth_code ) ) {
            return new WP_Error( 'missing_auth_code', __( 'Code d\'autorisation manquant', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Échanger le code d'autorisation contre un jeton d'accès
        $tokens = $this->exchange_auth_code_for_tokens( $auth_code );

        if ( is_wp_error( $tokens ) ) {
            return $tokens;
        }

        // Récupérer les paramètres actuels
        $settings = get_option( 'boss_optimizer_external_services', array() );

        // Mettre à jour les paramètres
        if ( ! isset( $settings['google_search_console'] ) ) {
            $settings['google_search_console'] = array();
        }

        $settings['google_search_console']['connected'] = true;
        $settings['google_search_console']['auth_token'] = $tokens['access_token'];
        $settings['google_search_console']['refresh_token'] = $tokens['refresh_token'];
        $settings['google_search_console']['token_expiry'] = time() + $tokens['expires_in'];

        // Enregistrer les paramètres
        update_option( 'boss_optimizer_external_services', $settings );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Connexion à Google Search Console réussie', 'boss-seo' ),
        ) );
    }

    /**
     * Déconnecte un compte Google Search Console
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function disconnect_google_search_console( $request ) {
        // Récupérer les paramètres actuels
        $settings = get_option( 'boss_optimizer_external_services', array() );

        // Mettre à jour les paramètres
        if ( isset( $settings['google_search_console'] ) ) {
            $settings['google_search_console']['connected'] = false;
            $settings['google_search_console']['auth_token'] = '';
            $settings['google_search_console']['refresh_token'] = '';
            $settings['google_search_console']['token_expiry'] = 0;
        }

        // Enregistrer les paramètres
        update_option( 'boss_optimizer_external_services', $settings );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Déconnexion de Google Search Console réussie', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère l'URL d'autorisation OAuth pour Google
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_google_auth_url( $request ) {
        // ID client OAuth
        $client_id = get_option( 'boss_optimizer_google_oauth_client_id', '' );

        if ( empty( $client_id ) ) {
            return new WP_Error( 'missing_client_id', __( 'ID client OAuth manquant', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // URL de redirection
        $redirect_uri = admin_url( 'admin-ajax.php?action=boss_optimizer_google_oauth_callback' );

        // URL d'autorisation
        $auth_url = add_query_arg(
            array(
                'client_id' => $client_id,
                'redirect_uri' => urlencode( $redirect_uri ),
                'response_type' => 'code',
                'scope' => 'https://www.googleapis.com/auth/webmasters.readonly',
                'access_type' => 'offline',
                'prompt' => 'consent',
            ),
            'https://accounts.google.com/o/oauth2/auth'
        );

        return rest_ensure_response( array(
            'url' => $auth_url,
        ) );
    }

    /**
     * Échange un code d'autorisation contre des jetons d'accès et de rafraîchissement
     *
     * @param string $auth_code Code d'autorisation
     * @return array|WP_Error Jetons ou erreur
     */
    private function exchange_auth_code_for_tokens( $auth_code ) {
        // ID client et secret OAuth
        $client_id = get_option( 'boss_optimizer_google_oauth_client_id', '' );
        $client_secret = get_option( 'boss_optimizer_google_oauth_client_secret', '' );

        if ( empty( $client_id ) || empty( $client_secret ) ) {
            return new WP_Error( 'missing_credentials', __( 'Identifiants OAuth manquants', 'boss-seo' ) );
        }

        // URL de redirection
        $redirect_uri = admin_url( 'admin-ajax.php?action=boss_optimizer_google_oauth_callback' );

        // Paramètres de la requête
        $params = array(
            'code' => $auth_code,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'authorization_code',
        );

        // Effectuer la requête
        $response = wp_remote_post(
            'https://oauth2.googleapis.com/token',
            array(
                'body' => $params,
                'timeout' => 30,
            )
        );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        if ( empty( $data ) || isset( $data['error'] ) ) {
            return new WP_Error(
                'oauth_error',
                isset( $data['error_description'] ) ? $data['error_description'] : __( 'Erreur lors de l\'échange du code d\'autorisation', 'boss-seo' )
            );
        }

        return $data;
    }

    /**
     * Vérifie une clé API PageSpeed Insights
     *
     * @param string $api_key Clé API
     * @return array Résultat de la vérification
     */
    private function verify_pagespeed_api_key( $api_key ) {
        // URL de test
        $test_url = add_query_arg(
            array(
                'url' => urlencode( home_url() ),
                'key' => $api_key,
            ),
            'https://www.googleapis.com/pagespeedonline/v5/runPagespeed'
        );

        // Effectuer la requête
        $response = wp_remote_get( $test_url );

        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => $response->get_error_message(),
            );
        }

        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        if ( empty( $data ) || isset( $data['error'] ) ) {
            return array(
                'success' => false,
                'message' => isset( $data['error']['message'] ) ? $data['error']['message'] : __( 'Clé API invalide', 'boss-seo' ),
            );
        }

        return array(
            'success' => true,
            'message' => __( 'Clé API valide', 'boss-seo' ),
        );
    }

    /**
     * Masque une clé API pour l'affichage
     *
     * @param string $api_key Clé API
     * @return string Clé API masquée
     */
    private function mask_api_key( $api_key ) {
        if ( empty( $api_key ) ) {
            return '';
        }

        $length = strlen( $api_key );
        $visible_chars = min( 4, $length );
        $masked_length = $length - $visible_chars;

        return substr( $api_key, 0, $visible_chars ) . str_repeat( '•', $masked_length );
    }

    /**
     * Vérifie si une clé API est masquée
     *
     * @param string $api_key Clé API
     * @return bool True si la clé est masquée, false sinon
     */
    private function is_masked_key( $api_key ) {
        return strpos( $api_key, '•' ) !== false;
    }

    /**
     * Méthodes de vérification spécifiques (stubs pour l'implémentation future)
     */
    private function verify_semrush_api_key( $api_key ) {
        // Implémentation fictive pour la démo
        return array(
            'success' => true,
            'message' => __( 'Clé API valide', 'boss-seo' ),
        );
    }

    private function verify_moz_api_key( $access_id, $api_key ) {
        // Implémentation fictive pour la démo
        return array(
            'success' => true,
            'message' => __( 'Clé API valide', 'boss-seo' ),
        );
    }

    private function verify_ahrefs_api_key( $api_key ) {
        // Implémentation fictive pour la démo
        return array(
            'success' => true,
            'message' => __( 'Clé API valide', 'boss-seo' ),
        );
    }

    private function verify_majestic_api_key( $api_key ) {
        // Implémentation fictive pour la démo
        return array(
            'success' => true,
            'message' => __( 'Clé API valide', 'boss-seo' ),
        );
    }

    private function verify_tinypng_api_key( $api_key ) {
        // Implémentation fictive pour la démo
        return array(
            'success' => true,
            'message' => __( 'Clé API valide', 'boss-seo' ),
        );
    }

    /**
     * Gère le callback OAuth de Google
     */
    public function handle_google_oauth_callback() {
        // Vérifier si l'utilisateur est connecté et a les permissions
        if ( ! is_user_logged_in() || ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'boss-seo' ) );
        }

        // Vérifier si le code d'autorisation est présent
        if ( ! isset( $_GET['code'] ) ) {
            wp_die( __( 'Code d\'autorisation manquant.', 'boss-seo' ) );
        }

        // Récupérer le code d'autorisation
        $auth_code = sanitize_text_field( $_GET['code'] );

        // Échanger le code d'autorisation contre un jeton d'accès
        $tokens = $this->exchange_auth_code_for_tokens( $auth_code );

        if ( is_wp_error( $tokens ) ) {
            wp_die( $tokens->get_error_message() );
        }

        // Récupérer les paramètres actuels
        $settings = get_option( 'boss_optimizer_external_services', array() );

        // Mettre à jour les paramètres
        if ( ! isset( $settings['google_search_console'] ) ) {
            $settings['google_search_console'] = array();
        }

        $settings['google_search_console']['connected'] = true;
        $settings['google_search_console']['auth_token'] = $tokens['access_token'];
        $settings['google_search_console']['refresh_token'] = $tokens['refresh_token'];
        $settings['google_search_console']['token_expiry'] = time() + $tokens['expires_in'];

        // Enregistrer les paramètres
        update_option( 'boss_optimizer_external_services', $settings );

        // Rediriger vers la page des paramètres
        wp_redirect( admin_url( 'admin.php?page=boss-seo-settings&tab=external-services&connected=1' ) );
        exit;
    }
}