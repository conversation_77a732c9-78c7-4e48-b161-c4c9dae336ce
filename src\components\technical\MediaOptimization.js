import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  TabPanel,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer les sous-composants
import MediaOverview from './media/MediaOverview';
import MediaList from './media/MediaList';
import MediaSettings from './media/MediaSettings';
import BulkOptimization from './media/BulkOptimization';

const MediaOptimization = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [media, setMedia] = useState([]);
  const [stats, setStats] = useState({
    totalMedia: 0,
    optimizedMedia: 0,
    unoptimizedMedia: 0,
    spaceSaved: 0,
    mediaTypes: {
      jpeg: 0,
      png: 0,
      gif: 0,
      webp: 0,
      svg: 0
    },
    issues: {
      uncompressed: 0,
      missingAlt: 0,
      oversized: 0,
      noLazyLoading: 0
    }
  });
  const [settings, setSettings] = useState({
    compression: {
      enabled: true,
      jpegQuality: 85,
      pngQuality: 7,
      convertToWebP: true,
      keepExif: false
    },
    resize: {
      enabled: true,
      maxWidth: 1920,
      maxHeight: 1080,
      method: 'proportional'
    },
    altText: {
      autoGenerate: true,
      aiModel: 'standard'
    },
    lazyLoading: {
      enabled: true,
      useNative: true,
      threshold: 200
    }
  });
  const [progress, setProgress] = useState({
    percent: 0,
    current: 0,
    total: 0,
    currentImage: null
  });
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Générer des données fictives pour les médias
      const mockMedia = [];
      const totalCount = 120;
      const optimizedCount = 68;
      
      for (let i = 1; i <= totalCount; i++) {
        const isOptimized = i <= optimizedCount;
        const type = i % 5 === 0 ? 'png' : i % 7 === 0 ? 'gif' : i % 11 === 0 ? 'webp' : i % 13 === 0 ? 'svg' : 'jpeg';
        const width = Math.floor(Math.random() * 1000) + 500;
        const height = Math.floor(Math.random() * 1000) + 500;
        const originalSize = Math.floor(Math.random() * 2000000) + 500000; // 500KB - 2.5MB
        const size = isOptimized ? Math.floor(originalSize * (Math.random() * 0.3 + 0.5)) : originalSize; // 50-80% de l'original si optimisé
        
        mockMedia.push({
          id: i,
          name: `image-${i}.${type}`,
          type: type,
          url: `https://example.com/wp-content/uploads/2023/06/image-${i}.${type}`,
          thumbnail: i % 3 === 0 ? `https://example.com/wp-content/uploads/2023/06/image-${i}-thumbnail.${type}` : null,
          dimensions: {
            width: width,
            height: height
          },
          size: size,
          originalSize: isOptimized ? originalSize : null,
          alt: i % 4 === 0 ? null : `Description de l'image ${i}`,
          status: isOptimized ? 'optimized' : 'unoptimized',
          date: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        });
      }
      
      // Calculer les statistiques
      const mockStats = {
        totalMedia: totalCount,
        optimizedMedia: optimizedCount,
        unoptimizedMedia: totalCount - optimizedCount,
        spaceSaved: mockMedia
          .filter(item => item.originalSize)
          .reduce((sum, item) => sum + (item.originalSize - item.size), 0) / (1024 * 1024), // En MB
        mediaTypes: {
          jpeg: mockMedia.filter(item => item.type === 'jpeg').length,
          png: mockMedia.filter(item => item.type === 'png').length,
          gif: mockMedia.filter(item => item.type === 'gif').length,
          webp: mockMedia.filter(item => item.type === 'webp').length,
          svg: mockMedia.filter(item => item.type === 'svg').length
        },
        issues: {
          uncompressed: mockMedia.filter(item => item.status === 'unoptimized').length,
          missingAlt: mockMedia.filter(item => !item.alt).length,
          oversized: mockMedia.filter(item => item.dimensions.width > 2000 || item.dimensions.height > 2000).length,
          noLazyLoading: Math.floor(totalCount * 0.4) // 40% sans lazy loading
        }
      };
      
      setMedia(mockMedia);
      setStats(mockStats);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Fonction pour optimiser une image
  const handleOptimizeImage = (id) => {
    setIsProcessing(true);
    
    // Simuler l'optimisation
    setTimeout(() => {
      const updatedMedia = media.map(item => {
        if (item.id === id) {
          const originalSize = item.originalSize || item.size;
          const newSize = Math.floor(originalSize * (Math.random() * 0.3 + 0.5)); // 50-80% de l'original
          
          return {
            ...item,
            size: newSize,
            originalSize: originalSize,
            status: 'optimized'
          };
        }
        return item;
      });
      
      // Mettre à jour les statistiques
      const optimizedItem = updatedMedia.find(item => item.id === id);
      const spaceSaved = (optimizedItem.originalSize - optimizedItem.size) / (1024 * 1024); // En MB
      
      setMedia(updatedMedia);
      setStats({
        ...stats,
        optimizedMedia: stats.optimizedMedia + 1,
        unoptimizedMedia: stats.unoptimizedMedia - 1,
        spaceSaved: stats.spaceSaved + spaceSaved,
        issues: {
          ...stats.issues,
          uncompressed: stats.issues.uncompressed - 1
        }
      });
      
      setIsProcessing(false);
      setSuccessMessage(__('Image optimisée avec succès !', 'boss-seo'));
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1500);
  };
  
  // Fonction pour générer le texte alternatif
  const handleGenerateAlt = (id) => {
    setIsProcessing(true);
    
    // Simuler la génération de texte alternatif
    setTimeout(() => {
      const updatedMedia = media.map(item => {
        if (item.id === id) {
          return {
            ...item,
            alt: `Image générée par IA montrant ${item.type === 'jpeg' ? 'une photographie' : item.type === 'png' ? 'une illustration' : item.type === 'gif' ? 'une animation' : 'un graphique'} de haute qualité.`
          };
        }
        return item;
      });
      
      // Mettre à jour les statistiques
      const hadNoAlt = !media.find(item => item.id === id).alt;
      
      setMedia(updatedMedia);
      
      if (hadNoAlt) {
        setStats({
          ...stats,
          issues: {
            ...stats.issues,
            missingAlt: stats.issues.missingAlt - 1
          }
        });
      }
      
      setIsProcessing(false);
      setSuccessMessage(__('Texte alternatif généré avec succès !', 'boss-seo'));
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1500);
  };
  
  // Fonction pour effectuer une action en masse
  const handleBulkAction = (action) => {
    setIsProcessing(true);
    
    // Simuler l'action en masse
    setTimeout(() => {
      let updatedMedia = [...media];
      let message = '';
      let optimizedCount = 0;
      let altGeneratedCount = 0;
      
      if (action === 'optimize') {
        updatedMedia = media.map(item => {
          if (item.status === 'unoptimized' && item.id % 10 < 8) { // 80% de réussite
            const originalSize = item.originalSize || item.size;
            const newSize = Math.floor(originalSize * (Math.random() * 0.3 + 0.5)); // 50-80% de l'original
            
            optimizedCount++;
            
            return {
              ...item,
              size: newSize,
              originalSize: originalSize,
              status: 'optimized'
            };
          }
          return item;
        });
        
        message = __(`${optimizedCount} images optimisées avec succès !`, 'boss-seo');
      } else if (action === 'generate-alt') {
        updatedMedia = media.map(item => {
          if (!item.alt) {
            altGeneratedCount++;
            
            return {
              ...item,
              alt: `Image générée par IA montrant ${item.type === 'jpeg' ? 'une photographie' : item.type === 'png' ? 'une illustration' : item.type === 'gif' ? 'une animation' : 'un graphique'} de haute qualité.`
            };
          }
          return item;
        });
        
        message = __(`Texte alternatif généré pour ${altGeneratedCount} images !`, 'boss-seo');
      }
      
      // Mettre à jour les statistiques
      const spaceSaved = optimizedCount * 0.5; // Estimation: 0.5 MB par image
      
      setMedia(updatedMedia);
      setStats({
        ...stats,
        optimizedMedia: stats.optimizedMedia + optimizedCount,
        unoptimizedMedia: stats.unoptimizedMedia - optimizedCount,
        spaceSaved: stats.spaceSaved + spaceSaved,
        issues: {
          ...stats.issues,
          uncompressed: stats.issues.uncompressed - optimizedCount,
          missingAlt: stats.issues.missingAlt - altGeneratedCount
        }
      });
      
      setIsProcessing(false);
      setSuccessMessage(message);
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 2000);
  };
  
  // Fonction pour enregistrer les paramètres
  const handleSaveSettings = (newSettings) => {
    setIsSaving(true);
    
    // Simuler l'enregistrement
    setTimeout(() => {
      setSettings(newSettings);
      setIsSaving(false);
      setSuccessMessage(__('Paramètres enregistrés avec succès !', 'boss-seo'));
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };
  
  // Fonction pour démarrer l'optimisation par lot
  const handleStartBulkOptimization = (options) => {
    if (!options) {
      // Annuler l'optimisation
      setIsProcessing(false);
      setProgress({
        percent: 0,
        current: 0,
        total: 0,
        currentImage: null
      });
      return;
    }
    
    setIsProcessing(true);
    
    // Filtrer les médias à optimiser
    const mediaToOptimize = media.filter(item => {
      // Vérifier le type
      const typeMatch = (
        (options.mediaTypes.jpeg && item.type === 'jpeg') ||
        (options.mediaTypes.png && item.type === 'png') ||
        (options.mediaTypes.gif && item.type === 'gif') ||
        (options.mediaTypes.webp && item.type === 'webp') ||
        (options.mediaTypes.svg && item.type === 'svg')
      );
      
      // Vérifier si on ignore les images déjà optimisées
      const statusMatch = !options.skipOptimized || item.status !== 'optimized';
      
      return typeMatch && statusMatch;
    });
    
    const totalImages = mediaToOptimize.length;
    
    if (totalImages === 0) {
      setIsProcessing(false);
      return;
    }
    
    // Initialiser la progression
    setProgress({
      percent: 0,
      current: 0,
      total: totalImages,
      currentImage: mediaToOptimize[0]
    });
    
    // Simuler l'optimisation par lot
    let currentIndex = 0;
    let optimizedCount = 0;
    let altGeneratedCount = 0;
    let spaceSaved = 0;
    
    const processNextBatch = () => {
      if (currentIndex >= totalImages) {
        // Optimisation terminée
        setIsProcessing(false);
        setProgress({
          percent: 100,
          current: totalImages,
          total: totalImages,
          currentImage: null
        });
        
        // Mettre à jour les statistiques
        setStats({
          ...stats,
          optimizedMedia: stats.optimizedMedia + optimizedCount,
          unoptimizedMedia: stats.unoptimizedMedia - optimizedCount,
          spaceSaved: stats.spaceSaved + spaceSaved,
          issues: {
            ...stats.issues,
            uncompressed: stats.issues.uncompressed - optimizedCount,
            missingAlt: stats.issues.missingAlt - altGeneratedCount
          }
        });
        
        setSuccessMessage(__(`Optimisation terminée ! ${optimizedCount} images optimisées.`, 'boss-seo'));
        setShowSuccess(true);
        
        // Masquer le message de succès après 3 secondes
        setTimeout(() => {
          setShowSuccess(false);
        }, 3000);
        
        return;
      }
      
      // Traiter le lot suivant
      const batchSize = Math.min(options.batchSize, totalImages - currentIndex);
      const batch = mediaToOptimize.slice(currentIndex, currentIndex + batchSize);
      
      // Simuler le traitement du lot
      setTimeout(() => {
        // Mettre à jour les médias
        const updatedMedia = [...media];
        
        batch.forEach(item => {
          const index = updatedMedia.findIndex(m => m.id === item.id);
          
          if (index !== -1) {
            // Optimiser l'image
            if (item.status !== 'optimized') {
              const originalSize = item.originalSize || item.size;
              const newSize = Math.floor(originalSize * (Math.random() * 0.3 + 0.5)); // 50-80% de l'original
              
              updatedMedia[index] = {
                ...updatedMedia[index],
                size: newSize,
                originalSize: originalSize,
                status: 'optimized'
              };
              
              optimizedCount++;
              spaceSaved += (originalSize - newSize) / (1024 * 1024); // En MB
            }
            
            // Générer le texte alternatif si nécessaire
            if (options.generateAltText && !updatedMedia[index].alt) {
              updatedMedia[index] = {
                ...updatedMedia[index],
                alt: `Image générée par IA montrant ${item.type === 'jpeg' ? 'une photographie' : item.type === 'png' ? 'une illustration' : item.type === 'gif' ? 'une animation' : 'un graphique'} de haute qualité.`
              };
              
              altGeneratedCount++;
            }
          }
        });
        
        setMedia(updatedMedia);
        
        // Mettre à jour la progression
        currentIndex += batchSize;
        const percent = Math.round((currentIndex / totalImages) * 100);
        
        setProgress({
          percent: percent,
          current: currentIndex,
          total: totalImages,
          currentImage: currentIndex < totalImages ? mediaToOptimize[currentIndex] : null
        });
        
        // Traiter le lot suivant
        processNextBatch();
      }, 1000);
    };
    
    // Démarrer le traitement
    processNextBatch();
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {successMessage}
            </Notice>
          )}
          
          <TabPanel
            className="boss-mb-6"
            activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
            onSelect={(tabName) => setActiveTab(tabName)}
            tabs={[
              {
                name: 'overview',
                title: __('Vue d\'ensemble', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'media-list',
                title: __('Liste des médias', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'bulk-optimization',
                title: __('Optimisation par lot', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'settings',
                title: __('Paramètres', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              }
            ]}
          >
            {(tab) => {
              if (tab.name === 'overview') {
                return (
                  <MediaOverview stats={stats} />
                );
              } else if (tab.name === 'media-list') {
                return (
                  <MediaList
                    media={media}
                    onOptimize={handleOptimizeImage}
                    onGenerateAlt={handleGenerateAlt}
                    onBulkAction={handleBulkAction}
                    isProcessing={isProcessing}
                  />
                );
              } else if (tab.name === 'bulk-optimization') {
                return (
                  <BulkOptimization
                    stats={stats}
                    onStartBulkOptimization={handleStartBulkOptimization}
                    isProcessing={isProcessing}
                    progress={progress}
                  />
                );
              } else if (tab.name === 'settings') {
                return (
                  <MediaSettings
                    settings={settings}
                    setSettings={setSettings}
                    onSave={handleSaveSettings}
                    isSaving={isSaving}
                  />
                );
              }
            }}
          </TabPanel>
        </div>
      )}
    </div>
  );
};

export default MediaOptimization;
