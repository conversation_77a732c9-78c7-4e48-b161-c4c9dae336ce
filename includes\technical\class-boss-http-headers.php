<?php
/**
 * Classe pour la gestion des en-têtes HTTP.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 */

/**
 * Classe pour la gestion des en-têtes HTTP.
 *
 * Cette classe gère les en-têtes HTTP pour le plugin Boss SEO.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Http_Headers {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le nom de l'option pour les en-têtes HTTP.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $headers_option    Le nom de l'option pour les en-têtes HTTP.
     */
    protected $headers_option;

    /**
     * Le nom de l'option pour les paramètres de préconnexion.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $preconnect_option    Le nom de l'option pour les paramètres de préconnexion.
     */
    protected $preconnect_option;

    /**
     * Le nom de l'option pour les paramètres de cache.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $cache_option    Le nom de l'option pour les paramètres de cache.
     */
    protected $cache_option;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->headers_option = $plugin_name . '_http_headers';
        $this->preconnect_option = $plugin_name . '_preconnect_settings';
        $this->cache_option = $plugin_name . '_cache_settings';
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Hook pour ajouter les en-têtes HTTP
        add_action( 'send_headers', array( $this, 'add_http_headers' ) );

        // Hook pour ajouter les balises de préconnexion
        add_action( 'wp_head', array( $this, 'add_preconnect_tags' ), 1 );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/http-headers',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_headers' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_headers' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/http-headers/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_header' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'update_header' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_header' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/http-headers/preconnect',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_preconnect_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_preconnect_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/http-headers/cache',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_cache_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_cache_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/http-headers/test',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'test_headers' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.1.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Ajoute les en-têtes HTTP.
     *
     * @since    1.1.0
     */
    public function add_http_headers() {
        // Récupérer les en-têtes HTTP
        $headers = get_option( $this->headers_option, array() );

        // Ajouter les en-têtes HTTP
        foreach ( $headers as $header ) {
            // Vérifier si l'en-tête est actif
            if ( isset( $header['active'] ) && $header['active'] ) {
                // Ajouter l'en-tête
                header( $header['name'] . ': ' . $header['value'] );
            }
        }

        // Ajouter les en-têtes de cache
        $cache_settings = get_option( $this->cache_option, array() );

        if ( isset( $cache_settings['enabled'] ) && $cache_settings['enabled'] ) {
            // Ajouter les en-têtes de cache
            $cache_control = array();

            if ( isset( $cache_settings['maxAge'] ) && $cache_settings['maxAge'] > 0 ) {
                $cache_control[] = 'max-age=' . intval( $cache_settings['maxAge'] );
            }

            if ( isset( $cache_settings['public'] ) && $cache_settings['public'] ) {
                $cache_control[] = 'public';
            } elseif ( isset( $cache_settings['private'] ) && $cache_settings['private'] ) {
                $cache_control[] = 'private';
            }

            if ( isset( $cache_settings['noCache'] ) && $cache_settings['noCache'] ) {
                $cache_control[] = 'no-cache';
            }

            if ( isset( $cache_settings['noStore'] ) && $cache_settings['noStore'] ) {
                $cache_control[] = 'no-store';
            }

            if ( isset( $cache_settings['mustRevalidate'] ) && $cache_settings['mustRevalidate'] ) {
                $cache_control[] = 'must-revalidate';
            }

            if ( ! empty( $cache_control ) ) {
                header( 'Cache-Control: ' . implode( ', ', $cache_control ) );
            }

            // Ajouter l'en-tête Expires
            if ( isset( $cache_settings['expires'] ) && $cache_settings['expires'] > 0 ) {
                $expires = gmdate( 'D, d M Y H:i:s', time() + intval( $cache_settings['expires'] ) ) . ' GMT';
                header( 'Expires: ' . $expires );
            }
        }
    }

    /**
     * Ajoute les balises de préconnexion.
     *
     * @since    1.1.0
     */
    public function add_preconnect_tags() {
        // Récupérer les paramètres de préconnexion
        $preconnect_settings = get_option( $this->preconnect_option, array() );

        // Vérifier si la préconnexion est activée
        if ( ! isset( $preconnect_settings['enabled'] ) || ! $preconnect_settings['enabled'] ) {
            return;
        }

        // Ajouter les balises de préconnexion
        if ( isset( $preconnect_settings['domains'] ) && is_array( $preconnect_settings['domains'] ) ) {
            foreach ( $preconnect_settings['domains'] as $domain ) {
                if ( ! empty( $domain ) ) {
                    echo '<link rel="preconnect" href="' . esc_url( $domain ) . '" crossorigin>' . "\n";
                    echo '<link rel="dns-prefetch" href="' . esc_url( $domain ) . '">' . "\n";
                }
            }
        }
    }

    /**
     * Récupère la liste des en-têtes HTTP.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_headers( $request ) {
        $headers = get_option( $this->headers_option, array() );

        // Si les en-têtes sont vides, utiliser les en-têtes par défaut
        if ( empty( $headers ) ) {
            $headers = $this->get_default_headers();
        }

        return rest_ensure_response( $headers );
    }

    /**
     * Récupère un en-tête HTTP spécifique.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_header( $request ) {
        $id = intval( $request['id'] );
        $headers = get_option( $this->headers_option, array() );

        // Rechercher l'en-tête par ID
        foreach ( $headers as $header ) {
            if ( isset( $header['id'] ) && $header['id'] === $id ) {
                return rest_ensure_response( $header );
            }
        }

        return new WP_Error(
            'boss_seo_header_not_found',
            __( 'En-tête non trouvé.', 'boss-seo' ),
            array( 'status' => 404 )
        );
    }

    /**
     * Enregistre les en-têtes HTTP.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function save_headers( $request ) {
        $headers = isset( $request['headers'] ) ? $request['headers'] : array();

        // Valider et sanitiser les en-têtes
        $sanitized_headers = array();
        foreach ( $headers as $header ) {
            if ( isset( $header['name'] ) && isset( $header['value'] ) ) {
                $sanitized_header = array(
                    'id' => isset( $header['id'] ) ? intval( $header['id'] ) : time() + rand( 1, 1000 ),
                    'name' => sanitize_text_field( $header['name'] ),
                    'value' => sanitize_text_field( $header['value'] ),
                    'active' => isset( $header['active'] ) ? (bool) $header['active'] : true,
                    'description' => isset( $header['description'] ) ? sanitize_textarea_field( $header['description'] ) : '',
                );
                $sanitized_headers[] = $sanitized_header;
            }
        }

        // Enregistrer les en-têtes
        update_option( $this->headers_option, $sanitized_headers );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Les en-têtes HTTP ont été enregistrés avec succès.', 'boss-seo' ),
            'headers' => $sanitized_headers,
        ) );
    }

    /**
     * Met à jour un en-tête HTTP.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function update_header( $request ) {
        $id = intval( $request['id'] );
        $headers = get_option( $this->headers_option, array() );
        $header_data = $request->get_json_params();

        // Valider les données
        if ( ! isset( $header_data['name'] ) || ! isset( $header_data['value'] ) ) {
            return new WP_Error(
                'boss_seo_invalid_header',
                __( 'Les champs nom et valeur sont obligatoires.', 'boss-seo' ),
                array( 'status' => 400 )
            );
        }

        // Rechercher l'en-tête par ID
        $found = false;
        foreach ( $headers as $key => $header ) {
            if ( isset( $header['id'] ) && $header['id'] === $id ) {
                // Mettre à jour l'en-tête
                $headers[ $key ] = array(
                    'id' => $id,
                    'name' => sanitize_text_field( $header_data['name'] ),
                    'value' => sanitize_text_field( $header_data['value'] ),
                    'active' => isset( $header_data['active'] ) ? (bool) $header_data['active'] : true,
                    'description' => isset( $header_data['description'] ) ? sanitize_textarea_field( $header_data['description'] ) : '',
                );
                $found = true;
                break;
            }
        }

        if ( ! $found ) {
            return new WP_Error(
                'boss_seo_header_not_found',
                __( 'En-tête non trouvé.', 'boss-seo' ),
                array( 'status' => 404 )
            );
        }

        // Enregistrer les en-têtes
        update_option( $this->headers_option, $headers );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'L\'en-tête HTTP a été mis à jour avec succès.', 'boss-seo' ),
            'header' => $headers[ $key ],
        ) );
    }

    /**
     * Supprime un en-tête HTTP.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function delete_header( $request ) {
        $id = intval( $request['id'] );
        $headers = get_option( $this->headers_option, array() );

        // Rechercher l'en-tête par ID
        $found = false;
        foreach ( $headers as $key => $header ) {
            if ( isset( $header['id'] ) && $header['id'] === $id ) {
                // Supprimer l'en-tête
                unset( $headers[ $key ] );
                $found = true;
                break;
            }
        }

        if ( ! $found ) {
            return new WP_Error(
                'boss_seo_header_not_found',
                __( 'En-tête non trouvé.', 'boss-seo' ),
                array( 'status' => 404 )
            );
        }

        // Réindexer le tableau
        $headers = array_values( $headers );

        // Enregistrer les en-têtes
        update_option( $this->headers_option, $headers );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'L\'en-tête HTTP a été supprimé avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les paramètres de préconnexion.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_preconnect_settings( $request ) {
        $settings = get_option( $this->preconnect_option, array() );

        // Si les paramètres sont vides, utiliser les paramètres par défaut
        if ( empty( $settings ) ) {
            $settings = $this->get_default_preconnect_settings();
        }

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres de préconnexion.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function save_preconnect_settings( $request ) {
        $settings = isset( $request['settings'] ) ? $request['settings'] : array();

        // Valider et sanitiser les paramètres
        $sanitized_settings = array(
            'enabled' => isset( $settings['enabled'] ) ? (bool) $settings['enabled'] : true,
            'domains' => array(),
        );

        if ( isset( $settings['domains'] ) && is_array( $settings['domains'] ) ) {
            foreach ( $settings['domains'] as $domain ) {
                if ( ! empty( $domain ) ) {
                    $sanitized_settings['domains'][] = esc_url_raw( $domain );
                }
            }
        }

        // Enregistrer les paramètres
        update_option( $this->preconnect_option, $sanitized_settings );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Les paramètres de préconnexion ont été enregistrés avec succès.', 'boss-seo' ),
            'settings' => $sanitized_settings,
        ) );
    }

    /**
     * Récupère les paramètres de cache.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function get_cache_settings( $request ) {
        $settings = get_option( $this->cache_option, array() );

        // Si les paramètres sont vides, utiliser les paramètres par défaut
        if ( empty( $settings ) ) {
            $settings = $this->get_default_cache_settings();
        }

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres de cache.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function save_cache_settings( $request ) {
        $settings = isset( $request['settings'] ) ? $request['settings'] : array();

        // Valider et sanitiser les paramètres
        $sanitized_settings = array(
            'enabled' => isset( $settings['enabled'] ) ? (bool) $settings['enabled'] : true,
            'maxAge' => isset( $settings['maxAge'] ) ? intval( $settings['maxAge'] ) : 3600,
            'public' => isset( $settings['public'] ) ? (bool) $settings['public'] : true,
            'private' => isset( $settings['private'] ) ? (bool) $settings['private'] : false,
            'noCache' => isset( $settings['noCache'] ) ? (bool) $settings['noCache'] : false,
            'noStore' => isset( $settings['noStore'] ) ? (bool) $settings['noStore'] : false,
            'mustRevalidate' => isset( $settings['mustRevalidate'] ) ? (bool) $settings['mustRevalidate'] : false,
            'expires' => isset( $settings['expires'] ) ? intval( $settings['expires'] ) : 3600,
        );

        // Enregistrer les paramètres
        update_option( $this->cache_option, $sanitized_settings );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Les paramètres de cache ont été enregistrés avec succès.', 'boss-seo' ),
            'settings' => $sanitized_settings,
        ) );
    }

    /**
     * Teste les en-têtes HTTP.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse de l'API.
     */
    public function test_headers( $request ) {
        $url = isset( $request['url'] ) ? esc_url_raw( $request['url'] ) : home_url();

        // Tester les en-têtes HTTP
        $response = wp_remote_head( $url, array( 'timeout' => 5 ) );

        if ( is_wp_error( $response ) ) {
            return new WP_Error(
                'boss_seo_test_failed',
                $response->get_error_message(),
                array( 'status' => 500 )
            );
        }

        // Récupérer les en-têtes
        $headers = wp_remote_retrieve_headers( $response );
        $status_code = wp_remote_retrieve_response_code( $response );

        // Préparer les données
        $data = array(
            'url' => $url,
            'status_code' => $status_code,
            'headers' => array(),
        );

        // Convertir les en-têtes en tableau
        foreach ( $headers as $name => $value ) {
            $data['headers'][] = array(
                'name' => $name,
                'value' => $value,
            );
        }

        return rest_ensure_response( $data );
    }

    /**
     * Récupère les en-têtes HTTP par défaut.
     *
     * @since    1.1.0
     * @return   array    Les en-têtes HTTP par défaut.
     */
    private function get_default_headers() {
        return array(
            array(
                'id' => 1,
                'name' => 'X-Frame-Options',
                'value' => 'SAMEORIGIN',
                'active' => true,
                'description' => __( 'Empêche le site d\'être affiché dans un iframe sur un autre domaine.', 'boss-seo' ),
            ),
            array(
                'id' => 2,
                'name' => 'X-Content-Type-Options',
                'value' => 'nosniff',
                'active' => true,
                'description' => __( 'Empêche les navigateurs de deviner le type MIME des ressources.', 'boss-seo' ),
            ),
            array(
                'id' => 3,
                'name' => 'X-XSS-Protection',
                'value' => '1; mode=block',
                'active' => true,
                'description' => __( 'Active la protection XSS dans les navigateurs.', 'boss-seo' ),
            ),
            array(
                'id' => 4,
                'name' => 'Referrer-Policy',
                'value' => 'strict-origin-when-cross-origin',
                'active' => true,
                'description' => __( 'Contrôle les informations de référence envoyées lors de la navigation.', 'boss-seo' ),
            ),
            array(
                'id' => 5,
                'name' => 'Strict-Transport-Security',
                'value' => 'max-age=31536000; includeSubDomains',
                'active' => false,
                'description' => __( 'Force les connexions HTTPS (activer uniquement si le site est en HTTPS).', 'boss-seo' ),
            ),
        );
    }

    /**
     * Récupère les paramètres de préconnexion par défaut.
     *
     * @since    1.1.0
     * @return   array    Les paramètres de préconnexion par défaut.
     */
    private function get_default_preconnect_settings() {
        return array(
            'enabled' => true,
            'domains' => array(
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com',
                'https://ajax.googleapis.com',
            ),
        );
    }

    /**
     * Récupère les paramètres de cache par défaut.
     *
     * @since    1.1.0
     * @return   array    Les paramètres de cache par défaut.
     */
    private function get_default_cache_settings() {
        return array(
            'enabled' => true,
            'maxAge' => 3600,
            'public' => true,
            'private' => false,
            'noCache' => false,
            'noStore' => false,
            'mustRevalidate' => false,
            'expires' => 3600,
        );
    }
}
