<?php
/**
 * Tests pour l'API E-commerce unifiée.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/tests
 */

/**
 * Classe de test pour l'API E-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/tests
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce_API_Test {

    /**
     * Instance de l'API à tester.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Ecommerce_API    $api    Instance de l'API.
     */
    private $api;

    /**
     * Initialise les tests.
     *
     * @since    1.2.0
     */
    public function __construct() {
        $this->setup_test_environment();
    }

    /**
     * Configure l'environnement de test.
     *
     * @since    1.2.0
     */
    private function setup_test_environment() {
        // Charger les dépendances
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-ecommerce-api.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-woocommerce-manager.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/ecommerce/class-boss-ecommerce-cache.php';

        // Créer des mocks pour les dépendances
        $settings = $this->create_mock_settings();
        $ai = $this->create_mock_ai();

        // Initialiser l'API
        $this->api = new Boss_Ecommerce_API( 'boss-seo', '1.2.0', $ai, $settings );
    }

    /**
     * Crée un mock pour les paramètres.
     *
     * @since    1.2.0
     * @return   object    Mock des paramètres.
     */
    private function create_mock_settings() {
        return new class {
            public function get_option( $key, $default = null ) {
                return $default;
            }
        };
    }

    /**
     * Crée un mock pour l'IA.
     *
     * @since    1.2.0
     * @return   object    Mock de l'IA.
     */
    private function create_mock_ai() {
        return new class {
            public function is_available() {
                return true;
            }

            public function generate_content( $prompt, $options = array() ) {
                return array(
                    'success' => true,
                    'content' => '{"title": "Titre généré", "meta_description": "Description générée", "description": "Description longue générée"}',
                    'provider' => 'mock',
                    'model' => 'test'
                );
            }
        };
    }

    /**
     * Teste l'enregistrement des routes API.
     *
     * @since    1.2.0
     */
    public function test_register_rest_routes() {
        echo "🧪 Test: Enregistrement des routes API\n";

        // Simuler l'enregistrement des routes
        $this->api->register_rest_routes();

        // Vérifier que les routes principales sont enregistrées
        $routes = rest_get_server()->get_routes();
        $expected_routes = array(
            '/boss-seo/v1/ecommerce/dashboard',
            '/boss-seo/v1/ecommerce/dashboard/stats',
            '/boss-seo/v1/ecommerce/dashboard/top-products',
            '/boss-seo/v1/ecommerce/dashboard/top-categories',
            '/boss-seo/v1/ecommerce/products',
            '/boss-seo/v1/ecommerce/optimize-product',
            '/boss-seo/v1/ecommerce/generate-description'
        );

        $routes_found = 0;
        foreach ( $expected_routes as $expected_route ) {
            if ( isset( $routes[ $expected_route ] ) ) {
                $routes_found++;
                echo "  ✅ Route trouvée: {$expected_route}\n";
            } else {
                echo "  ❌ Route manquante: {$expected_route}\n";
            }
        }

        if ( $routes_found === count( $expected_routes ) ) {
            echo "  ✅ Toutes les routes sont enregistrées\n";
            return true;
        } else {
            echo "  ❌ {$routes_found}/" . count( $expected_routes ) . " routes trouvées\n";
            return false;
        }
    }

    /**
     * Teste la vérification des permissions.
     *
     * @since    1.2.0
     */
    public function test_check_permissions() {
        echo "🧪 Test: Vérification des permissions\n";

        // Simuler un utilisateur avec permissions
        wp_set_current_user( 1 ); // Admin
        $has_permissions = $this->api->check_permissions();

        if ( $has_permissions ) {
            echo "  ✅ Permissions admin détectées correctement\n";
        } else {
            echo "  ❌ Permissions admin non détectées\n";
            return false;
        }

        // Simuler un utilisateur sans permissions
        wp_set_current_user( 0 ); // Aucun utilisateur
        $has_permissions = $this->api->check_permissions();

        if ( ! $has_permissions ) {
            echo "  ✅ Absence de permissions détectée correctement\n";
            return true;
        } else {
            echo "  ❌ Permissions accordées à tort\n";
            return false;
        }
    }

    /**
     * Teste la gestion d'erreurs WooCommerce.
     *
     * @since    1.2.0
     */
    public function test_woocommerce_error_handling() {
        echo "🧪 Test: Gestion d'erreurs WooCommerce\n";

        // Simuler l'absence de WooCommerce
        if ( ! class_exists( 'WooCommerce' ) ) {
            $request = new WP_REST_Request( 'GET', '/boss-seo/v1/ecommerce/dashboard' );
            $response = $this->api->get_dashboard_data( $request );

            if ( is_wp_error( $response ) && $response->get_error_code() === 'woocommerce_not_available' ) {
                echo "  ✅ Erreur WooCommerce non disponible gérée correctement\n";
                return true;
            } else {
                echo "  ❌ Erreur WooCommerce non gérée correctement\n";
                return false;
            }
        } else {
            echo "  ⚠️  WooCommerce est disponible, test ignoré\n";
            return true;
        }
    }

    /**
     * Teste la mise en cache.
     *
     * @since    1.2.0
     */
    public function test_caching() {
        echo "🧪 Test: Système de cache\n";

        $cache = Boss_Ecommerce_Cache::get_instance();

        // Tester la mise en cache
        $test_data = array( 'test' => 'data', 'timestamp' => time() );
        $cache_result = $cache->set( 'test_key', $test_data, 300 );

        if ( $cache_result ) {
            echo "  ✅ Données mises en cache avec succès\n";
        } else {
            echo "  ❌ Échec de la mise en cache\n";
            return false;
        }

        // Tester la récupération du cache
        $cached_data = $cache->get( 'test_key' );

        if ( $cached_data && $cached_data['test'] === 'data' ) {
            echo "  ✅ Données récupérées du cache avec succès\n";
        } else {
            echo "  ❌ Échec de la récupération du cache\n";
            return false;
        }

        // Tester la suppression du cache
        $delete_result = $cache->delete( 'test_key' );

        if ( $delete_result ) {
            echo "  ✅ Données supprimées du cache avec succès\n";
            return true;
        } else {
            echo "  ❌ Échec de la suppression du cache\n";
            return false;
        }
    }

    /**
     * Exécute tous les tests.
     *
     * @since    1.2.0
     */
    public function run_all_tests() {
        echo "🚀 Démarrage des tests de l'API E-commerce\n";
        echo "==========================================\n\n";

        $tests = array(
            'test_register_rest_routes',
            'test_check_permissions',
            'test_woocommerce_error_handling',
            'test_caching'
        );

        $passed = 0;
        $total = count( $tests );

        foreach ( $tests as $test ) {
            if ( $this->$test() ) {
                $passed++;
            }
            echo "\n";
        }

        echo "==========================================\n";
        echo "📊 Résultats: {$passed}/{$total} tests réussis\n";

        if ( $passed === $total ) {
            echo "🎉 Tous les tests sont passés avec succès!\n";
        } else {
            echo "⚠️  Certains tests ont échoué. Vérifiez les détails ci-dessus.\n";
        }

        return $passed === $total;
    }
}

// Exécuter les tests si ce fichier est appelé directement
if ( defined( 'WP_CLI' ) && WP_CLI ) {
    $test = new Boss_Ecommerce_API_Test();
    $test->run_all_tests();
}
