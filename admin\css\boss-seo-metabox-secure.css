/**
 * Styles sécurisés et optimisés pour la Meta Box Boss SEO
 * Version responsive avec accessibilité améliorée
 */

/* Variables CSS pour la cohérence */
:root {
    --boss-seo-primary: #0073aa;
    --boss-seo-primary-hover: #006291;
    --boss-seo-success: #46b450;
    --boss-seo-warning: #ffb900;
    --boss-seo-error: #dc3232;
    --boss-seo-info: #72aee6;
    --boss-seo-border: #ddd;
    --boss-seo-border-focus: #0073aa;
    --boss-seo-bg-light: #f9f9f9;
    --boss-seo-bg-white: #ffffff;
    --boss-seo-text: #23282d;
    --boss-seo-text-light: #666;
    --boss-seo-radius: 4px;
    --boss-seo-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --boss-seo-transition: all 0.2s ease;
}

/* Support du mode sombre */
@media (prefers-color-scheme: dark) {
    :root {
        --boss-seo-bg-light: #1e1e1e;
        --boss-seo-bg-white: #2c2c2c;
        --boss-seo-text: #ffffff;
        --boss-seo-text-light: #cccccc;
        --boss-seo-border: #444;
    }
}

/* Container principal */
.boss-seo-metabox-secure {
    padding: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.5;
}

/* Sections */
.boss-seo-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--boss-seo-border);
    position: relative;
}

.boss-seo-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.boss-seo-section h4 {
    margin: 0 0 16px 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--boss-seo-text);
    display: flex;
    align-items: center;
    gap: 8px;
}

.boss-seo-section h4 .dashicons {
    color: var(--boss-seo-primary);
    font-size: 18px;
}

/* Groupes de champs */
.boss-seo-field-group {
    margin-bottom: 16px;
}

.boss-seo-field-group:last-child {
    margin-bottom: 0;
}

.boss-seo-field-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    font-size: 13px;
    color: var(--boss-seo-text);
}

/* Champs de saisie */
.boss-seo-input,
.boss-seo-textarea,
.boss-seo-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    font-size: 14px;
    background: var(--boss-seo-bg-white);
    color: var(--boss-seo-text);
    transition: var(--boss-seo-transition);
    box-sizing: border-box;
}

.boss-seo-input:focus,
.boss-seo-textarea:focus,
.boss-seo-select:focus {
    border-color: var(--boss-seo-border-focus);
    box-shadow: 0 0 0 1px var(--boss-seo-border-focus);
    outline: none;
}

.boss-seo-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Grid responsive pour les champs */
.boss-seo-field-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

@media (max-width: 768px) {
    .boss-seo-field-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}

/* Compteurs de caractères */
.boss-seo-counter {
    position: absolute;
    right: 8px;
    bottom: 8px;
    font-size: 11px;
    color: var(--boss-seo-text-light);
    background: var(--boss-seo-bg-white);
    padding: 2px 6px;
    border-radius: var(--boss-seo-radius);
    box-shadow: var(--boss-seo-shadow);
    pointer-events: none;
    z-index: 10;
}

.boss-seo-field-group {
    position: relative;
}

.boss-seo-counter-current.too-short,
.boss-seo-counter-current.too-long {
    color: var(--boss-seo-error);
    font-weight: 600;
}

.boss-seo-counter-current.good {
    color: var(--boss-seo-success);
    font-weight: 600;
}

/* Textes d'aide */
.boss-seo-help {
    font-size: 12px;
    color: var(--boss-seo-text-light);
    margin-top: 4px;
    line-height: 1.4;
}

/* Gestion des mots-clés */
.boss-seo-keywords-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 12px 0;
    min-height: 40px;
    padding: 8px;
    border: 1px dashed var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    background: var(--boss-seo-bg-light);
}

.boss-seo-keyword-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: var(--boss-seo-bg-white);
    border: 1px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    padding: 6px 10px;
    font-size: 13px;
    color: var(--boss-seo-text);
    cursor: pointer;
    transition: var(--boss-seo-transition);
    user-select: none;
}

.boss-seo-keyword-tag:hover {
    border-color: var(--boss-seo-primary);
    box-shadow: var(--boss-seo-shadow);
}

.boss-seo-keyword-tag.primary {
    background: var(--boss-seo-success);
    border-color: var(--boss-seo-success);
    color: white;
    font-weight: 600;
}

.boss-seo-keyword-tag .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.boss-seo-keyword-tag:not(.primary) .dashicons:hover {
    color: var(--boss-seo-error);
}

/* Suggestions de mots-clés */
.boss-seo-suggestions {
    margin-top: 16px;
    padding: 12px;
    background: var(--boss-seo-bg-light);
    border-radius: var(--boss-seo-radius);
}

.boss-seo-suggestions-title {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--boss-seo-text);
}

.boss-seo-suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.boss-seo-suggestion {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: var(--boss-seo-bg-white);
    border: 1px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    padding: 4px 8px;
    font-size: 12px;
    color: var(--boss-seo-text);
    cursor: pointer;
    transition: var(--boss-seo-transition);
}

.boss-seo-suggestion:hover {
    background: var(--boss-seo-primary);
    border-color: var(--boss-seo-primary);
    color: white;
}

.boss-seo-suggestion .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Score SEO */
.boss-seo-score-container {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.boss-seo-score-indicator {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.boss-seo-score-indicator::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent 0%, var(--boss-seo-primary) 100%);
    opacity: 0.1;
}

.boss-seo-score-good {
    background: var(--boss-seo-success);
}

.boss-seo-score-ok {
    background: var(--boss-seo-warning);
}

.boss-seo-score-poor {
    background: #f56e28;
}

.boss-seo-score-bad {
    background: var(--boss-seo-error);
}

.boss-seo-score-none {
    background: var(--boss-seo-text-light);
}

.boss-seo-score-details {
    flex: 1;
    min-width: 200px;
}

.boss-seo-score-details p {
    margin: 0 0 8px 0;
    font-size: 13px;
}

/* Actions et boutons */
.boss-seo-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
}

.boss-seo-actions .button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: var(--boss-seo-transition);
}

.boss-seo-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Recommandations */
.boss-seo-recommendations {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--boss-seo-border);
    border-radius: var(--boss-seo-radius);
    background: var(--boss-seo-bg-white);
}

.boss-seo-recommendation {
    padding: 12px;
    border-bottom: 1px solid var(--boss-seo-border);
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.boss-seo-recommendation:last-child {
    border-bottom: none;
}

.boss-seo-recommendation-critical {
    background: #fef8f6;
    border-left: 4px solid var(--boss-seo-error);
}

.boss-seo-recommendation-warning {
    background: #fff8e5;
    border-left: 4px solid var(--boss-seo-warning);
}

.boss-seo-recommendation-info {
    background: #f0f6fc;
    border-left: 4px solid var(--boss-seo-info);
}

.boss-seo-recommendation-icon {
    font-size: 18px;
    flex-shrink: 0;
    margin-top: 2px;
}

.boss-seo-recommendation-critical .boss-seo-recommendation-icon {
    color: var(--boss-seo-error);
}

.boss-seo-recommendation-warning .boss-seo-recommendation-icon {
    color: var(--boss-seo-warning);
}

.boss-seo-recommendation-info .boss-seo-recommendation-icon {
    color: var(--boss-seo-info);
}

.boss-seo-recommendation-text {
    flex: 1;
    font-size: 13px;
    line-height: 1.4;
}

/* États de chargement */
.boss-seo-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.boss-seo-loading::after {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--boss-seo-radius);
}

.boss-seo-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-left-color: var(--boss-seo-primary);
    border-radius: 50%;
    animation: boss-seo-spin 1s linear infinite;
}

@keyframes boss-seo-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .boss-seo-metabox-secure {
        padding: 16px 0;
    }
    
    .boss-seo-section {
        margin-bottom: 20px;
        padding-bottom: 16px;
    }
    
    .boss-seo-score-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .boss-seo-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .boss-seo-actions .button {
        justify-content: center;
    }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus visible pour l'accessibilité */
.boss-seo-input:focus-visible,
.boss-seo-textarea:focus-visible,
.boss-seo-select:focus-visible,
.boss-seo-suggestion:focus-visible,
.boss-seo-keyword-tag:focus-visible {
    outline: 2px solid var(--boss-seo-primary);
    outline-offset: 2px;
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
    :root {
        --boss-seo-border: #000;
        --boss-seo-text-light: #333;
    }
}

/* Masquer les éléments pour les lecteurs d'écran uniquement */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    color: #21759b;
    display: block;
    font-size: 14px;
    font-weight: bold;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
}
