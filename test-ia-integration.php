<?php
/**
 * Test de l'intégration IA Boss SEO
 * 
 * Ce fichier teste l'intégration avec les services IA
 * et vérifie que toutes les fonctionnalités utilisent bien l'IA.
 */

// Simuler l'environnement WordPress pour les tests
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

echo "<!DOCTYPE html><html><head><title>🤖 Test Intégration IA Boss SEO</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🤖 Test de l'Intégration IA Boss SEO</h1>";

/**
 * Classe de test pour l'intégration IA
 */
class Boss_SEO_AI_Integration_Test {
    
    private $settings;
    private $ai_service;
    
    public function __construct() {
        $this->load_dependencies();
    }
    
    /**
     * Charge les dépendances nécessaires
     */
    private function load_dependencies() {
        // Simuler les classes WordPress nécessaires
        $this->mock_wordpress_functions();
        
        try {
            // Charger les classes Boss SEO
            require_once 'includes/class-boss-optimizer-settings.php';
            require_once 'includes/class-boss-optimizer-ai.php';
            require_once 'includes/class-boss-seo-analyzer.php';
            require_once 'includes/class-boss-seo-optimizer.php';
            
            echo "<p class='success'>✅ Classes Boss SEO chargées avec succès</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur lors du chargement des classes : " . $e->getMessage() . "</p>";
            return false;
        }
        
        return true;
    }
    
    /**
     * Exécute tous les tests d'intégration IA
     */
    public function run_all_tests() {
        echo "<h2>🔧 Tests de Configuration IA</h2>";
        $this->test_ai_configuration();
        
        echo "<h2>🏷️ Tests de Génération de Mots-clés IA</h2>";
        $this->test_ai_keyword_generation();
        
        echo "<h2>⚡ Tests d'Optimisation IA</h2>";
        $this->test_ai_optimization();
        
        echo "<h2>🔍 Tests d'Analyse avec IA</h2>";
        $this->test_ai_analysis();
        
        echo "<h2>📊 Résumé des Tests</h2>";
        $this->display_summary();
    }
    
    /**
     * Test de la configuration IA
     */
    private function test_ai_configuration() {
        try {
            $this->settings = new Boss_Optimizer_Settings('boss-seo');
            
            echo "<h3>Configuration des Fournisseurs IA</h3>";
            
            // Test des fournisseurs disponibles
            $providers = array('openai', 'anthropic', 'gemini');
            foreach ($providers as $provider) {
                echo "<p class='info'>🔧 Fournisseur {$provider} : ";
                
                // Simuler une configuration
                $this->mock_ai_settings($provider);
                
                if ($this->settings->is_ai_configured()) {
                    echo "<span class='success'>Configuré ✅</span></p>";
                } else {
                    echo "<span class='warning'>Non configuré ⚠️</span></p>";
                }
            }
            
            // Test du service IA
            $this->ai_service = new Boss_Optimizer_AI($this->settings);
            echo "<p class='success'>✅ Service IA initialisé</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur de configuration IA : " . $e->getMessage() . "</p>";
        }
    }
    
    /**
     * Test de génération de mots-clés avec IA
     */
    private function test_ai_keyword_generation() {
        try {
            $analyzer = new Boss_SEO_Analyzer();
            
            echo "<h3>Génération de Mots-clés Intelligents</h3>";
            
            // Test avec contenu simulé
            $mock_content = "Ceci est un guide complet sur l'optimisation SEO pour WordPress. Nous allons voir comment améliorer le référencement de votre site web avec des techniques avancées.";
            
            echo "<p class='info'>📝 Contenu de test : " . substr($mock_content, 0, 100) . "...</p>";
            
            // Simuler un post ID
            $mock_post_id = 123;
            $this->mock_post_data($mock_post_id, 'Guide SEO WordPress', $mock_content);
            
            // Tester la génération de suggestions
            $suggestions = $analyzer->generate_smart_suggestions($mock_post_id, $mock_content);
            
            if (!empty($suggestions)) {
                echo "<p class='success'>✅ Suggestions générées : " . count($suggestions) . "</p>";
                echo "<ul>";
                foreach (array_slice($suggestions, 0, 5) as $suggestion) {
                    echo "<li>🏷️ {$suggestion}</li>";
                }
                echo "</ul>";
                
                // Vérifier si c'est de l'IA ou du fallback
                if ($this->settings->is_ai_configured()) {
                    echo "<p class='success'>🤖 Généré avec IA</p>";
                } else {
                    echo "<p class='warning'>⚠️ Généré avec algorithme de fallback</p>";
                }
            } else {
                echo "<p class='error'>❌ Aucune suggestion générée</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur lors de la génération de mots-clés : " . $e->getMessage() . "</p>";
        }
    }
    
    /**
     * Test d'optimisation avec IA
     */
    private function test_ai_optimization() {
        try {
            $optimizer = new Boss_SEO_Optimizer();
            
            echo "<h3>Optimisation de Contenu avec IA</h3>";
            
            $mock_post_id = 456;
            $mock_content = "WordPress est un système de gestion de contenu très populaire. Ce guide vous explique comment l'optimiser pour le SEO.";
            $keywords = array('wordpress seo', 'optimisation', 'référencement');
            
            echo "<p class='info'>🎯 Mots-clés : " . implode(', ', $keywords) . "</p>";
            
            // Simuler les données du post
            $this->mock_post_data($mock_post_id, 'Guide WordPress', $mock_content);
            
            // Tester l'optimisation
            $optimization_result = $optimizer->perform_content_optimization($mock_post_id, $mock_content, $keywords);
            
            if (!empty($optimization_result['optimizations'])) {
                echo "<p class='success'>✅ Optimisations générées</p>";
                
                foreach ($optimization_result['optimizations'] as $type => $optimization) {
                    if (is_string($optimization)) {
                        echo "<p class='info'>📝 {$type} : {$optimization}</p>";
                    }
                }
                
                // Vérifier si c'est de l'IA
                if ($this->settings->is_ai_configured() && $this->settings->get('ai', 'use_ai_for_titles', true)) {
                    echo "<p class='success'>🤖 Optimisé avec IA</p>";
                } else {
                    echo "<p class='warning'>⚠️ Optimisé avec templates de fallback</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ Aucune optimisation nécessaire</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur lors de l'optimisation : " . $e->getMessage() . "</p>";
        }
    }
    
    /**
     * Test d'analyse avec IA
     */
    private function test_ai_analysis() {
        try {
            $analyzer = new Boss_SEO_Analyzer();
            
            echo "<h3>Analyse SEO Intelligente</h3>";
            
            $mock_post_id = 789;
            $mock_content = "Ce guide détaillé sur le SEO WordPress vous apprendra toutes les techniques d'optimisation. Découvrez comment améliorer votre référencement naturel avec des stratégies éprouvées.";
            
            // Simuler les métadonnées
            $this->mock_post_meta($mock_post_id, array(
                '_boss_seo_title' => 'Guide SEO WordPress Complet',
                '_boss_seo_meta_description' => 'Découvrez toutes les techniques SEO pour WordPress',
                '_boss_seo_focus_keyword' => 'seo wordpress'
            ));
            
            // Tester l'analyse
            $analysis_result = $analyzer->perform_content_analysis($mock_post_id, $mock_content);
            
            if (isset($analysis_result['score'])) {
                echo "<p class='success'>✅ Analyse terminée - Score : {$analysis_result['score']}/100</p>";
                
                if (!empty($analysis_result['recommendations'])) {
                    echo "<p class='info'>📋 Recommandations : " . count($analysis_result['recommendations']) . "</p>";
                    foreach (array_slice($analysis_result['recommendations'], 0, 3) as $rec) {
                        echo "<p class='info'>• [{$rec['type']}] {$rec['text']}</p>";
                    }
                }
                
                echo "<p class='success'>🧠 Analyse intelligente effectuée</p>";
            } else {
                echo "<p class='error'>❌ Erreur lors de l'analyse</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur lors de l'analyse : " . $e->getMessage() . "</p>";
        }
    }
    
    /**
     * Affiche un résumé des tests
     */
    private function display_summary() {
        echo "<div style='background:#f0f0f0;padding:20px;border-radius:8px;'>";
        echo "<h3>🎯 Résumé de l'Intégration IA</h3>";
        
        if ($this->settings && $this->settings->is_ai_configured()) {
            echo "<p class='success'>✅ <strong>IA CONFIGURÉE ET ACTIVE</strong></p>";
            echo "<p class='info'>🤖 Fournisseur : " . $this->settings->get_ai_provider() . "</p>";
            echo "<p class='info'>🧠 Modèle : " . $this->settings->get_ai_model() . "</p>";
            
            $ai_features = array(
                'use_ai_for_titles' => 'Génération de titres',
                'use_ai_for_descriptions' => 'Génération de descriptions',
                'use_ai_for_content' => 'Optimisation de contenu',
                'use_ai_for_alt_text' => 'Génération d\'attributs alt'
            );
            
            echo "<p><strong>Fonctionnalités IA activées :</strong></p><ul>";
            foreach ($ai_features as $setting => $label) {
                $enabled = $this->settings->get('ai', $setting, false);
                $status = $enabled ? '✅' : '❌';
                echo "<li>{$status} {$label}</li>";
            }
            echo "</ul>";
            
        } else {
            echo "<p class='warning'>⚠️ <strong>IA NON CONFIGURÉE</strong></p>";
            echo "<p class='info'>Le plugin utilise les algorithmes de fallback</p>";
        }
        
        echo "<p class='info'>💡 <strong>Conclusion :</strong> ";
        if ($this->settings && $this->settings->is_ai_configured()) {
            echo "L'intégration IA est fonctionnelle ! Les boutons d'optimisation utilisent bien l'IA configurée.</p>";
        } else {
            echo "Configurez une clé API IA dans les paramètres pour activer l'optimisation intelligente.</p>";
        }
        
        echo "</div>";
    }
    
    /**
     * Simule les fonctions WordPress
     */
    private function mock_wordpress_functions() {
        if (!function_exists('get_post_meta')) {
            function get_post_meta($post_id, $key, $single = false) {
                global $mock_post_meta;
                if (isset($mock_post_meta[$post_id][$key])) {
                    return $mock_post_meta[$post_id][$key];
                }
                return $single ? '' : array();
            }
        }
        
        if (!function_exists('get_the_title')) {
            function get_the_title($post_id) {
                global $mock_posts;
                return isset($mock_posts[$post_id]['title']) ? $mock_posts[$post_id]['title'] : 'Titre de test';
            }
        }
        
        if (!function_exists('get_post')) {
            function get_post($post_id) {
                global $mock_posts;
                if (isset($mock_posts[$post_id])) {
                    $post = new stdClass();
                    $post->ID = $post_id;
                    $post->post_title = $mock_posts[$post_id]['title'];
                    $post->post_content = $mock_posts[$post_id]['content'];
                    return $post;
                }
                return null;
            }
        }
        
        if (!function_exists('get_the_category')) {
            function get_the_category($post_id) {
                $category = new stdClass();
                $category->name = 'SEO';
                return array($category);
            }
        }
        
        if (!function_exists('wp_trim_words')) {
            function wp_trim_words($text, $num_words = 55, $more = null) {
                $words = explode(' ', $text);
                return implode(' ', array_slice($words, 0, $num_words));
            }
        }
        
        if (!function_exists('wp_strip_all_tags')) {
            function wp_strip_all_tags($string) {
                return strip_tags($string);
            }
        }
    }
    
    /**
     * Simule les paramètres IA
     */
    private function mock_ai_settings($provider) {
        // Simuler une configuration IA pour les tests
        global $mock_ai_settings;
        $mock_ai_settings = array(
            'ai' => array(
                'provider' => $provider,
                'openai_api_key' => 'sk-test-key-123',
                'anthropic_api_key' => 'sk-ant-test-123',
                'gemini_api_key' => 'AIza-test-123',
                'use_ai_for_titles' => true,
                'use_ai_for_descriptions' => true,
                'use_ai_for_content' => true,
                'use_ai_for_alt_text' => true
            )
        );
    }
    
    /**
     * Simule les données de post
     */
    private function mock_post_data($post_id, $title, $content) {
        global $mock_posts;
        $mock_posts[$post_id] = array(
            'title' => $title,
            'content' => $content
        );
    }
    
    /**
     * Simule les métadonnées de post
     */
    private function mock_post_meta($post_id, $meta_data) {
        global $mock_post_meta;
        $mock_post_meta[$post_id] = $meta_data;
    }
}

// Exécuter les tests
$tester = new Boss_SEO_AI_Integration_Test();
$tester->run_all_tests();

echo "</body></html>";
?>
