/**
 * Service pour le module SEO Local
 *
 * Gère les communications avec l'API pour les fonctionnalités de SEO Local
 */

import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class LocalSeoService {
  /**
   * Récupère les données du tableau de bord SEO Local
   *
   * @returns {Promise} Promesse contenant les données du tableau de bord
   */
  async getDashboardData() {
    try {
      const path = '/boss-seo/v1/local/dashboard';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données du tableau de bord SEO Local:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques du tableau de bord SEO Local
   *
   * @returns {Promise} Promesse contenant les statistiques du tableau de bord
   */
  async getDashboardStats() {
    try {
      const path = '/boss-seo/v1/local/dashboard/stats';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques du tableau de bord SEO Local:', error);
      throw error;
    }
  }

  /**
   * Récupère les classements du tableau de bord SEO Local
   *
   * @returns {Promise} Promesse contenant les classements du tableau de bord
   */
  async getDashboardRankings() {
    try {
      const path = '/boss-seo/v1/local/dashboard/rankings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des classements du tableau de bord SEO Local:', error);
      throw error;
    }
  }

  /**
   * Récupère les emplacements du tableau de bord SEO Local
   *
   * @returns {Promise} Promesse contenant les emplacements du tableau de bord
   */
  async getDashboardLocations() {
    try {
      const path = '/boss-seo/v1/local/dashboard/locations';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des emplacements du tableau de bord SEO Local:', error);
      throw error;
    }
  }

  /**
   * Récupère la liste des emplacements
   *
   * @param {Object} filters - Filtres pour la requête
   * @returns {Promise} Promesse contenant la liste des emplacements
   */
  async getLocations(filters = {}) {
    try {
      const path = addQueryArgs('/boss-seo/v1/local/locations', filters);
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des emplacements:', error);
      throw error;
    }
  }

  /**
   * Récupère un emplacement spécifique
   *
   * @param {number} id - ID de l'emplacement
   * @returns {Promise} Promesse contenant les détails de l'emplacement
   */
  async getLocation(id) {
    try {
      const path = `/boss-seo/v1/local/locations/${id}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'emplacement ${id}:`, error);
      throw error;
    }
  }

  /**
   * Crée un nouvel emplacement
   *
   * @param {Object} locationData - Données de l'emplacement
   * @returns {Promise} Promesse contenant les détails de l'emplacement créé
   */
  async createLocation(locationData) {
    try {
      const path = '/boss-seo/v1/local/locations';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { location: locationData }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la création de l\'emplacement:', error);
      // Retourner un objet d'erreur formaté au lieu de lancer une exception
      return {
        success: false,
        message: error.message || 'Une erreur est survenue lors de la création de l\'emplacement',
        error: error
      };
    }
  }

  /**
   * Met à jour un emplacement existant
   *
   * @param {number} id - ID de l'emplacement
   * @param {Object} locationData - Données de l'emplacement
   * @returns {Promise} Promesse contenant les détails de l'emplacement mis à jour
   */
  async updateLocation(id, locationData) {
    try {
      const path = `/boss-seo/v1/local/locations/${id}`;
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { location: locationData }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de l'emplacement ${id}:`, error);
      throw error;
    }
  }

  /**
   * Supprime un emplacement
   *
   * @param {number} id - ID de l'emplacement
   * @returns {Promise} Promesse contenant le résultat de la suppression
   */
  async deleteLocation(id) {
    try {
      const path = `/boss-seo/v1/local/locations/${id}`;
      const response = await apiFetch({
        path,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la suppression de l'emplacement ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les informations d'entreprise
   *
   * @returns {Promise} Promesse contenant les informations d'entreprise
   */
  async getBusinessInfo() {
    try {
      const path = '/boss-seo/v1/local/business-info';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des informations d\'entreprise:', error);
      throw error;
    }
  }

  /**
   * Enregistre les informations d'entreprise
   *
   * @param {Object} businessInfo - Informations d'entreprise
   * @returns {Promise} Promesse contenant le résultat de l'enregistrement
   */
  async saveBusinessInfo(businessInfo) {
    try {
      const path = '/boss-seo/v1/local/business-info';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { business_info: businessInfo }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des informations d\'entreprise:', error);
      throw error;
    }
  }

  /**
   * Récupère les horaires d'ouverture
   *
   * @returns {Promise} Promesse contenant les horaires d'ouverture
   */
  async getBusinessHours() {
    try {
      const path = '/boss-seo/v1/local/business-hours';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des horaires d\'ouverture:', error);
      throw error;
    }
  }

  /**
   * Enregistre les horaires d'ouverture
   *
   * @param {Object} hours - Horaires d'ouverture
   * @returns {Promise} Promesse contenant le résultat de l'enregistrement
   */
  async saveBusinessHours(hours) {
    try {
      const path = '/boss-seo/v1/local/business-hours';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { hours }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des horaires d\'ouverture:', error);
      throw error;
    }
  }

  /**
   * Récupère les modèles de page
   *
   * @returns {Promise} Promesse contenant les modèles de page
   */
  async getPageTemplates() {
    try {
      const path = '/boss-seo/v1/local/page-templates';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des modèles de page:', error);
      throw error;
    }
  }

  /**
   * Génère une page locale
   *
   * @param {Object} pageData - Données de la page
   * @returns {Promise} Promesse contenant les détails de la page générée
   */
  async generatePage(pageData) {
    try {
      const path = '/boss-seo/v1/local/generate-page';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { page: pageData }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la génération de la page:', error);
      throw error;
    }
  }

  /**
   * Récupère les pages générées pour un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @returns {Promise} Promesse contenant la liste des pages générées
   */
  async getGeneratedPages(locationId) {
    try {
      const path = `/boss-seo/v1/local/generated-pages/${locationId}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération des pages générées pour l'emplacement ${locationId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres de schéma
   *
   * @returns {Promise} Promesse contenant les paramètres de schéma
   */
  async getSchemaSettings() {
    try {
      const path = '/boss-seo/v1/local/schema-settings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres de schéma:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres de schéma
   *
   * @param {Object} settings - Paramètres de schéma
   * @returns {Promise} Promesse contenant le résultat de l'enregistrement
   */
  async saveSchemaSettings(settings) {
    try {
      const path = '/boss-seo/v1/local/schema-settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { schema_settings: settings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres de schéma:', error);
      throw error;
    }
  }

  /**
   * Génère un schéma pour un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @param {string} schemaType - Type de schéma
   * @returns {Promise} Promesse contenant le schéma généré
   */
  async generateSchema(locationId, schemaType) {
    try {
      const path = '/boss-seo/v1/local/generate-schema';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { location_id: locationId, schema_type: schemaType }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la génération du schéma:', error);
      throw error;
    }
  }

  /**
   * Teste un schéma avec l'outil de test de Google
   *
   * @param {Object} schema - Schéma à tester
   * @returns {Promise} Promesse contenant le résultat du test
   */
  async testSchema(schema) {
    try {
      const path = '/boss-seo/v1/local/test-schema';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { schema }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du test du schéma:', error);
      throw error;
    }
  }

  /**
   * Récupère les mots-clés pour un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @returns {Promise} Promesse contenant la liste des mots-clés
   */
  async getKeywords(locationId) {
    try {
      const path = `/boss-seo/v1/local/keywords/${locationId}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération des mots-clés pour l'emplacement ${locationId}:`, error);
      throw error;
    }
  }

  /**
   * Ajoute un mot-clé pour un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @param {string} keyword - Mot-clé à ajouter
   * @returns {Promise} Promesse contenant le résultat de l'ajout
   */
  async addKeyword(locationId, keyword) {
    try {
      const path = '/boss-seo/v1/local/keywords';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { location_id: locationId, keyword }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'ajout du mot-clé:', error);
      throw error;
    }
  }

  /**
   * Supprime un mot-clé
   *
   * @param {number} keywordId - ID du mot-clé
   * @returns {Promise} Promesse contenant le résultat de la suppression
   */
  async deleteKeyword(keywordId) {
    try {
      const path = `/boss-seo/v1/local/keywords/${keywordId}`;
      const response = await apiFetch({
        path,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la suppression du mot-clé ${keywordId}:`, error);
      throw error;
    }
  }

  /**
   * Met à jour les classements pour un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @returns {Promise} Promesse contenant le résultat de la mise à jour
   */
  async updateRankings(locationId) {
    try {
      const path = '/boss-seo/v1/local/update-rankings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { location_id: locationId }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour des classements pour l'emplacement ${locationId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des classements pour un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @param {string} period - Période (7days, 30days, 90days, 1year)
   * @returns {Promise} Promesse contenant l'historique des classements
   */
  async getRankingsHistory(locationId, period = '30days') {
    try {
      const path = addQueryArgs(`/boss-seo/v1/local/rankings-history/${locationId}`, { period });
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'historique des classements pour l'emplacement ${locationId}:`, error);
      throw error;
    }
  }

  /**
   * Analyse un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @returns {Promise} Promesse contenant les résultats de l'analyse
   */
  async analyzeLocation(locationId) {
    try {
      const path = '/boss-seo/v1/local/analyze-location';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { location_id: locationId }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'analyse de l'emplacement ${locationId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère l'analyse d'un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @returns {Promise} Promesse contenant l'analyse de l'emplacement
   */
  async getLocationAnalysis(locationId) {
    try {
      const path = `/boss-seo/v1/local/location-analysis/${locationId}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'analyse pour l'emplacement ${locationId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des analyses pour un emplacement
   *
   * @param {number} locationId - ID de l'emplacement
   * @returns {Promise} Promesse contenant l'historique des analyses
   */
  async getAnalysisHistory(locationId) {
    try {
      const path = `/boss-seo/v1/local/analysis-history/${locationId}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'historique des analyses pour l'emplacement ${locationId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres du module SEO Local
   *
   * @returns {Promise} Promesse contenant les paramètres du module
   */
  async getLocalSettings() {
    try {
      const path = '/boss-seo/v1/local/settings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres du module SEO Local:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres du module SEO Local
   *
   * @param {Object} settings - Paramètres du module
   * @returns {Promise} Promesse contenant le résultat de l'enregistrement
   */
  async saveLocalSettings(settings) {
    try {
      const path = '/boss-seo/v1/local/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: settings
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres du module SEO Local:', error);
      throw error;
    }
  }
}

export default LocalSeoService;
