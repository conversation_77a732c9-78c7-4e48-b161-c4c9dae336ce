<?php
/**
 * Meta Box moderne avec interface à onglets pour Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe Meta Box moderne avec onglets pour Boss SEO.
 *
 * Cette classe fournit une interface moderne avec onglets, sécurisée,
 * responsive et accessible pour la gestion SEO.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Metabox_Tabbed extends Boss_Optimizer_Metabox_Secure {

    /**
     * Retourne une traduction sécurisée ou le texte par défaut.
     *
     * @since    1.2.0
     * @param    string    $text      Texte à traduire.
     * @param    string    $default   Texte par défaut si traduction non disponible.
     * @return   string               Texte traduit ou par défaut.
     */
    private function safe_translate( $text, $default = '' ) {
        if ( function_exists( '__' ) && did_action( 'init' ) ) {
            return __( $text, 'boss-seo' );
        }
        return $default ?: $text;
    }

    /**
     * Affiche l'interface moderne avec onglets.
     *
     * @since    1.2.0
     * @param    WP_Post    $post                   Post actuel.
     * @param    array      $metadata               Métadonnées validées.
     * @param    array      $keyword_suggestions    Suggestions de mots-clés.
     */
    protected function render_secure_interface( $post, $metadata, $keyword_suggestions ) {
        ?>
        <div class="boss-seo-metabox-tabbed" data-post-id="<?php echo esc_attr( $post->ID ); ?>">

            <!-- Navigation par onglets moderne -->
            <div class="boss-seo-tabs-nav" role="tablist" aria-label="<?php esc_attr_e( 'Options SEO', 'boss-seo' ); ?>">
                <button type="button" class="boss-seo-tab-button active"
                        role="tab"
                        aria-selected="true"
                        aria-controls="boss-seo-tab-keywords"
                        id="boss-seo-tab-keywords-button"
                        data-tab="keywords">
                    <span class="dashicons dashicons-tag" aria-hidden="true"></span>
                    <span class="tab-label">Mots-clés</span>
                </button>

                <button type="button" class="boss-seo-tab-button"
                        role="tab"
                        aria-selected="false"
                        aria-controls="boss-seo-tab-metadata"
                        id="boss-seo-tab-metadata-button"
                        data-tab="metadata">
                    <span class="dashicons dashicons-admin-appearance" aria-hidden="true"></span>
                    <span class="tab-label">Métadonnées</span>
                </button>

                <button type="button" class="boss-seo-tab-button"
                        role="tab"
                        aria-selected="false"
                        aria-controls="boss-seo-tab-social"
                        id="boss-seo-tab-social-button"
                        data-tab="social">
                    <span class="dashicons dashicons-share" aria-hidden="true"></span>
                    <span class="tab-label">Réseaux Sociaux</span>
                </button>

                <button type="button" class="boss-seo-tab-button"
                        role="tab"
                        aria-selected="false"
                        aria-controls="boss-seo-tab-advanced"
                        id="boss-seo-tab-advanced-button"
                        data-tab="advanced">
                    <span class="dashicons dashicons-admin-tools" aria-hidden="true"></span>
                    <span class="tab-label">Avancé</span>
                </button>

                <button type="button" class="boss-seo-tab-button"
                        role="tab"
                        aria-selected="false"
                        aria-controls="boss-seo-tab-analysis"
                        id="boss-seo-tab-analysis-button"
                        data-tab="analysis">
                    <span class="dashicons dashicons-chart-bar" aria-hidden="true"></span>
                    <span class="tab-label">Analyse</span>
                    <?php if ( ! empty( $metadata['seo_score'] ) ) : ?>
                        <span class="boss-seo-score-badge boss-seo-score-<?php echo esc_attr( $this->get_score_class( $metadata['seo_score'] ) ); ?>">
                            <?php echo intval( $metadata['seo_score'] ); ?>
                        </span>
                    <?php endif; ?>
                </button>
            </div>

            <!-- Contenu des onglets -->
            <div class="boss-seo-tabs-content">

                <!-- Onglet 1: Mots-clés -->
                <div class="boss-seo-tab-panel active"
                     role="tabpanel"
                     id="boss-seo-tab-keywords"
                     aria-labelledby="boss-seo-tab-keywords-button"
                     data-tab="keywords">

                    <?php $this->render_keywords_tab( $metadata, $keyword_suggestions ); ?>
                </div>

                <!-- Onglet 2: Métadonnées SEO -->
                <div class="boss-seo-tab-panel"
                     role="tabpanel"
                     id="boss-seo-tab-metadata"
                     aria-labelledby="boss-seo-tab-metadata-button"
                     data-tab="metadata">

                    <?php $this->render_metadata_tab( $metadata ); ?>
                </div>

                <!-- Onglet 3: Réseaux Sociaux -->
                <div class="boss-seo-tab-panel"
                     role="tabpanel"
                     id="boss-seo-tab-social"
                     aria-labelledby="boss-seo-tab-social-button"
                     data-tab="social">

                    <?php $this->render_social_tab( $metadata ); ?>
                </div>

                <!-- Onglet 4: Avancé -->
                <div class="boss-seo-tab-panel"
                     role="tabpanel"
                     id="boss-seo-tab-advanced"
                     aria-labelledby="boss-seo-tab-advanced-button"
                     data-tab="advanced">

                    <?php $this->render_advanced_tab( $metadata ); ?>
                </div>

                <!-- Onglet 5: Analyse -->
                <div class="boss-seo-tab-panel"
                     role="tabpanel"
                     id="boss-seo-tab-analysis"
                     aria-labelledby="boss-seo-tab-analysis-button"
                     data-tab="analysis">

                    <?php $this->render_analysis_tab( $metadata ); ?>
                </div>
            </div>

            <!-- Barre d'actions flottante -->
            <div class="boss-seo-floating-actions">
                <button type="button" class="button button-primary" id="boss_seo_optimize_button" aria-describedby="boss_seo_optimize_help">
                    <span class="dashicons dashicons-superhero" aria-hidden="true"></span>
                    <?php esc_html_e( 'Optimiser avec l\'IA', 'boss-seo' ); ?>
                </button>

                <button type="button" class="button" id="boss_seo_analyze_button" aria-describedby="boss_seo_analyze_help">
                    <span class="dashicons dashicons-chart-bar" aria-hidden="true"></span>
                    <?php esc_html_e( 'Analyser', 'boss-seo' ); ?>
                </button>

                <div class="boss-seo-save-indicator" aria-live="polite">
                    <span class="dashicons dashicons-saved" aria-hidden="true"></span>
                    <span class="save-text"><?php esc_html_e( 'Sauvegardé', 'boss-seo' ); ?></span>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche l'onglet des mots-clés.
     *
     * @since    1.2.0
     * @param    array    $metadata               Métadonnées validées.
     * @param    array    $keyword_suggestions    Suggestions de mots-clés.
     */
    private function render_keywords_tab( $metadata, $keyword_suggestions ) {
        ?>
        <div class="boss-seo-tab-content">
            <div class="boss-seo-field-group">
                <label for="boss_seo_keywords_input" class="boss-seo-label">
                    <span class="dashicons dashicons-tag" aria-hidden="true"></span>
                    <?php esc_html_e( 'Ajouter des mots-clés', 'boss-seo' ); ?>
                </label>
                <div class="boss-seo-input-with-button">
                    <input
                        type="text"
                        id="boss_seo_keywords_input"
                        class="boss-seo-input"
                        placeholder="<?php esc_attr_e( 'Tapez un mot-clé et appuyez sur Entrée', 'boss-seo' ); ?>"
                        maxlength="50"
                        autocomplete="off"
                        aria-describedby="boss_seo_keywords_help"
                    >
                    <button type="button" class="boss-seo-add-keyword-btn" aria-label="<?php esc_attr_e( 'Ajouter le mot-clé', 'boss-seo' ); ?>">
                        <span class="dashicons dashicons-plus-alt2" aria-hidden="true"></span>
                    </button>
                </div>
                <div id="boss_seo_keywords_help" class="boss-seo-help">
                    <?php esc_html_e( 'Définissez un mot-clé principal (⭐) et des mots-clés secondaires. Cliquez sur une étoile pour définir le mot-clé principal.', 'boss-seo' ); ?>
                </div>

                <div class="boss-seo-keywords-container">
                    <div class="boss-seo-keywords-tags" role="list" aria-label="<?php esc_attr_e( 'Mots-clés sélectionnés', 'boss-seo' ); ?>">
                        <!-- Les mots-clés seront ajoutés ici par JavaScript -->
                    </div>
                </div>

                <?php if ( ! empty( $keyword_suggestions ) ) : ?>
                <div class="boss-seo-suggestions">
                    <div class="boss-seo-suggestions-header">
                        <span class="dashicons dashicons-lightbulb" aria-hidden="true"></span>
                        <span class="suggestions-title"><?php esc_html_e( 'Suggestions intelligentes', 'boss-seo' ); ?></span>
                        <button type="button" class="boss-seo-refresh-suggestions" aria-label="<?php esc_attr_e( 'Actualiser les suggestions', 'boss-seo' ); ?>">
                            <span class="dashicons dashicons-update" aria-hidden="true"></span>
                        </button>
                    </div>
                    <div class="boss-seo-suggestions-list" role="list">
                        <?php foreach ( $keyword_suggestions as $suggestion ) : ?>
                        <button
                            type="button"
                            class="boss-seo-suggestion"
                            data-keyword="<?php echo esc_attr( $suggestion ); ?>"
                            role="listitem"
                        >
                            <span class="dashicons dashicons-plus-alt2" aria-hidden="true"></span>
                            <span class="suggestion-text"><?php echo esc_html( $suggestion ); ?></span>
                        </button>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Champs cachés pour stocker les valeurs -->
                <input type="hidden" name="boss_seo_focus_keyword" id="boss_seo_focus_keyword" value="<?php echo esc_attr( $metadata['focus_keyword'] ); ?>">
                <input type="hidden" name="boss_seo_secondary_keywords" id="boss_seo_secondary_keywords" value="<?php echo esc_attr( $metadata['secondary_keywords'] ); ?>">
            </div>
        </div>
        <?php
    }

    /**
     * Affiche l'onglet des métadonnées.
     *
     * @since    1.2.0
     * @param    array    $metadata    Métadonnées validées.
     */
    private function render_metadata_tab( $metadata ) {
        ?>
        <div class="boss-seo-tab-content">
            <div class="boss-seo-field-group">
                <label for="boss_seo_title" class="boss-seo-label">
                    <span class="dashicons dashicons-admin-appearance" aria-hidden="true"></span>
                    <?php esc_html_e( 'Titre SEO', 'boss-seo' ); ?>
                </label>
                <div class="boss-seo-input-container">
                    <input
                        type="text"
                        name="boss_seo_title"
                        id="boss_seo_title"
                        value="<?php echo esc_attr( $metadata['seo_title'] ); ?>"
                        class="boss-seo-input"
                        placeholder="<?php esc_attr_e( 'Titre optimisé pour les moteurs de recherche', 'boss-seo' ); ?>"
                        maxlength="120"
                        aria-describedby="boss_seo_title_help boss_seo_title_counter"
                    >
                    <div class="boss-seo-counter" id="boss_seo_title_counter" aria-live="polite">
                        <span class="boss-seo-counter-current"><?php echo mb_strlen( $metadata['seo_title'] ); ?></span>
                        <span class="boss-seo-counter-separator">/</span>
                        <span class="boss-seo-counter-max">60</span>
                        <span class="boss-seo-counter-label"><?php esc_html_e( 'caractères', 'boss-seo' ); ?></span>
                    </div>
                </div>
                <div id="boss_seo_title_help" class="boss-seo-help">
                    <?php esc_html_e( 'Laissez vide pour utiliser le titre de l\'article. Idéalement entre 50 et 60 caractères.', 'boss-seo' ); ?>
                </div>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_meta_description" class="boss-seo-label">
                    <span class="dashicons dashicons-text-page" aria-hidden="true"></span>
                    <?php esc_html_e( 'Meta description', 'boss-seo' ); ?>
                </label>
                <div class="boss-seo-input-container">
                    <textarea
                        name="boss_seo_meta_description"
                        id="boss_seo_meta_description"
                        class="boss-seo-textarea"
                        rows="3"
                        placeholder="<?php esc_attr_e( 'Description qui apparaîtra dans les résultats de recherche', 'boss-seo' ); ?>"
                        maxlength="320"
                        aria-describedby="boss_seo_meta_description_help boss_seo_meta_description_counter"
                    ><?php echo esc_textarea( $metadata['meta_description'] ); ?></textarea>
                    <div class="boss-seo-counter" id="boss_seo_meta_description_counter" aria-live="polite">
                        <span class="boss-seo-counter-current"><?php echo mb_strlen( $metadata['meta_description'] ); ?></span>
                        <span class="boss-seo-counter-separator">/</span>
                        <span class="boss-seo-counter-max">160</span>
                        <span class="boss-seo-counter-label"><?php esc_html_e( 'caractères', 'boss-seo' ); ?></span>
                    </div>
                </div>
                <div id="boss_seo_meta_description_help" class="boss-seo-help">
                    <?php esc_html_e( 'La méta-description apparaît dans les résultats de recherche. Idéalement entre 120 et 160 caractères.', 'boss-seo' ); ?>
                </div>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_canonical_url" class="boss-seo-label">
                    <span class="dashicons dashicons-admin-links" aria-hidden="true"></span>
                    <?php esc_html_e( 'URL canonique', 'boss-seo' ); ?>
                </label>
                <input
                    type="url"
                    name="boss_seo_canonical_url"
                    id="boss_seo_canonical_url"
                    value="<?php echo esc_url( $metadata['canonical_url'] ); ?>"
                    class="boss-seo-input"
                    placeholder="<?php esc_attr_e( 'https://example.com/page-canonique', 'boss-seo' ); ?>"
                    aria-describedby="boss_seo_canonical_help"
                >
                <div id="boss_seo_canonical_help" class="boss-seo-help">
                    <?php esc_html_e( 'Laissez vide pour utiliser l\'URL actuelle. Utilisez uniquement si cette page est un duplicata.', 'boss-seo' ); ?>
                </div>
            </div>

            <div class="boss-seo-field-grid">
                <div class="boss-seo-field-group">
                    <label for="boss_seo_robots_index" class="boss-seo-label">
                        <span class="dashicons dashicons-visibility" aria-hidden="true"></span>
                        <?php esc_html_e( 'Indexation', 'boss-seo' ); ?>
                    </label>
                    <select name="boss_seo_robots_index" id="boss_seo_robots_index" class="boss-seo-select">
                        <option value="index" <?php selected( $metadata['robots_index'], 'index' ); ?>><?php esc_html_e( 'Index (recommandé)', 'boss-seo' ); ?></option>
                        <option value="noindex" <?php selected( $metadata['robots_index'], 'noindex' ); ?>><?php esc_html_e( 'Noindex', 'boss-seo' ); ?></option>
                    </select>
                </div>

                <div class="boss-seo-field-group">
                    <label for="boss_seo_robots_follow" class="boss-seo-label">
                        <span class="dashicons dashicons-admin-links" aria-hidden="true"></span>
                        <?php esc_html_e( 'Suivi des liens', 'boss-seo' ); ?>
                    </label>
                    <select name="boss_seo_robots_follow" id="boss_seo_robots_follow" class="boss-seo-select">
                        <option value="follow" <?php selected( $metadata['robots_follow'], 'follow' ); ?>><?php esc_html_e( 'Follow (recommandé)', 'boss-seo' ); ?></option>
                        <option value="nofollow" <?php selected( $metadata['robots_follow'], 'nofollow' ); ?>><?php esc_html_e( 'Nofollow', 'boss-seo' ); ?></option>
                    </select>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche l'onglet des réseaux sociaux.
     *
     * @since    1.2.0
     * @param    array    $metadata    Métadonnées validées.
     */
    private function render_social_tab( $metadata ) {
        ?>
        <div class="boss-seo-tab-content">
            <!-- Open Graph -->
            <div class="boss-seo-section-header">
                <h4>
                    <span class="dashicons dashicons-facebook" aria-hidden="true"></span>
                    <?php esc_html_e( 'Open Graph (Facebook, LinkedIn)', 'boss-seo' ); ?>
                </h4>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_og_title" class="boss-seo-label">
                    <span class="dashicons dashicons-admin-appearance" aria-hidden="true"></span>
                    <?php esc_html_e( 'Titre Open Graph', 'boss-seo' ); ?>
                </label>
                <input
                    type="text"
                    name="boss_seo_og_title"
                    id="boss_seo_og_title"
                    value="<?php echo esc_attr( $metadata['og_title'] ); ?>"
                    class="boss-seo-input"
                    placeholder="<?php esc_attr_e( 'Titre pour Facebook et LinkedIn', 'boss-seo' ); ?>"
                    maxlength="95"
                >
                <div class="boss-seo-help">
                    <?php esc_html_e( 'Laissez vide pour utiliser le titre SEO. Maximum 95 caractères.', 'boss-seo' ); ?>
                </div>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_og_description" class="boss-seo-label">
                    <span class="dashicons dashicons-text-page" aria-hidden="true"></span>
                    <?php esc_html_e( 'Description Open Graph', 'boss-seo' ); ?>
                </label>
                <textarea
                    name="boss_seo_og_description"
                    id="boss_seo_og_description"
                    class="boss-seo-textarea"
                    rows="3"
                    placeholder="<?php esc_attr_e( 'Description pour Facebook et LinkedIn', 'boss-seo' ); ?>"
                    maxlength="300"
                ><?php echo esc_textarea( $metadata['og_description'] ); ?></textarea>
                <div class="boss-seo-help">
                    <?php esc_html_e( 'Laissez vide pour utiliser la meta description. Maximum 300 caractères.', 'boss-seo' ); ?>
                </div>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_og_image" class="boss-seo-label">
                    <span class="dashicons dashicons-format-image" aria-hidden="true"></span>
                    <?php esc_html_e( 'Image Open Graph', 'boss-seo' ); ?>
                </label>
                <div class="boss-seo-media-field">
                    <input
                        type="url"
                        name="boss_seo_og_image"
                        id="boss_seo_og_image"
                        value="<?php echo esc_url( $metadata['og_image'] ); ?>"
                        class="boss-seo-input"
                        placeholder="<?php esc_attr_e( 'URL de l\'image', 'boss-seo' ); ?>"
                    >
                    <button type="button" class="button boss-seo-media-button" data-target="boss_seo_og_image">
                        <span class="dashicons dashicons-admin-media" aria-hidden="true"></span>
                        <?php esc_html_e( 'Choisir', 'boss-seo' ); ?>
                    </button>
                </div>
                <div class="boss-seo-help">
                    <?php esc_html_e( 'Image recommandée : 1200x630 pixels. Laissez vide pour utiliser l\'image à la une.', 'boss-seo' ); ?>
                </div>
            </div>

            <!-- Twitter -->
            <div class="boss-seo-section-header">
                <h4>
                    <span class="dashicons dashicons-twitter" aria-hidden="true"></span>
                    <?php esc_html_e( 'Twitter Cards', 'boss-seo' ); ?>
                </h4>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_twitter_card_type" class="boss-seo-label">
                    <span class="dashicons dashicons-admin-tools" aria-hidden="true"></span>
                    <?php esc_html_e( 'Type de carte Twitter', 'boss-seo' ); ?>
                </label>
                <select name="boss_seo_twitter_card_type" id="boss_seo_twitter_card_type" class="boss-seo-select">
                    <option value="summary_large_image" <?php selected( $metadata['twitter_card_type'], 'summary_large_image' ); ?>><?php esc_html_e( 'Grande image (recommandé)', 'boss-seo' ); ?></option>
                    <option value="summary" <?php selected( $metadata['twitter_card_type'], 'summary' ); ?>><?php esc_html_e( 'Résumé', 'boss-seo' ); ?></option>
                </select>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_twitter_title" class="boss-seo-label">
                    <span class="dashicons dashicons-admin-appearance" aria-hidden="true"></span>
                    <?php esc_html_e( 'Titre Twitter', 'boss-seo' ); ?>
                </label>
                <input
                    type="text"
                    name="boss_seo_twitter_title"
                    id="boss_seo_twitter_title"
                    value="<?php echo esc_attr( $metadata['twitter_title'] ); ?>"
                    class="boss-seo-input"
                    placeholder="<?php esc_attr_e( 'Titre pour Twitter', 'boss-seo' ); ?>"
                    maxlength="70"
                >
                <div class="boss-seo-help">
                    <?php esc_html_e( 'Laissez vide pour utiliser le titre Open Graph. Maximum 70 caractères.', 'boss-seo' ); ?>
                </div>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_twitter_description" class="boss-seo-label">
                    <span class="dashicons dashicons-text-page" aria-hidden="true"></span>
                    <?php esc_html_e( 'Description Twitter', 'boss-seo' ); ?>
                </label>
                <textarea
                    name="boss_seo_twitter_description"
                    id="boss_seo_twitter_description"
                    class="boss-seo-textarea"
                    rows="3"
                    placeholder="<?php esc_attr_e( 'Description pour Twitter', 'boss-seo' ); ?>"
                    maxlength="200"
                ><?php echo esc_textarea( $metadata['twitter_description'] ); ?></textarea>
                <div class="boss-seo-help">
                    <?php esc_html_e( 'Laissez vide pour utiliser la description Open Graph. Maximum 200 caractères.', 'boss-seo' ); ?>
                </div>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_twitter_image" class="boss-seo-label">
                    <span class="dashicons dashicons-format-image" aria-hidden="true"></span>
                    <?php esc_html_e( 'Image Twitter', 'boss-seo' ); ?>
                </label>
                <div class="boss-seo-media-field">
                    <input
                        type="url"
                        name="boss_seo_twitter_image"
                        id="boss_seo_twitter_image"
                        value="<?php echo esc_url( $metadata['twitter_image'] ); ?>"
                        class="boss-seo-input"
                        placeholder="<?php esc_attr_e( 'URL de l\'image', 'boss-seo' ); ?>"
                    >
                    <button type="button" class="button boss-seo-media-button" data-target="boss_seo_twitter_image">
                        <span class="dashicons dashicons-admin-media" aria-hidden="true"></span>
                        <?php esc_html_e( 'Choisir', 'boss-seo' ); ?>
                    </button>
                </div>
                <div class="boss-seo-help">
                    <?php esc_html_e( 'Image recommandée : 1200x600 pixels. Laissez vide pour utiliser l\'image Open Graph.', 'boss-seo' ); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche l'onglet avancé.
     *
     * @since    1.2.0
     * @param    array    $metadata    Métadonnées validées.
     */
    private function render_advanced_tab( $metadata ) {
        ?>
        <div class="boss-seo-tab-content">
            <!-- Schémas structurés -->
            <div class="boss-seo-section-header">
                <h4>
                    <span class="dashicons dashicons-admin-tools" aria-hidden="true"></span>
                    <?php esc_html_e( 'Schémas Structurés', 'boss-seo' ); ?>
                </h4>
            </div>

            <div class="boss-seo-field-group">
                <label class="boss-seo-label">
                    <span class="dashicons dashicons-admin-generic" aria-hidden="true"></span>
                    <?php esc_html_e( 'Type de schéma', 'boss-seo' ); ?>
                </label>
                <select name="boss_seo_schema_type" id="boss_seo_schema_type" class="boss-seo-select">
                    <option value="auto"><?php esc_html_e( 'Automatique (recommandé)', 'boss-seo' ); ?></option>
                    <option value="article"><?php esc_html_e( 'Article', 'boss-seo' ); ?></option>
                    <option value="webpage"><?php esc_html_e( 'Page Web', 'boss-seo' ); ?></option>
                    <option value="product"><?php esc_html_e( 'Produit', 'boss-seo' ); ?></option>
                    <option value="organization"><?php esc_html_e( 'Organisation', 'boss-seo' ); ?></option>
                    <option value="person"><?php esc_html_e( 'Personne', 'boss-seo' ); ?></option>
                </select>
                <div class="boss-seo-help">
                    <?php esc_html_e( 'Le type de schéma sera détecté automatiquement selon le contenu.', 'boss-seo' ); ?>
                </div>
            </div>

            <!-- Paramètres robots -->
            <div class="boss-seo-section-header">
                <h4>
                    <span class="dashicons dashicons-admin-tools" aria-hidden="true"></span>
                    <?php esc_html_e( 'Paramètres Robots Avancés', 'boss-seo' ); ?>
                </h4>
            </div>

            <div class="boss-seo-field-grid">
                <div class="boss-seo-field-group">
                    <label class="boss-seo-checkbox-label">
                        <input type="checkbox" name="boss_seo_robots_noarchive" value="1" <?php checked( get_post_meta( get_the_ID(), '_boss_seo_robots_noarchive', true ), '1' ); ?>>
                        <span class="checkmark"></span>
                        <?php esc_html_e( 'Noarchive', 'boss-seo' ); ?>
                    </label>
                    <div class="boss-seo-help">
                        <?php esc_html_e( 'Empêche la mise en cache de la page.', 'boss-seo' ); ?>
                    </div>
                </div>

                <div class="boss-seo-field-group">
                    <label class="boss-seo-checkbox-label">
                        <input type="checkbox" name="boss_seo_robots_nosnippet" value="1" <?php checked( get_post_meta( get_the_ID(), '_boss_seo_robots_nosnippet', true ), '1' ); ?>>
                        <span class="checkmark"></span>
                        <?php esc_html_e( 'Nosnippet', 'boss-seo' ); ?>
                    </label>
                    <div class="boss-seo-help">
                        <?php esc_html_e( 'Empêche l\'affichage d\'extraits.', 'boss-seo' ); ?>
                    </div>
                </div>

                <div class="boss-seo-field-group">
                    <label class="boss-seo-checkbox-label">
                        <input type="checkbox" name="boss_seo_robots_noimageindex" value="1" <?php checked( get_post_meta( get_the_ID(), '_boss_seo_robots_noimageindex', true ), '1' ); ?>>
                        <span class="checkmark"></span>
                        <?php esc_html_e( 'Noimageindex', 'boss-seo' ); ?>
                    </label>
                    <div class="boss-seo-help">
                        <?php esc_html_e( 'Empêche l\'indexation des images.', 'boss-seo' ); ?>
                    </div>
                </div>

                <div class="boss-seo-field-group">
                    <label class="boss-seo-checkbox-label">
                        <input type="checkbox" name="boss_seo_robots_notranslate" value="1" <?php checked( get_post_meta( get_the_ID(), '_boss_seo_robots_notranslate', true ), '1' ); ?>>
                        <span class="checkmark"></span>
                        <?php esc_html_e( 'Notranslate', 'boss-seo' ); ?>
                    </label>
                    <div class="boss-seo-help">
                        <?php esc_html_e( 'Empêche la traduction automatique.', 'boss-seo' ); ?>
                    </div>
                </div>
            </div>

            <!-- Redirections -->
            <div class="boss-seo-section-header">
                <h4>
                    <span class="dashicons dashicons-admin-links" aria-hidden="true"></span>
                    <?php esc_html_e( 'Redirection', 'boss-seo' ); ?>
                </h4>
            </div>

            <div class="boss-seo-field-group">
                <label for="boss_seo_redirect_url" class="boss-seo-label">
                    <span class="dashicons dashicons-admin-links" aria-hidden="true"></span>
                    <?php esc_html_e( 'URL de redirection', 'boss-seo' ); ?>
                </label>
                <input
                    type="url"
                    name="boss_seo_redirect_url"
                    id="boss_seo_redirect_url"
                    value="<?php echo esc_url( get_post_meta( get_the_ID(), '_boss_seo_redirect_url', true ) ); ?>"
                    class="boss-seo-input"
                    placeholder="<?php esc_attr_e( 'https://example.com/nouvelle-page', 'boss-seo' ); ?>"
                >
                <div class="boss-seo-help">
                    <?php esc_html_e( 'Redirige automatiquement vers cette URL (redirection 301).', 'boss-seo' ); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Affiche l'onglet d'analyse.
     *
     * @since    1.2.0
     * @param    array    $metadata    Métadonnées validées.
     */
    private function render_analysis_tab( $metadata ) {
        ?>
        <div class="boss-seo-tab-content">
            <!-- Score SEO -->
            <div class="boss-seo-score-container">
                <?php if ( ! empty( $metadata['seo_score'] ) ) : ?>
                    <div class="boss-seo-score-indicator boss-seo-score-<?php echo esc_attr( $this->get_score_class( $metadata['seo_score'] ) ); ?>" role="img" aria-label="<?php echo esc_attr( sprintf( __( 'Score SEO: %d sur 100', 'boss-seo' ), $metadata['seo_score'] ) ); ?>">
                        <span class="boss-seo-score-value"><?php echo intval( $metadata['seo_score'] ); ?></span>
                        <span class="boss-seo-score-label">/100</span>
                    </div>
                <?php else : ?>
                    <div class="boss-seo-score-indicator boss-seo-score-none" role="img" aria-label="<?php esc_attr_e( 'Score SEO non calculé', 'boss-seo' ); ?>">
                        <span class="boss-seo-score-value">?</span>
                        <span class="boss-seo-score-label">/100</span>
                    </div>
                <?php endif; ?>

                <div class="boss-seo-score-details">
                    <h4><?php esc_html_e( 'Analyse SEO', 'boss-seo' ); ?></h4>
                    <p>
                        <?php esc_html_e( 'Dernière analyse:', 'boss-seo' ); ?>
                        <strong>
                            <?php
                            echo ! empty( $metadata['analysis_date'] )
                                ? esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $metadata['analysis_date'] ) ) )
                                : esc_html__( 'Jamais', 'boss-seo' );
                            ?>
                        </strong>
                    </p>
                </div>
            </div>

            <!-- Recommandations -->
            <?php if ( ! empty( $metadata['recommendations'] ) && is_array( $metadata['recommendations'] ) ) : ?>
                <div class="boss-seo-recommendations-container">
                    <h4>
                        <span class="dashicons dashicons-lightbulb" aria-hidden="true"></span>
                        <?php esc_html_e( 'Recommandations', 'boss-seo' ); ?>
                    </h4>
                    <div class="boss-seo-recommendations" role="list">
                        <?php foreach ( $metadata['recommendations'] as $recommendation ) : ?>
                            <div class="boss-seo-recommendation boss-seo-recommendation-<?php echo esc_attr( $recommendation['type'] ); ?>" role="listitem">
                                <span class="boss-seo-recommendation-icon dashicons dashicons-<?php echo esc_attr( $this->get_recommendation_icon( $recommendation['type'] ) ); ?>" aria-hidden="true"></span>
                                <span class="boss-seo-recommendation-text"><?php echo esc_html( $recommendation['text'] ); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php else : ?>
                <div class="boss-seo-no-recommendations">
                    <span class="dashicons dashicons-info" aria-hidden="true"></span>
                    <p><?php esc_html_e( 'Aucune recommandation disponible. Lancez une analyse pour obtenir des suggestions d\'amélioration.', 'boss-seo' ); ?></p>
                </div>
            <?php endif; ?>

            <!-- Aperçu SERP -->
            <div class="boss-seo-serp-preview">
                <h4>
                    <span class="dashicons dashicons-search" aria-hidden="true"></span>
                    <?php esc_html_e( 'Aperçu dans les résultats de recherche', 'boss-seo' ); ?>
                </h4>
                <div class="boss-seo-serp-result">
                    <div class="serp-url"><?php echo esc_url( get_permalink() ); ?></div>
                    <div class="serp-title" id="serp-title-preview">
                        <?php echo esc_html( $metadata['seo_title'] ?: get_the_title() ); ?>
                    </div>
                    <div class="serp-description" id="serp-description-preview">
                        <?php echo esc_html( $metadata['meta_description'] ?: wp_trim_words( get_the_excerpt(), 20 ) ); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Retourne la classe CSS pour le score SEO.
     *
     * @since    1.2.0
     * @param    int    $score    Score SEO.
     * @return   string           Classe CSS.
     */
    private function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'good';
        } elseif ( $score >= 60 ) {
            return 'ok';
        } elseif ( $score >= 40 ) {
            return 'poor';
        } else {
            return 'bad';
        }
    }

    /**
     * Retourne l'icône pour un type de recommandation.
     *
     * @since    1.2.0
     * @param    string    $type    Type de recommandation.
     * @return   string             Nom de l'icône Dashicons.
     */
    private function get_recommendation_icon( $type ) {
        switch ( $type ) {
            case 'critical':
                return 'warning';
            case 'warning':
                return 'info';
            case 'info':
            default:
                return 'lightbulb';
        }
    }

    /**
     * Enqueue les scripts et styles pour les meta boxes.
     *
     * @since    1.2.0
     */
    public function enqueue_scripts() {
        global $post_type;

        // Vérifier si on est sur une page d'édition de post
        $screen = get_current_screen();
        if ( ! $screen || ! in_array( $screen->base, array( 'post', 'page' ) ) ) {
            return;
        }

        // Vérifier si le type de post est supporté
        $supported_post_types = apply_filters( 'boss_seo_supported_post_types', array( 'post', 'page' ) );
        if ( ! in_array( $post_type, $supported_post_types ) ) {
            return;
        }

        // Enqueue CSS
        wp_enqueue_style(
            'boss-seo-metabox-tabbed',
            plugin_dir_url( dirname( __FILE__ ) ) . 'admin/css/boss-seo-metabox-secure.css',
            array(),
            BOSS_SEO_VERSION,
            'all'
        );

        // Enqueue JavaScript
        wp_enqueue_script(
            'boss-seo-metabox-tabbed',
            plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/boss-seo-metabox-secure.js',
            array( 'jquery', 'wp-util' ),
            BOSS_SEO_VERSION,
            true
        );

        // Localiser le script avec des chaînes sécurisées
        wp_localize_script( 'boss-seo-metabox-tabbed', 'bossSeoTabbed', array(
            'ajaxUrl' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'boss_seo_metabox_nonce' ),
            'postId' => get_the_ID(),
            'strings' => array(
                'saving' => 'Sauvegarde...',
                'saved' => 'Sauvegardé',
                'error' => 'Erreur de sauvegarde',
                'loading' => 'Chargement...',
                'retry' => 'Réessayer',
            )
        ) );
    }
}
