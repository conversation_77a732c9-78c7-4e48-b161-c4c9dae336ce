<?php
/**
 * Classe principale pour la gestion technique du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe principale pour la gestion technique du plugin Boss SEO.
 *
 * Cette classe gère les fonctionnalités de gestion technique du plugin Boss SEO :
 * - Redirections
 * - Robots.txt et Sitemap
 * - Optimisation des médias
 * - En-têtes HTTP
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Technical_Management {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de gestion des redirections.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Redirections    $redirections    Instance de la classe de gestion des redirections.
     */
    protected $redirections;

    /**
     * Instance de la classe de gestion des robots.txt et sitemaps.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Robots_Sitemap    $robots_sitemap    Instance de la classe de gestion des robots.txt et sitemaps.
     */
    protected $robots_sitemap;

    /**
     * Instance de la classe d'optimisation des médias.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Media_Optimization    $media_optimization    Instance de la classe d'optimisation des médias.
     */
    protected $media_optimization;

    /**
     * Instance de la classe de gestion des en-têtes HTTP.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Http_Headers    $http_headers    Instance de la classe de gestion des en-têtes HTTP.
     */
    protected $http_headers;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_components();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.1.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * La classe de gestion des redirections.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-redirections.php';

        /**
         * La classe de gestion des robots.txt et sitemaps.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-robots-sitemap.php';

        /**
         * La classe d'optimisation des médias.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-media-optimization.php';

        /**
         * La classe de gestion des en-têtes HTTP.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-http-headers.php';
    }

    /**
     * Initialise les composants du module.
     *
     * @since    1.1.0
     * @access   private
     */
    private function init_components() {
        $this->redirections = new Boss_Redirections( $this->plugin_name, $this->version );
        $this->robots_sitemap = new Boss_Robots_Sitemap( $this->plugin_name, $this->version );
        $this->media_optimization = new Boss_Media_Optimization( $this->plugin_name, $this->version );
        $this->http_headers = new Boss_Http_Headers( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Enregistrer les hooks pour les redirections
        $this->redirections->register_hooks();

        // Enregistrer les hooks pour les robots.txt et sitemaps
        $this->robots_sitemap->register_hooks();

        // Enregistrer les hooks pour l'optimisation des médias
        $this->media_optimization->register_hooks();

        // Enregistrer les hooks pour les en-têtes HTTP
        $this->http_headers->register_hooks();
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API pour les redirections
        $this->redirections->register_rest_routes();

        // Enregistrer les routes REST API pour les robots.txt et sitemaps
        $this->robots_sitemap->register_rest_routes();

        // Enregistrer les routes REST API pour l'optimisation des médias
        $this->media_optimization->register_rest_routes();

        // Enregistrer les routes REST API pour les en-têtes HTTP
        $this->http_headers->register_rest_routes();
    }
}
