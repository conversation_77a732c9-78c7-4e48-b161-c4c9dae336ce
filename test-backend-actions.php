<?php
/**
 * Test des actions backend Boss SEO
 * 
 * Ce fichier teste toutes les fonctionnalités backend
 * des meta boxes Boss SEO.
 */

// Simuler l'environnement WordPress pour les tests
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Inclure les classes nécessaires
require_once 'includes/class-boss-seo-analyzer.php';
require_once 'includes/class-boss-seo-optimizer.php';
require_once 'includes/class-boss-seo-schema-generator.php';

/**
 * Classe de test pour les actions backend
 */
class Boss_SEO_Backend_Test {
    
    /**
     * Exécute tous les tests
     */
    public function run_all_tests() {
        echo "<h1>🧪 Tests des Actions Backend Boss SEO</h1>\n";
        
        $this->test_analyzer();
        $this->test_optimizer();
        $this->test_schema_generator();
        $this->test_integration();
        
        echo "<h2>✅ Tous les tests terminés !</h2>\n";
    }
    
    /**
     * Test de la classe Analyzer
     */
    private function test_analyzer() {
        echo "<h2>🔍 Test de l'Analyzer</h2>\n";
        
        $analyzer = new Boss_SEO_Analyzer();
        
        // Test d'analyse de contenu
        $mock_post_id = 123;
        $mock_content = "Ceci est un contenu de test pour WordPress SEO. Il contient des mots-clés importants comme optimisation et référencement.";
        
        echo "<h3>Test d'analyse de contenu</h3>\n";
        try {
            // Simuler les métadonnées
            $this->mock_post_meta($mock_post_id, array(
                '_boss_seo_title' => 'Guide WordPress SEO',
                '_boss_seo_meta_description' => 'Découvrez comment optimiser votre site WordPress pour le SEO',
                '_boss_seo_focus_keyword' => 'wordpress seo'
            ));
            
            $result = $analyzer->perform_content_analysis($mock_post_id, $mock_content);
            
            echo "✅ Analyse réussie - Score: " . $result['score'] . "/100<br>\n";
            echo "📊 Recommandations: " . count($result['recommendations']) . "<br>\n";
            
            foreach ($result['recommendations'] as $rec) {
                echo "- [{$rec['type']}] {$rec['text']}<br>\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "<br>\n";
        }
        
        // Test de génération de suggestions
        echo "<h3>Test de génération de suggestions</h3>\n";
        try {
            $suggestions = $analyzer->generate_smart_suggestions($mock_post_id, $mock_content);
            
            echo "✅ Suggestions générées: " . count($suggestions) . "<br>\n";
            echo "🏷️ Mots-clés suggérés: " . implode(', ', array_slice($suggestions, 0, 5)) . "<br>\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "<br>\n";
        }
    }
    
    /**
     * Test de la classe Optimizer
     */
    private function test_optimizer() {
        echo "<h2>⚡ Test de l'Optimizer</h2>\n";
        
        $optimizer = new Boss_SEO_Optimizer();
        
        // Test d'optimisation de contenu
        echo "<h3>Test d'optimisation de contenu</h3>\n";
        try {
            $mock_post_id = 123;
            $mock_content = "Contenu à optimiser pour le SEO";
            $keywords = array('seo', 'optimisation', 'wordpress');
            
            $result = $optimizer->perform_content_optimization($mock_post_id, $mock_content, $keywords);
            
            echo "✅ Optimisation réussie<br>\n";
            echo "🎯 Mot-clé principal: " . $result['primary_keyword'] . "<br>\n";
            
            if (!empty($result['optimizations'])) {
                foreach ($result['optimizations'] as $type => $optimization) {
                    if (is_string($optimization)) {
                        echo "- {$type}: {$optimization}<br>\n";
                    } else if (is_array($optimization)) {
                        echo "- {$type}: " . count($optimization) . " suggestions<br>\n";
                    }
                }
            }
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "<br>\n";
        }
        
        // Test de validation de métadonnées
        echo "<h3>Test de validation de métadonnées</h3>\n";
        try {
            $metadata = array(
                'boss_seo_title' => 'Titre de test',
                'boss_seo_meta_description' => 'Description de test',
                'boss_seo_focus_keyword' => 'test',
                'boss_seo_canonical_url' => 'https://example.com/test',
                'boss_seo_robots_index' => 'index'
            );
            
            $validated = $optimizer->validate_metadata_fields($metadata);
            
            echo "✅ Validation réussie - " . count($validated) . " champs validés<br>\n";
            
            foreach ($validated as $field => $value) {
                echo "- {$field}: " . (is_string($value) ? $value : 'valeur complexe') . "<br>\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "<br>\n";
        }
        
        // Test de validation de champ individuel
        echo "<h3>Test de validation de champ individuel</h3>\n";
        try {
            $validation = $optimizer->validate_single_field('boss_seo_title', 'Titre trop long pour être optimal en SEO car il dépasse largement les 60 caractères recommandés');
            
            echo "✅ Validation de champ réussie<br>\n";
            echo "- Valide: " . ($validation['valid'] ? 'Oui' : 'Non') . "<br>\n";
            echo "- Message: " . $validation['message'] . "<br>\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "<br>\n";
        }
    }
    
    /**
     * Test de la classe Schema Generator
     */
    private function test_schema_generator() {
        echo "<h2>🏗️ Test du Schema Generator</h2>\n";
        
        $generator = new Boss_SEO_Schema_Generator();
        
        // Test de génération de schéma Article
        echo "<h3>Test de génération de schéma Article</h3>\n";
        try {
            $mock_post = $this->create_mock_post();
            $schema = $generator->generate_schema_markup($mock_post->ID, 'article');
            
            echo "✅ Schéma Article généré<br>\n";
            echo "- Type: " . $schema['@type'] . "<br>\n";
            echo "- Titre: " . $schema['headline'] . "<br>\n";
            echo "- URL: " . $schema['url'] . "<br>\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "<br>\n";
        }
        
        // Test de génération de schéma WebPage
        echo "<h3>Test de génération de schéma WebPage</h3>\n";
        try {
            $schema = $generator->generate_schema_markup($mock_post->ID, 'webpage');
            
            echo "✅ Schéma WebPage généré<br>\n";
            echo "- Type: " . $schema['@type'] . "<br>\n";
            echo "- Nom: " . $schema['name'] . "<br>\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "<br>\n";
        }
        
        // Test de validation de schéma
        echo "<h3>Test de validation de schéma</h3>\n";
        try {
            $validation = $generator->validate_schema($schema);
            
            echo "✅ Validation de schéma réussie<br>\n";
            echo "- Valide: " . ($validation['valid'] ? 'Oui' : 'Non') . "<br>\n";
            echo "- Erreurs: " . count($validation['errors']) . "<br>\n";
            echo "- Avertissements: " . count($validation['warnings']) . "<br>\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "<br>\n";
        }
    }
    
    /**
     * Test d'intégration complète
     */
    private function test_integration() {
        echo "<h2>🔗 Test d'intégration complète</h2>\n";
        
        try {
            $mock_post_id = 456;
            $content = "Ceci est un guide complet sur l'optimisation SEO pour WordPress. Nous allons voir comment améliorer le référencement de votre site web.";
            
            // 1. Analyser le contenu
            $analyzer = new Boss_SEO_Analyzer();
            $analysis = $analyzer->perform_content_analysis($mock_post_id, $content);
            
            // 2. Générer des suggestions
            $suggestions = $analyzer->generate_smart_suggestions($mock_post_id, $content);
            
            // 3. Optimiser avec les suggestions
            $optimizer = new Boss_SEO_Optimizer();
            $optimization = $optimizer->perform_content_optimization($mock_post_id, $content, array_slice($suggestions, 0, 3));
            
            // 4. Générer un schéma
            $generator = new Boss_SEO_Schema_Generator();
            $schema = $generator->generate_schema_markup($mock_post_id, 'auto');
            
            echo "✅ Intégration complète réussie !<br>\n";
            echo "📊 Score d'analyse: " . $analysis['score'] . "/100<br>\n";
            echo "🏷️ Suggestions générées: " . count($suggestions) . "<br>\n";
            echo "⚡ Optimisations proposées: " . count($optimization['optimizations']) . "<br>\n";
            echo "🏗️ Schéma généré: " . $schema['@type'] . "<br>\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur d'intégration: " . $e->getMessage() . "<br>\n";
        }
    }
    
    /**
     * Simule les métadonnées de post
     */
    private function mock_post_meta($post_id, $meta_data) {
        // Dans un vrai environnement WordPress, ceci utiliserait get_post_meta()
        // Pour les tests, on simule les données
        global $mock_post_meta;
        $mock_post_meta[$post_id] = $meta_data;
    }
    
    /**
     * Crée un post fictif pour les tests
     */
    private function create_mock_post() {
        $post = new stdClass();
        $post->ID = 789;
        $post->post_title = 'Article de test SEO';
        $post->post_content = 'Contenu de test pour l\'article SEO';
        $post->post_author = 1;
        $post->post_type = 'post';
        
        return $post;
    }
}

// Simuler quelques fonctions WordPress pour les tests
if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $key, $single = false) {
        global $mock_post_meta;
        if (isset($mock_post_meta[$post_id][$key])) {
            return $mock_post_meta[$post_id][$key];
        }
        return $single ? '' : array();
    }
}

if (!function_exists('update_post_meta')) {
    function update_post_meta($post_id, $key, $value) {
        global $mock_post_meta;
        $mock_post_meta[$post_id][$key] = $value;
        return true;
    }
}

if (!function_exists('get_the_title')) {
    function get_the_title($post_id) {
        return 'Titre de test pour le post ' . $post_id;
    }
}

if (!function_exists('get_permalink')) {
    function get_permalink($post_id) {
        return 'https://example.com/post-' . $post_id;
    }
}

if (!function_exists('current_time')) {
    function current_time($format) {
        return date($format);
    }
}

if (!function_exists('get_bloginfo')) {
    function get_bloginfo($show) {
        switch ($show) {
            case 'name': return 'Site de Test Boss SEO';
            case 'description': return 'Description du site de test';
            default: return '';
        }
    }
}

if (!function_exists('home_url')) {
    function home_url($path = '') {
        return 'https://example.com' . $path;
    }
}

// Exécuter les tests si ce fichier est appelé directement
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    echo "<!DOCTYPE html><html><head><title>Tests Backend Boss SEO</title></head><body>";
    
    $tester = new Boss_SEO_Backend_Test();
    $tester->run_all_tests();
    
    echo "</body></html>";
}
?>
