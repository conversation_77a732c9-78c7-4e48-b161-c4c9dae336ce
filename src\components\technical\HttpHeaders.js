import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  TabPanel,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer les sous-composants
import HeadersList from './headers/HeadersList';
import HeaderForm from './headers/HeaderForm';
import HeadersTest from './headers/HeadersTest';
import PreconnectSettings from './headers/PreconnectSettings';

const HttpHeaders = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isRunningTest, setIsRunningTest] = useState(false);
  const [headers, setHeaders] = useState([]);
  const [commonHeaders, setCommonHeaders] = useState([]);
  const [preconnectUrls, setPreconnectUrls] = useState([]);
  const [editingHeader, setEditingHeader] = useState(null);
  const [testResults, setTestResults] = useState(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('headers-list');
  const [settings, setSettings] = useState({
    enableGzip: true,
    enableBrotli: false,
    enableBrowserCache: true,
    imageCacheDuration: '1m',
    assetsCacheDuration: '1w',
    includeEtag: true
  });
  
  // Charger les données
  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      // Données fictives pour les en-têtes
      const mockHeaders = [
        {
          id: 1,
          name: 'X-Frame-Options',
          value: 'SAMEORIGIN',
          type: 'security',
          active: true,
          description: 'Empêche le site d\'être affiché dans un iframe sur d\'autres domaines'
        },
        {
          id: 2,
          name: 'X-Content-Type-Options',
          value: 'nosniff',
          type: 'security',
          active: true,
          description: 'Empêche le navigateur de deviner le type MIME'
        },
        {
          id: 3,
          name: 'Strict-Transport-Security',
          value: 'max-age=31536000; includeSubDomains',
          type: 'security',
          active: true,
          description: 'Force les connexions HTTPS'
        },
        {
          id: 4,
          name: 'Cache-Control',
          value: 'public, max-age=86400',
          type: 'cache',
          active: true,
          description: 'Contrôle la mise en cache des ressources'
        },
        {
          id: 5,
          name: 'Access-Control-Allow-Origin',
          value: 'https://example.com',
          type: 'cors',
          active: false,
          description: 'Autorise les requêtes cross-origin depuis example.com'
        }
      ];
      
      // Données fictives pour les en-têtes communs
      const mockCommonHeaders = [
        {
          id: 'csp',
          name: 'Content-Security-Policy',
          value: 'default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://www.google-analytics.com; style-src \'self\' \'unsafe-inline\'; img-src \'self\' data: https:; font-src \'self\' data:; connect-src \'self\';',
          type: 'security',
          description: 'Politique de sécurité du contenu pour prévenir les attaques XSS'
        },
        {
          id: 'xss',
          name: 'X-XSS-Protection',
          value: '1; mode=block',
          type: 'security',
          description: 'Protection contre les attaques XSS'
        },
        {
          id: 'referrer',
          name: 'Referrer-Policy',
          value: 'strict-origin-when-cross-origin',
          type: 'security',
          description: 'Contrôle les informations de référent envoyées lors de la navigation'
        },
        {
          id: 'feature',
          name: 'Feature-Policy',
          value: 'camera \'none\'; microphone \'none\'; geolocation \'self\'',
          type: 'security',
          description: 'Contrôle les fonctionnalités du navigateur que le site peut utiliser'
        },
        {
          id: 'vary',
          name: 'Vary',
          value: 'Accept-Encoding, User-Agent',
          type: 'cache',
          description: 'Indique comment faire correspondre les futures requêtes pour décider si une réponse mise en cache peut être utilisée'
        }
      ];
      
      // Données fictives pour les URLs de préconnexion
      const mockPreconnectUrls = [
        { url: 'https://fonts.googleapis.com', type: 'preconnect' },
        { url: 'https://fonts.gstatic.com', type: 'preconnect' },
        { url: 'https://www.google-analytics.com', type: 'dns-prefetch' },
        { url: 'https://example.com/main.css', type: 'preload' }
      ];
      
      setHeaders(mockHeaders);
      setCommonHeaders(mockCommonHeaders);
      setPreconnectUrls(mockPreconnectUrls);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Fonction pour ajouter ou mettre à jour un en-tête
  const handleSaveHeader = (headerData) => {
    setIsSaving(true);
    
    // Simuler l'enregistrement
    setTimeout(() => {
      if (headerData.id) {
        // Mise à jour d'un en-tête existant
        const updatedHeaders = headers.map(header => {
          if (header.id === headerData.id) {
            return headerData;
          }
          return header;
        });
        
        setHeaders(updatedHeaders);
        setSuccessMessage(__('En-tête mis à jour avec succès !', 'boss-seo'));
      } else {
        // Ajout d'un nouvel en-tête
        const newHeader = {
          ...headerData,
          id: headers.length > 0 ? Math.max(...headers.map(h => h.id)) + 1 : 1
        };
        
        setHeaders([...headers, newHeader]);
        setSuccessMessage(__('En-tête ajouté avec succès !', 'boss-seo'));
      }
      
      setEditingHeader(null);
      setIsSaving(false);
      setShowSuccess(true);
      setActiveTab('headers-list');
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };
  
  // Fonction pour supprimer un en-tête
  const handleDeleteHeader = (id) => {
    setIsSaving(true);
    
    // Simuler la suppression
    setTimeout(() => {
      const updatedHeaders = headers.filter(header => header.id !== id);
      setHeaders(updatedHeaders);
      setIsSaving(false);
      setSuccessMessage(__('En-tête supprimé avec succès !', 'boss-seo'));
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };
  
  // Fonction pour activer/désactiver un en-tête
  const handleToggleHeader = (id) => {
    setIsSaving(true);
    
    // Simuler la mise à jour
    setTimeout(() => {
      const updatedHeaders = headers.map(header => {
        if (header.id === id) {
          return {
            ...header,
            active: !header.active
          };
        }
        return header;
      });
      
      setHeaders(updatedHeaders);
      setIsSaving(false);
      setSuccessMessage(__('Statut de l\'en-tête mis à jour avec succès !', 'boss-seo'));
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };
  
  // Fonction pour exécuter un test d'en-têtes
  const handleRunTest = (url) => {
    setIsRunningTest(true);
    setTestResults(null);
    
    // Simuler le test
    setTimeout(() => {
      // Générer des résultats de test fictifs
      const mockResults = {
        url: url,
        statusCode: 200,
        responseTime: Math.floor(Math.random() * 500) + 100, // 100-600ms
        headers: [
          {
            name: 'X-Frame-Options',
            value: 'SAMEORIGIN',
            status: 'success',
            statusText: __('Correctement configuré', 'boss-seo')
          },
          {
            name: 'X-Content-Type-Options',
            value: 'nosniff',
            status: 'success',
            statusText: __('Correctement configuré', 'boss-seo')
          },
          {
            name: 'Strict-Transport-Security',
            value: 'max-age=31536000',
            status: 'warning',
            statusText: __('Manque includeSubDomains', 'boss-seo')
          },
          {
            name: 'Content-Security-Policy',
            value: 'default-src \'self\'',
            status: 'warning',
            statusText: __('Configuration basique', 'boss-seo')
          },
          {
            name: 'Cache-Control',
            value: 'public, max-age=86400',
            status: 'success',
            statusText: __('Correctement configuré', 'boss-seo')
          },
          {
            name: 'Server',
            value: 'Apache/2.4.41',
            status: 'error',
            statusText: __('Divulgue des informations', 'boss-seo')
          }
        ],
        recommendations: [
          {
            title: __('Masquer les informations du serveur', 'boss-seo'),
            description: __('L\'en-tête "Server" divulgue des informations sur votre serveur web qui pourraient être utilisées par des attaquants.', 'boss-seo'),
            suggestion: 'ServerTokens Prod\nServerSignature Off',
            priority: 'high'
          },
          {
            title: __('Améliorer HSTS', 'boss-seo'),
            description: __('Ajoutez "includeSubDomains" à votre en-tête Strict-Transport-Security pour protéger également tous les sous-domaines.', 'boss-seo'),
            suggestion: 'Strict-Transport-Security: max-age=31536000; includeSubDomains',
            priority: 'medium'
          },
          {
            title: __('Renforcer la politique CSP', 'boss-seo'),
            description: __('Votre politique de sécurité du contenu est basique. Envisagez de la renforcer pour une meilleure protection.', 'boss-seo'),
            suggestion: 'Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://www.google-analytics.com; style-src \'self\' \'unsafe-inline\'; img-src \'self\' data: https:; font-src \'self\' data:; connect-src \'self\';',
            priority: 'low'
          }
        ]
      };
      
      setTestResults(mockResults);
      setIsRunningTest(false);
    }, 2000);
  };
  
  // Fonction pour ajouter une URL de préconnexion
  const handleAddPreconnect = (url, type) => {
    setPreconnectUrls([...preconnectUrls, { url, type }]);
  };
  
  // Fonction pour supprimer une URL de préconnexion
  const handleRemovePreconnect = (index) => {
    const updatedUrls = [...preconnectUrls];
    updatedUrls.splice(index, 1);
    setPreconnectUrls(updatedUrls);
  };
  
  // Fonction pour enregistrer les paramètres
  const handleSaveSettings = (newSettings) => {
    setIsSaving(true);
    
    // Simuler l'enregistrement
    setTimeout(() => {
      setSettings(newSettings);
      setIsSaving(false);
      setSuccessMessage(__('Paramètres enregistrés avec succès !', 'boss-seo'));
      setShowSuccess(true);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {successMessage}
            </Notice>
          )}
          
          <TabPanel
            className="boss-mb-6"
            activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
            onSelect={(tabName) => setActiveTab(tabName)}
            tabs={[
              {
                name: 'headers-list',
                title: __('En-têtes HTTP', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'add-header',
                title: editingHeader 
                  ? __('Modifier l\'en-tête', 'boss-seo') 
                  : __('Ajouter un en-tête', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'preconnect',
                title: __('Préconnexion & Cache', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'test',
                title: __('Tester', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              }
            ]}
          >
            {(tab) => {
              if (tab.name === 'headers-list') {
                return (
                  <HeadersList
                    headers={headers}
                    onEdit={(header) => {
                      setEditingHeader(header);
                      setActiveTab('add-header');
                    }}
                    onDelete={handleDeleteHeader}
                    onToggle={handleToggleHeader}
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                  />
                );
              } else if (tab.name === 'add-header') {
                return (
                  <HeaderForm
                    editingHeader={editingHeader}
                    onSave={handleSaveHeader}
                    onCancel={() => {
                      setEditingHeader(null);
                      setActiveTab('headers-list');
                    }}
                    commonHeaders={commonHeaders}
                  />
                );
              } else if (tab.name === 'preconnect') {
                return (
                  <PreconnectSettings
                    preconnectUrls={preconnectUrls}
                    onAddPreconnect={handleAddPreconnect}
                    onRemovePreconnect={handleRemovePreconnect}
                    onSaveSettings={handleSaveSettings}
                    isSaving={isSaving}
                    settings={settings}
                  />
                );
              } else if (tab.name === 'test') {
                return (
                  <HeadersTest
                    onRunTest={handleRunTest}
                    isRunningTest={isRunningTest}
                    testResults={testResults}
                  />
                );
              }
            }}
          </TabPanel>
        </div>
      )}
    </div>
  );
};

export default HttpHeaders;
