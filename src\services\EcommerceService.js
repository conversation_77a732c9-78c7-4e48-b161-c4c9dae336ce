/**
 * Service pour le module E-commerce
 *
 * Gère les communications avec l'API pour les fonctionnalités e-commerce
 */

import apiFetch from '@wordpress/api-fetch';
import { addQueryArgs } from '@wordpress/url';

class EcommerceService {
  /**
   * Récupère les données du tableau de bord e-commerce
   *
   * @returns {Promise} Promesse contenant les données du tableau de bord
   */
  async getDashboardData() {
    try {
      const path = '/boss-seo/v1/ecommerce/dashboard';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des données du tableau de bord e-commerce:', error);
      // Retourner des données fictives en cas d'erreur
      return {
        stats: {
          total_products: 33,
          optimized_products: 17,
          critical_products: 4,
          to_improve_products: 12,
          optimized_percentage: 52,
          critical_percentage: 12,
          to_improve_percentage: 36,
        },
        recent_activity: [
          { type: 'product_optimized', product_name: 'Smartphone XYZ', date: '2023-05-10T15:30:00Z' },
          { type: 'product_added', product_name: 'T-shirt Premium', date: '2023-05-09T10:15:00Z' },
          { type: 'product_analyzed', product_name: 'Casque Audio Pro', date: '2023-05-08T14:45:00Z' },
        ],
        performance: {
          current_month: { clicks: 12500, conversions: 750, revenue: 45000 },
          previous_month: { clicks: 11000, conversions: 680, revenue: 41000 },
          growth: { clicks: 13.6, conversions: 10.3, revenue: 9.8 }
        }
      };
    }
  }

  /**
   * Récupère les statistiques du tableau de bord e-commerce
   *
   * @param {string} period - Période (7days, 30days, 90days, 1year)
   * @returns {Promise} Promesse contenant les statistiques du tableau de bord
   */
  async getDashboardStats(period = '30days') {
    try {
      const path = addQueryArgs('/boss-seo/v1/ecommerce/dashboard/stats', { period });
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques du tableau de bord e-commerce:', error);
      throw error;
    }
  }

  /**
   * Récupère les produits les plus performants
   *
   * @param {string} period - Période (7days, 30days, 90days, 1year)
   * @returns {Promise} Promesse contenant les produits les plus performants
   */
  async getTopProducts(period = '30days') {
    try {
      const path = addQueryArgs('/boss-seo/v1/ecommerce/dashboard/top-products', { period });
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des produits les plus performants:', error);
      // Retourner des données fictives en cas d'erreur
      return {
        products: [
          { id: 1, name: 'Smartphone XYZ', category: 'Électronique', score: 92, clicks: 1250, conversions: 78 },
          { id: 2, name: 'T-shirt Premium', category: 'Vêtements', score: 88, clicks: 980, conversions: 65 },
          { id: 3, name: 'Casque Audio Pro', category: 'Électronique', score: 85, clicks: 870, conversions: 52 },
          { id: 4, name: 'Chaussures de Sport', category: 'Sports', score: 83, clicks: 760, conversions: 48 },
          { id: 5, name: 'Lampe Design', category: 'Maison', score: 80, clicks: 650, conversions: 41 }
        ]
      };
    }
  }

  /**
   * Récupère les catégories les plus performantes
   *
   * @param {string} period - Période (7days, 30days, 90days, 1year)
   * @returns {Promise} Promesse contenant les catégories les plus performantes
   */
  async getTopCategories(period = '30days') {
    try {
      const path = addQueryArgs('/boss-seo/v1/ecommerce/dashboard/top-categories', { period });
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des catégories les plus performantes:', error);
      // Retourner des données fictives en cas d'erreur
      return {
        categories: [
          { name: 'Électronique', clicks: 3850, conversions: 245, conversionRate: 6.36 },
          { name: 'Vêtements', clicks: 3200, conversions: 210, conversionRate: 6.56 },
          { name: 'Sports', clicks: 1950, conversions: 135, conversionRate: 6.92 },
          { name: 'Maison', clicks: 1650, conversions: 98, conversionRate: 5.94 },
          { name: 'Livres', clicks: 980, conversions: 52, conversionRate: 5.31 }
        ]
      };
    }
  }

  /**
   * Récupère la liste des produits
   *
   * @param {Object} filters - Filtres pour la requête
   * @returns {Promise} Promesse contenant la liste des produits ou une erreur
   */
  async getProducts(filters = {}) {
    try {
      const path = addQueryArgs('/boss-seo/v1/ecommerce/products', filters);
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des produits:', error);

      // Vérifier si c'est une erreur spécifique de WooCommerce
      if (error && error.code) {
        if (error.code === 'woocommerce_not_available') {
          // WooCommerce n'est pas installé ou activé
          throw {
            code: 'woocommerce_not_available',
            message: error.message || __('WooCommerce n\'est pas installé ou activé.', 'boss-seo')
          };
        } else if (error.code === 'no_products_available') {
          // Aucun produit n'est disponible
          throw {
            code: 'no_products_available',
            message: error.message || __('Aucun produit n\'est disponible dans WooCommerce.', 'boss-seo')
          };
        }
      }

      // Pour les autres erreurs, propager l'erreur
      throw error;
    }
  }

  /**
   * Récupère un produit spécifique
   *
   * @param {number} id - ID du produit
   * @returns {Promise} Promesse contenant les détails du produit
   */
  async getProduct(id) {
    try {
      const path = `/boss-seo/v1/ecommerce/products/${id}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération du produit ${id}:`, error);

      // Retourner des données fictives en cas d'erreur
      const categories = [
        'Électronique',
        'Vêtements',
        'Maison',
        'Sports',
        'Livres'
      ];

      const category = categories[Math.floor(Math.random() * categories.length)];
      const score = Math.floor(Math.random() * 60) + 40; // 40-100
      let status;

      if (score >= 80) {
        status = 'optimized';
      } else if (score >= 60) {
        status = 'needs_attention';
      } else {
        status = 'critical';
      }

      // Générer les problèmes en fonction du statut
      const issues = [];

      if (status === 'needs_attention' || status === 'critical') {
        if (Math.random() < 0.7) issues.push('title_too_short');
        if (Math.random() < 0.6) issues.push('missing_meta_description');
        if (Math.random() < 0.5) issues.push('no_alt_text');
        if (Math.random() < 0.4) issues.push('duplicate_content');
        if (Math.random() < 0.3) issues.push('missing_schema');
      }

      return {
        product: {
          id: parseInt(id),
          name: `Produit ${id}`,
          category: category,
          categoryId: categories.indexOf(category) + 1,
          price: (Math.random() * 100 + 10).toFixed(2),
          stock: Math.floor(Math.random() * 100),
          score: score,
          status: status,
          issues: issues,
          hasSchema: Math.random() < 0.6,
          lastUpdated: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          description: 'Description du produit générée automatiquement pour le développement.',
          images: [
            { id: 1, url: 'https://via.placeholder.com/600x400', alt: 'Image du produit' }
          ],
          meta: {
            title: `Produit ${id} - Titre SEO`,
            description: 'Meta description du produit pour le référencement.',
            keywords: 'produit, exemple, développement'
          }
        }
      };
    }
  }

  /**
   * Met à jour un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} productData - Données du produit
   * @returns {Promise} Promesse contenant les détails du produit mis à jour
   */
  async updateProduct(id, productData) {
    try {
      const path = `/boss-seo/v1/ecommerce/products/${id}`;
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { product: productData }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Optimise un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} optimizationSettings - Paramètres d'optimisation
   * @returns {Promise} Promesse contenant les détails du produit optimisé
   */
  async optimizeProduct(id, optimizationSettings) {
    try {
      const path = '/boss-seo/v1/ecommerce/optimize-product';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_id: id,
          settings: optimizationSettings
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'optimisation du produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Optimise plusieurs produits en masse
   *
   * @param {Array} ids - IDs des produits
   * @param {Object} optimizationSettings - Paramètres d'optimisation
   * @returns {Promise} Promesse contenant les détails des produits optimisés
   */
  async bulkOptimizeProducts(ids, optimizationSettings) {
    try {
      const path = '/boss-seo/v1/ecommerce/bulk-optimize-products';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_ids: ids,
          settings: optimizationSettings
        }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'optimisation en masse des produits:', error);
      throw error;
    }
  }

  /**
   * Analyse un produit
   *
   * @param {number} id - ID du produit
   * @returns {Promise} Promesse contenant les résultats de l'analyse
   */
  async analyzeProduct(id) {
    try {
      const path = '/boss-seo/v1/ecommerce/analyze-product';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { product_id: id }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'analyse du produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère l'analyse d'un produit
   *
   * @param {number} id - ID du produit
   * @returns {Promise} Promesse contenant l'analyse du produit
   */
  async getProductAnalysis(id) {
    try {
      const path = `/boss-seo/v1/ecommerce/product-analysis/${id}`;
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'analyse du produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Génère une description de produit avec l'IA
   *
   * @param {number} id - ID du produit
   * @param {Object} generationSettings - Paramètres de génération
   * @returns {Promise} Promesse contenant la description générée
   */
  async generateProductDescription(id, generationSettings) {
    try {
      const path = '/boss-seo/v1/ecommerce/generate-description';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_id: id,
          settings: generationSettings
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la génération de la description du produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Applique la description générée à un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} content - Contenu généré (titre, description, etc.)
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async applyGeneratedContent(id, content) {
    try {
      const path = '/boss-seo/v1/ecommerce/apply-generated-content';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_id: id,
          content
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'application du contenu généré au produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Génère un schéma pour un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} schemaSettings - Paramètres du schéma
   * @returns {Promise} Promesse contenant le schéma généré
   */
  async generateProductSchema(id, schemaSettings) {
    try {
      const path = '/boss-seo/v1/ecommerce/generate-schema';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_id: id,
          settings: schemaSettings
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la génération du schéma pour le produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Enregistre un schéma pour un produit
   *
   * @param {number} id - ID du produit
   * @param {Object} schema - Schéma à enregistrer
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async saveProductSchema(id, schema) {
    try {
      const path = '/boss-seo/v1/ecommerce/save-schema';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: {
          product_id: id,
          schema
        }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de l'enregistrement du schéma pour le produit ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les flux Google Shopping
   *
   * @returns {Promise} Promesse contenant la liste des flux
   */
  async getShoppingFeeds() {
    try {
      const path = '/boss-seo/v1/ecommerce/shopping-feeds';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des flux Google Shopping:', error);
      throw error;
    }
  }

  /**
   * Crée un nouveau flux Google Shopping
   *
   * @param {Object} feedData - Données du flux
   * @returns {Promise} Promesse contenant les détails du flux créé
   */
  async createShoppingFeed(feedData) {
    try {
      const path = '/boss-seo/v1/ecommerce/shopping-feeds';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { feed: feedData }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la création du flux Google Shopping:', error);
      throw error;
    }
  }

  /**
   * Met à jour un flux Google Shopping
   *
   * @param {number} id - ID du flux
   * @param {Object} feedData - Données du flux
   * @returns {Promise} Promesse contenant les détails du flux mis à jour
   */
  async updateShoppingFeed(id, feedData) {
    try {
      const path = `/boss-seo/v1/ecommerce/shopping-feeds/${id}`;
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { feed: feedData }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du flux Google Shopping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Supprime un flux Google Shopping
   *
   * @param {number} id - ID du flux
   * @returns {Promise} Promesse contenant le résultat de la suppression
   */
  async deleteShoppingFeed(id) {
    try {
      const path = `/boss-seo/v1/ecommerce/shopping-feeds/${id}`;
      const response = await apiFetch({
        path,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la suppression du flux Google Shopping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Génère un flux Google Shopping
   *
   * @param {number} id - ID du flux
   * @returns {Promise} Promesse contenant le résultat de la génération
   */
  async generateShoppingFeed(id) {
    try {
      const path = '/boss-seo/v1/ecommerce/generate-shopping-feed';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { feed_id: id }
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la génération du flux Google Shopping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres du module e-commerce
   *
   * @returns {Promise} Promesse contenant les paramètres du module
   */
  async getEcommerceSettings() {
    try {
      const path = '/boss-seo/v1/ecommerce/settings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres du module e-commerce:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres du module e-commerce
   *
   * @param {Object} settings - Paramètres du module
   * @returns {Promise} Promesse contenant le résultat de l'enregistrement
   */
  async saveEcommerceSettings(settings) {
    try {
      const path = '/boss-seo/v1/ecommerce/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: settings
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres du module e-commerce:', error);
      throw error;
    }
  }
}

export default EcommerceService;
