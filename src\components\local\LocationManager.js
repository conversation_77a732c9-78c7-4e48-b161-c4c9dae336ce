import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Dashicon,
  TextControl,
  SelectControl,
  ToggleControl,
  Modal,
  Spinner,
  Notice
} from '@wordpress/components';

// Importer le service
import LocalSeoService from '../../services/LocalSeoService';

const LocationManager = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [locations, setLocations] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [newLocation, setNewLocation] = useState({
    name: '',
    address: '',
    city: '',
    postalCode: '',
    country: 'FR',
    phone: '',
    website: '',
    status: 'pending',
    openingHours: '',
    description: '',
    primaryCategory: '',
    additionalCategories: []
  });

  // Créer une instance du service
  const localSeoService = new LocalSeoService();

  // Charger les données
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Récupérer les emplacements
        const response = await localSeoService.getLocations();

        // Mettre à jour l'état
        setLocations(response.locations || []);
      } catch (err) {
        console.error('Erreur lors du chargement des emplacements:', err);
        setError(__('Erreur lors du chargement des emplacements. Veuillez réessayer.', 'boss-seo'));

        // Utiliser des données fictives en cas d'erreur pour le développement
        // À supprimer en production
        const mockLocations = [
          {
            id: 1,
            name: 'Paris - Siège social',
            address: '123 Avenue des Champs-Élysées',
            city: 'Paris',
            postalCode: '75008',
            country: 'FR',
            phone: '01 23 45 67 89',
            website: 'https://example.com/paris',
            status: 'verified',
            score: 87,
            openingHours: 'Lun-Ven: 9h-18h, Sam: 10h-17h',
            description: 'Notre siège social au cœur de Paris',
            primaryCategory: 'store',
            additionalCategories: ['service'],
            latitude: 48.8698,
            longitude: 2.3075,
            lastUpdated: '2023-06-15'
          },
          {
            id: 2,
            name: 'Lyon - Succursale',
            address: '45 Rue de la République',
            city: 'Lyon',
            postalCode: '69002',
            country: 'FR',
            phone: '04 78 12 34 56',
            website: 'https://example.com/lyon',
            status: 'verified',
            score: 72,
            openingHours: 'Lun-Ven: 9h-18h, Sam: 10h-16h',
            description: 'Notre boutique au centre de Lyon',
            primaryCategory: 'store',
            additionalCategories: [],
            latitude: 45.7640,
            longitude: 4.8357,
            lastUpdated: '2023-06-10'
          },
          {
            id: 3,
            name: 'Marseille - Boutique',
            address: '78 La Canebière',
            city: 'Marseille',
            postalCode: '13001',
            country: 'FR',
            phone: '04 91 23 45 67',
            website: 'https://example.com/marseille',
            status: 'pending',
            score: 65,
            openingHours: 'Lun-Sam: 10h-19h',
            description: 'Notre nouvelle boutique à Marseille',
            primaryCategory: 'store',
            additionalCategories: ['service'],
            latitude: 43.2965,
            longitude: 5.3698,
            lastUpdated: '2023-06-05'
          }
        ];

        setLocations(mockLocations);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLocations();
  }, []);

  // Fonction pour filtrer les emplacements
  const getFilteredLocations = () => {
    return locations.filter(location => {
      // Filtrer par recherche
      const matchesSearch =
        location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.city.toLowerCase().includes(searchQuery.toLowerCase());

      // Filtrer par statut
      const matchesStatus =
        filterStatus === 'all' ||
        location.status === filterStatus;

      return matchesSearch && matchesStatus;
    });
  };

  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 80) return 'boss-text-green-600';
    if (score >= 60) return 'boss-text-yellow-600';
    return 'boss-text-red-600';
  };

  // Fonction pour ajouter un nouvel emplacement
  const handleAddLocation = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Préparer les données de l'emplacement
      const locationToAdd = {
        ...newLocation,
        score: 50, // Score initial
        latitude: 48.8566, // Coordonnées par défaut (Paris)
        longitude: 2.3522,
        lastUpdated: new Date().toISOString().split('T')[0]
      };

      // Appeler le service pour créer l'emplacement
      const response = await localSeoService.createLocation(locationToAdd);

      // Vérifier si la réponse contient une erreur
      if (response && response.success === false) {
        console.error('Erreur lors de l\'ajout de l\'emplacement:', response.message);
        setError(response.message || __('Erreur lors de l\'ajout de l\'emplacement. Veuillez réessayer.', 'boss-seo'));

        // Utiliser des données simulées pour le développement
        const newId = locations.length > 0 ? Math.max(...locations.map(loc => loc.id)) + 1 : 1;

        const mockLocation = {
          ...newLocation,
          id: newId,
          score: 50,
          latitude: 48.8566,
          longitude: 2.3522,
          lastUpdated: new Date().toISOString().split('T')[0]
        };

        setLocations([...locations, mockLocation]);
        setShowAddModal(false);
      } else {
        // Succès - Mettre à jour la liste des emplacements
        if (response && response.location) {
          setLocations([...locations, response.location]);
        } else {
          // Fallback si la réponse n'a pas le format attendu
          const newId = locations.length > 0 ? Math.max(...locations.map(loc => loc.id)) + 1 : 1;
          const mockLocation = {
            ...newLocation,
            id: newId,
            score: 50,
            latitude: 48.8566,
            longitude: 2.3522,
            lastUpdated: new Date().toISOString().split('T')[0]
          };
          setLocations([...locations, mockLocation]);
        }

        // Réinitialiser le formulaire
        setShowAddModal(false);
        setNewLocation({
          name: '',
          address: '',
          city: '',
          postalCode: '',
          country: 'FR',
          phone: '',
          website: '',
          status: 'pending',
          openingHours: '',
          description: '',
          primaryCategory: '',
          additionalCategories: []
        });
      }
    } catch (err) {
      console.error('Erreur lors de l\'ajout de l\'emplacement:', err);
      setError(__('Erreur lors de l\'ajout de l\'emplacement. Veuillez réessayer.', 'boss-seo'));

      // Simuler l'ajout en cas d'erreur (pour le développement)
      const newId = locations.length > 0 ? Math.max(...locations.map(loc => loc.id)) + 1 : 1;

      const mockLocation = {
        ...newLocation,
        id: newId,
        score: 50,
        latitude: 48.8566,
        longitude: 2.3522,
        lastUpdated: new Date().toISOString().split('T')[0]
      };

      setLocations([...locations, mockLocation]);
      setShowAddModal(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour mettre à jour un emplacement
  const handleUpdateLocation = async () => {
    if (!selectedLocation) return;

    try {
      setIsLoading(true);
      setError(null);

      // Appeler le service pour mettre à jour l'emplacement
      const response = await localSeoService.updateLocation(selectedLocation.id, selectedLocation);

      // Mettre à jour la liste des emplacements
      const updatedLocations = locations.map(location =>
        location.id === selectedLocation.id ? response.location : location
      );

      setLocations(updatedLocations);
      setShowEditModal(false);
      setSelectedLocation(null);
    } catch (err) {
      console.error('Erreur lors de la mise à jour de l\'emplacement:', err);
      setError(__('Erreur lors de la mise à jour de l\'emplacement. Veuillez réessayer.', 'boss-seo'));

      // Simuler la mise à jour en cas d'erreur (pour le développement)
      const updatedLocations = locations.map(location =>
        location.id === selectedLocation.id ? selectedLocation : location
      );

      setLocations(updatedLocations);
      setShowEditModal(false);
      setSelectedLocation(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour supprimer un emplacement
  const handleDeleteLocation = async () => {
    if (!selectedLocation) return;

    try {
      setIsLoading(true);
      setError(null);

      // Appeler le service pour supprimer l'emplacement
      await localSeoService.deleteLocation(selectedLocation.id);

      // Mettre à jour la liste des emplacements
      const updatedLocations = locations.filter(location =>
        location.id !== selectedLocation.id
      );

      setLocations(updatedLocations);
      setShowDeleteConfirm(false);
      setSelectedLocation(null);
    } catch (err) {
      console.error('Erreur lors de la suppression de l\'emplacement:', err);
      setError(__('Erreur lors de la suppression de l\'emplacement. Veuillez réessayer.', 'boss-seo'));

      // Simuler la suppression en cas d'erreur (pour le développement)
      const updatedLocations = locations.filter(location =>
        location.id !== selectedLocation.id
      );

      setLocations(updatedLocations);
      setShowDeleteConfirm(false);
      setSelectedLocation(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour éditer un emplacement
  const handleEditLocation = (location) => {
    setSelectedLocation(location);
    setShowEditModal(true);
  };

  // Fonction pour confirmer la suppression
  const confirmDeleteLocation = (location) => {
    setSelectedLocation(location);
    setShowDeleteConfirm(true);
  };

  // Obtenir les emplacements filtrés
  const filteredLocations = getFilteredLocations();

  return (
    <div>
      {error && (
        <Notice status="error" isDismissible={false} className="boss-mb-4">
          {error}
        </Notice>
      )}

      {/* En-tête avec actions */}
      <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start md:boss-items-center boss-mb-6 boss-gap-4">
        <div>
          <h2 className="boss-text-xl boss-font-semibold boss-text-boss-dark boss-mb-1">
            {__('Gestion des emplacements', 'boss-seo')}
          </h2>
          <p className="boss-text-boss-gray">
            {__('Gérez vos emplacements physiques pour le référencement local', 'boss-seo')}
          </p>
        </div>

        <Button
          isPrimary
          onClick={() => setShowAddModal(true)}
        >
          <Dashicon icon="plus" className="boss-mr-1" />
          {__('Ajouter un emplacement', 'boss-seo')}
        </Button>
      </div>

      {/* Filtres */}
      <Card className="boss-mb-6">
        <CardBody>
          <div className="boss-flex boss-flex-col md:boss-flex-row boss-gap-4">
            <TextControl
              placeholder={__('Rechercher un emplacement...', 'boss-seo')}
              value={searchQuery}
              onChange={setSearchQuery}
              className="boss-flex-1"
            />

            <SelectControl
              label=""
              value={filterStatus}
              options={[
                { label: __('Tous les statuts', 'boss-seo'), value: 'all' },
                { label: __('Vérifiés', 'boss-seo'), value: 'verified' },
                { label: __('En attente', 'boss-seo'), value: 'pending' }
              ]}
              onChange={setFilterStatus}
              className="boss-w-48"
            />
          </div>
        </CardBody>
      </Card>

      {/* Carte interactive (simulée) */}
      <Card className="boss-mb-6">
        <CardHeader className="boss-border-b boss-border-gray-200">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Carte des emplacements', 'boss-seo')}
          </h2>
        </CardHeader>
        <CardBody className="boss-p-0">
          <div className="boss-bg-gray-100 boss-h-80 boss-flex boss-items-center boss-justify-center boss-relative">
            {/* Simuler une carte interactive */}
            <div className="boss-text-center boss-text-boss-gray">
              <Dashicon icon="location" className="boss-text-5xl boss-mb-2" />
              <p>{__('Carte interactive des emplacements', 'boss-seo')}</p>
              <p className="boss-text-sm">{__('(Intégration Google Maps)', 'boss-seo')}</p>
            </div>

            {/* Marqueurs simulés */}
            {locations.map((location, index) => (
              <div
                key={index}
                className="boss-absolute boss-w-6 boss-h-6 boss-rounded-full boss-bg-boss-primary boss-text-white boss-flex boss-items-center boss-justify-center boss-text-xs boss-font-bold boss-cursor-pointer boss-shadow-md"
                style={{
                  left: `${(location.longitude + 180) / 360 * 100}%`,
                  top: `${(90 - location.latitude) / 180 * 100}%`,
                  transform: 'translate(-50%, -50%)'
                }}
                title={location.name}
              >
                {index + 1}
              </div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Liste des emplacements */}
      <Card>
        <CardHeader className="boss-border-b boss-border-gray-200">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Liste des emplacements', 'boss-seo')}
          </h2>
        </CardHeader>
        <CardBody className="boss-p-0">
          {isLoading ? (
            <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
              <Spinner />
            </div>
          ) : filteredLocations.length === 0 ? (
            <div className="boss-text-center boss-py-8 boss-text-boss-gray">
              {__('Aucun emplacement trouvé.', 'boss-seo')}
            </div>
          ) : (
            <div className="boss-divide-y boss-divide-gray-200">
              {filteredLocations.map(location => (
                <div key={location.id} className="boss-p-4 boss-hover:boss-bg-gray-50">
                  <div className="boss-flex boss-flex-col md:boss-flex-row boss-justify-between boss-items-start boss-gap-4">
                    <div>
                      <div className="boss-flex boss-items-center boss-mb-1">
                        <h3 className="boss-font-medium boss-text-boss-dark boss-mr-2">
                          {location.name}
                        </h3>
                        <span className={`boss-px-2 boss-py-1 boss-text-xs boss-rounded-full ${
                          location.status === 'verified'
                            ? 'boss-bg-green-100 boss-text-green-800'
                            : 'boss-bg-yellow-100 boss-text-yellow-800'
                        }`}>
                          {location.status === 'verified'
                            ? __('Vérifié', 'boss-seo')
                            : __('En attente', 'boss-seo')}
                        </span>
                      </div>
                      <p className="boss-text-sm boss-text-boss-gray boss-mb-2">
                        {location.address}, {location.postalCode} {location.city}
                      </p>
                      <div className="boss-flex boss-flex-wrap boss-gap-4 boss-text-sm boss-text-boss-gray">
                        <div className="boss-flex boss-items-center">
                          <Dashicon icon="phone" className="boss-mr-1" />
                          {location.phone}
                        </div>
                        <div className="boss-flex boss-items-center">
                          <Dashicon icon="admin-site" className="boss-mr-1" />
                          {location.website}
                        </div>
                        <div className="boss-flex boss-items-center">
                          <Dashicon icon="calendar" className="boss-mr-1" />
                          {__('Mis à jour le', 'boss-seo')} {location.lastUpdated}
                        </div>
                      </div>
                    </div>
                    <div className="boss-flex boss-items-center boss-gap-4">
                      <div className="boss-text-center">
                        <div className={`boss-text-2xl boss-font-bold ${getScoreColorClass(location.score)}`}>
                          {location.score}
                        </div>
                        <div className="boss-text-xs boss-text-boss-gray">
                          {__('Score SEO', 'boss-seo')}
                        </div>
                      </div>
                      <div className="boss-flex boss-space-x-2">
                        <Button
                          isSecondary
                          isSmall
                          onClick={() => handleEditLocation(location)}
                        >
                          {__('Éditer', 'boss-seo')}
                        </Button>
                        <Button
                          isDestructive
                          isSmall
                          onClick={() => confirmDeleteLocation(location)}
                        >
                          {__('Supprimer', 'boss-seo')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Modal d'ajout d'emplacement */}
      {showAddModal && (
        <Modal
          title={__('Ajouter un nouvel emplacement', 'boss-seo')}
          onRequestClose={() => setShowAddModal(false)}
          className="boss-location-modal"
        >
          <div className="boss-p-6">
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-6">
              <TextControl
                label={__('Nom de l\'emplacement', 'boss-seo')}
                value={newLocation.name}
                onChange={(value) => setNewLocation({ ...newLocation, name: value })}
              />

              <SelectControl
                label={__('Catégorie principale', 'boss-seo')}
                value={newLocation.primaryCategory}
                options={[
                  { label: __('-- Sélectionner --', 'boss-seo'), value: '' },
                  { label: __('Magasin', 'boss-seo'), value: 'store' },
                  { label: __('Restaurant', 'boss-seo'), value: 'restaurant' },
                  { label: __('Service', 'boss-seo'), value: 'service' },
                  { label: __('Bureau', 'boss-seo'), value: 'office' }
                ]}
                onChange={(value) => setNewLocation({ ...newLocation, primaryCategory: value })}
              />

              <TextControl
                label={__('Adresse', 'boss-seo')}
                value={newLocation.address}
                onChange={(value) => setNewLocation({ ...newLocation, address: value })}
              />

              <TextControl
                label={__('Ville', 'boss-seo')}
                value={newLocation.city}
                onChange={(value) => setNewLocation({ ...newLocation, city: value })}
              />

              <TextControl
                label={__('Code postal', 'boss-seo')}
                value={newLocation.postalCode}
                onChange={(value) => setNewLocation({ ...newLocation, postalCode: value })}
              />

              <SelectControl
                label={__('Pays', 'boss-seo')}
                value={newLocation.country}
                options={[
                  { label: 'France', value: 'FR' },
                  { label: 'Belgique', value: 'BE' },
                  { label: 'Suisse', value: 'CH' },
                  { label: 'Canada', value: 'CA' }
                ]}
                onChange={(value) => setNewLocation({ ...newLocation, country: value })}
              />

              <TextControl
                label={__('Téléphone', 'boss-seo')}
                value={newLocation.phone}
                onChange={(value) => setNewLocation({ ...newLocation, phone: value })}
              />

              <TextControl
                label={__('Site web', 'boss-seo')}
                value={newLocation.website}
                onChange={(value) => setNewLocation({ ...newLocation, website: value })}
              />

              <div className="boss-col-span-full">
                <TextControl
                  label={__('Horaires d\'ouverture', 'boss-seo')}
                  value={newLocation.openingHours}
                  onChange={(value) => setNewLocation({ ...newLocation, openingHours: value })}
                />
              </div>

              <div className="boss-col-span-full">
                <TextControl
                  label={__('Description', 'boss-seo')}
                  value={newLocation.description}
                  onChange={(value) => setNewLocation({ ...newLocation, description: value })}
                />
              </div>
            </div>

            <div className="boss-flex boss-justify-end boss-space-x-3">
              <Button
                isSecondary
                onClick={() => setShowAddModal(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                onClick={handleAddLocation}
                disabled={!newLocation.name || !newLocation.address || !newLocation.city}
              >
                {__('Ajouter', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Modal d'édition d'emplacement */}
      {showEditModal && selectedLocation && (
        <Modal
          title={__('Modifier l\'emplacement', 'boss-seo')}
          onRequestClose={() => setShowEditModal(false)}
          className="boss-location-modal"
        >
          <div className="boss-p-6">
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-6">
              <TextControl
                label={__('Nom de l\'emplacement', 'boss-seo')}
                value={selectedLocation.name}
                onChange={(value) => setSelectedLocation({ ...selectedLocation, name: value })}
              />

              <SelectControl
                label={__('Catégorie principale', 'boss-seo')}
                value={selectedLocation.primaryCategory}
                options={[
                  { label: __('-- Sélectionner --', 'boss-seo'), value: '' },
                  { label: __('Magasin', 'boss-seo'), value: 'store' },
                  { label: __('Restaurant', 'boss-seo'), value: 'restaurant' },
                  { label: __('Service', 'boss-seo'), value: 'service' },
                  { label: __('Bureau', 'boss-seo'), value: 'office' }
                ]}
                onChange={(value) => setSelectedLocation({ ...selectedLocation, primaryCategory: value })}
              />

              <TextControl
                label={__('Adresse', 'boss-seo')}
                value={selectedLocation.address}
                onChange={(value) => setSelectedLocation({ ...selectedLocation, address: value })}
              />

              <TextControl
                label={__('Ville', 'boss-seo')}
                value={selectedLocation.city}
                onChange={(value) => setSelectedLocation({ ...selectedLocation, city: value })}
              />

              <TextControl
                label={__('Code postal', 'boss-seo')}
                value={selectedLocation.postalCode}
                onChange={(value) => setSelectedLocation({ ...selectedLocation, postalCode: value })}
              />

              <SelectControl
                label={__('Pays', 'boss-seo')}
                value={selectedLocation.country}
                options={[
                  { label: 'France', value: 'FR' },
                  { label: 'Belgique', value: 'BE' },
                  { label: 'Suisse', value: 'CH' },
                  { label: 'Canada', value: 'CA' }
                ]}
                onChange={(value) => setSelectedLocation({ ...selectedLocation, country: value })}
              />

              <TextControl
                label={__('Téléphone', 'boss-seo')}
                value={selectedLocation.phone}
                onChange={(value) => setSelectedLocation({ ...selectedLocation, phone: value })}
              />

              <TextControl
                label={__('Site web', 'boss-seo')}
                value={selectedLocation.website}
                onChange={(value) => setSelectedLocation({ ...selectedLocation, website: value })}
              />

              <div className="boss-col-span-full">
                <TextControl
                  label={__('Horaires d\'ouverture', 'boss-seo')}
                  value={selectedLocation.openingHours}
                  onChange={(value) => setSelectedLocation({ ...selectedLocation, openingHours: value })}
                />
              </div>

              <div className="boss-col-span-full">
                <TextControl
                  label={__('Description', 'boss-seo')}
                  value={selectedLocation.description}
                  onChange={(value) => setSelectedLocation({ ...selectedLocation, description: value })}
                />
              </div>

              <div className="boss-col-span-full">
                <SelectControl
                  label={__('Statut', 'boss-seo')}
                  value={selectedLocation.status}
                  options={[
                    { label: __('En attente', 'boss-seo'), value: 'pending' },
                    { label: __('Vérifié', 'boss-seo'), value: 'verified' }
                  ]}
                  onChange={(value) => setSelectedLocation({ ...selectedLocation, status: value })}
                />
              </div>
            </div>

            <div className="boss-flex boss-justify-end boss-space-x-3">
              <Button
                isSecondary
                onClick={() => setShowEditModal(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                onClick={handleUpdateLocation}
                disabled={!selectedLocation.name || !selectedLocation.address || !selectedLocation.city}
              >
                {__('Mettre à jour', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Modal de confirmation de suppression */}
      {showDeleteConfirm && selectedLocation && (
        <Modal
          title={__('Confirmer la suppression', 'boss-seo')}
          onRequestClose={() => setShowDeleteConfirm(false)}
        >
          <div className="boss-p-6">
            <p className="boss-mb-6">
              {__('Êtes-vous sûr de vouloir supprimer l\'emplacement', 'boss-seo')} <strong>{selectedLocation.name}</strong> ?
              {__('Cette action est irréversible.', 'boss-seo')}
            </p>

            <div className="boss-flex boss-justify-end boss-space-x-3">
              <Button
                isSecondary
                onClick={() => setShowDeleteConfirm(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                isDanger
                onClick={handleDeleteLocation}
              >
                {__('Supprimer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default LocationManager;
