AIzaSyCaitVZsfUFErNjGBlLwOpaMtA0DVyxspM
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Nouveau Design Boss SEO - Démo</title>
    <link rel="stylesheet" href="admin/css/boss-seo-metabox-secure.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-header h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            margin: 0;
            font-weight: 700;
        }
        
        .demo-header p {
            color: #6b7280;
            font-size: 1.1rem;
            margin: 10px 0 0 0;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        
        .demo-section h3 {
            color: #1f2937;
            margin: 0 0 20px 0;
            font-size: 1.3rem;
        }
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .color-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .color-primary { background: #667eea; }
        .color-secondary { background: #764ba2; }
        .color-success { background: #10b981; }
        .color-warning { background: #f59e0b; }
        .color-error { background: #ef4444; }
        .color-info { background: #3b82f6; }
        
        .dashicons {
            font-family: dashicons;
            display: inline-block;
            line-height: 1;
            font-weight: normal;
            font-style: normal;
            speak: none;
            text-decoration: inherit;
            text-transform: none;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            width: 20px;
            height: 20px;
            font-size: 20px;
            vertical-align: top;
        }
        .dashicons-tag:before { content: "\f323"; }
        .dashicons-admin-appearance:before { content: "\f100"; }
        .dashicons-share:before { content: "\f237"; }
        .dashicons-admin-tools:before { content: "\f108"; }
        .dashicons-chart-bar:before { content: "\f185"; }
        .dashicons-lightbulb:before { content: "\f339"; }
        .dashicons-plus-alt2:before { content: "\f502"; }
        .dashicons-star-filled:before { content: "\f155"; }
        .dashicons-no-alt:before { content: "\f335"; }
        .dashicons-superhero:before { content: "\f308"; }
        .dashicons-saved:before { content: "\f147"; }
        
        .button {
            display: inline-block;
            text-decoration: none;
            font-size: 13px;
            line-height: 2.15384615;
            min-height: 30px;
            margin: 0;
            padding: 0 10px;
            cursor: pointer;
            border-width: 1px;
            border-style: solid;
            -webkit-appearance: none;
            border-radius: 3px;
            white-space: nowrap;
            box-sizing: border-box;
        }
        
        .button-primary {
            background: #0073aa;
            border-color: #0073aa;
            color: #fff;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            border-color: #667eea;
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎨 Nouveau Design Boss SEO</h1>
            <p>Interface moderne, colorée et interactive avec animations fluides</p>
        </div>

        <!-- Palette de couleurs -->
        <div class="demo-section">
            <h3>🌈 Nouvelle Palette de Couleurs</h3>
            <div class="color-palette">
                <div class="color-item color-primary">Primary<br>#667eea</div>
                <div class="color-item color-secondary">Secondary<br>#764ba2</div>
                <div class="color-item color-success">Success<br>#10b981</div>
                <div class="color-item color-warning">Warning<br>#f59e0b</div>
                <div class="color-item color-error">Error<br>#ef4444</div>
                <div class="color-item color-info">Info<br>#3b82f6</div>
            </div>
        </div>

        <!-- Fonctionnalités -->
        <div class="demo-section">
            <h3>✨ Nouvelles Fonctionnalités</h3>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h4>Design Moderne</h4>
                    <p>Interface colorée avec dégradés et animations fluides</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h4>Animations Fluides</h4>
                    <p>Transitions CSS3 avec courbes de Bézier optimisées</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h4>UX Améliorée</h4>
                    <p>Feedback visuel immédiat et interactions intuitives</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h4>Responsive</h4>
                    <p>Parfaitement adapté à tous les écrans</p>
                </div>
            </div>
        </div>

        <!-- Meta Box avec nouveau design -->
        <div class="demo-section">
            <h3>🚀 Meta Box Modernisée</h3>
            
            <div class="boss-seo-metabox-tabbed" data-post-id="demo">
                
                <!-- Navigation par onglets moderne -->
                <div class="boss-seo-tabs-nav" role="tablist">
                    <button type="button" class="boss-seo-tab-button active" 
                            role="tab" 
                            aria-selected="true" 
                            data-tab="keywords">
                        <span class="dashicons dashicons-tag" aria-hidden="true"></span>
                        <span class="tab-label">Mots-clés</span>
                    </button>
                    
                    <button type="button" class="boss-seo-tab-button" 
                            role="tab" 
                            aria-selected="false" 
                            data-tab="metadata">
                        <span class="dashicons dashicons-admin-appearance" aria-hidden="true"></span>
                        <span class="tab-label">Métadonnées</span>
                    </button>
                    
                    <button type="button" class="boss-seo-tab-button" 
                            role="tab" 
                            aria-selected="false" 
                            data-tab="social">
                        <span class="dashicons dashicons-share" aria-hidden="true"></span>
                        <span class="tab-label">Réseaux Sociaux</span>
                    </button>
                    
                    <button type="button" class="boss-seo-tab-button" 
                            role="tab" 
                            aria-selected="false" 
                            data-tab="analysis">
                        <span class="dashicons dashicons-chart-bar" aria-hidden="true"></span>
                        <span class="tab-label">Analyse</span>
                        <span class="boss-seo-score-badge boss-seo-score-good">92</span>
                    </button>
                </div>

                <!-- Contenu des onglets -->
                <div class="boss-seo-tabs-content">
                    
                    <!-- Onglet Mots-clés -->
                    <div class="boss-seo-tab-panel active" id="boss-seo-tab-keywords" data-tab="keywords">
                        <div class="boss-seo-tab-content">
                            <div class="boss-seo-field-group">
                                <label class="boss-seo-label">
                                    <span class="dashicons dashicons-tag" aria-hidden="true"></span>
                                    Ajouter des mots-clés
                                </label>
                                <div class="boss-seo-input-with-button">
                                    <input type="text" class="boss-seo-input" placeholder="Tapez un mot-clé moderne...">
                                    <button type="button" class="boss-seo-add-keyword-btn">
                                        <span class="dashicons dashicons-plus-alt2" aria-hidden="true"></span>
                                    </button>
                                </div>
                                
                                <div class="boss-seo-keywords-container">
                                    <div class="boss-seo-keywords-tags">
                                        <div class="boss-seo-keyword-tag primary">
                                            <span class="dashicons dashicons-star-filled" aria-hidden="true"></span>
                                            <span class="keyword-text">design moderne</span>
                                            <span class="dashicons dashicons-no-alt"></span>
                                        </div>
                                        <div class="boss-seo-keyword-tag">
                                            <span class="keyword-text">interface colorée</span>
                                            <span class="dashicons dashicons-no-alt"></span>
                                        </div>
                                        <div class="boss-seo-keyword-tag">
                                            <span class="keyword-text">animations fluides</span>
                                            <span class="dashicons dashicons-no-alt"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="boss-seo-suggestions">
                                    <div class="boss-seo-suggestions-header">
                                        <span class="dashicons dashicons-lightbulb" aria-hidden="true"></span>
                                        <span class="suggestions-title">Suggestions intelligentes</span>
                                    </div>
                                    <div class="boss-seo-suggestions-list">
                                        <button type="button" class="boss-seo-suggestion">
                                            <span class="dashicons dashicons-plus-alt2" aria-hidden="true"></span>
                                            <span class="suggestion-text">ux design</span>
                                        </button>
                                        <button type="button" class="boss-seo-suggestion">
                                            <span class="dashicons dashicons-plus-alt2" aria-hidden="true"></span>
                                            <span class="suggestion-text">css animations</span>
                                        </button>
                                        <button type="button" class="boss-seo-suggestion">
                                            <span class="dashicons dashicons-plus-alt2" aria-hidden="true"></span>
                                            <span class="suggestion-text">responsive design</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Autres onglets (cachés par défaut) -->
                    <div class="boss-seo-tab-panel" id="boss-seo-tab-metadata" data-tab="metadata">
                        <div class="boss-seo-tab-content">
                            <p style="text-align: center; color: #6b7280; padding: 40px;">
                                Contenu de l'onglet Métadonnées avec le nouveau design...
                            </p>
                        </div>
                    </div>

                    <div class="boss-seo-tab-panel" id="boss-seo-tab-social" data-tab="social">
                        <div class="boss-seo-tab-content">
                            <p style="text-align: center; color: #6b7280; padding: 40px;">
                                Contenu de l'onglet Réseaux Sociaux avec le nouveau design...
                            </p>
                        </div>
                    </div>

                    <div class="boss-seo-tab-panel" id="boss-seo-tab-analysis" data-tab="analysis">
                        <div class="boss-seo-tab-content">
                            <div class="boss-seo-score-container">
                                <div class="boss-seo-score-indicator boss-seo-score-good">
                                    <span class="boss-seo-score-value">92</span>
                                    <span class="boss-seo-score-label">/100</span>
                                </div>
                                <div class="boss-seo-score-details">
                                    <h4>Analyse SEO</h4>
                                    <p>Excellent ! Votre nouveau design est parfaitement optimisé.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Barre d'actions moderne -->
                <div class="boss-seo-floating-actions">
                    <button type="button" class="button button-primary">
                        <span class="dashicons dashicons-superhero" aria-hidden="true"></span>
                        Optimiser avec l'IA
                    </button>
                    
                    <button type="button" class="button">
                        <span class="dashicons dashicons-chart-bar" aria-hidden="true"></span>
                        Analyser
                    </button>
                    
                    <div class="boss-seo-save-indicator visible">
                        <span class="dashicons dashicons-saved" aria-hidden="true"></span>
                        <span class="save-text">Design sauvegardé</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Gestion des onglets avec animations
            $('.boss-seo-tab-button').on('click', function(e) {
                e.preventDefault();
                const tabName = $(this).data('tab');
                
                // Animation de sortie
                $('.boss-seo-tab-panel.active').fadeOut(200, function() {
                    $(this).removeClass('active');
                    
                    // Mettre à jour les boutons
                    $('.boss-seo-tab-button').removeClass('active').attr('aria-selected', 'false');
                    $(`[data-tab="${tabName}"]`).addClass('active').attr('aria-selected', 'true');
                    
                    // Animation d'entrée
                    $(`#boss-seo-tab-${tabName}`).addClass('active').fadeIn(300);
                });
                
                console.log('Onglet activé avec animation:', tabName);
            });
            
            console.log('🎨 Démo du nouveau design Boss SEO initialisée !');
        });
    </script>
</body>
</html>