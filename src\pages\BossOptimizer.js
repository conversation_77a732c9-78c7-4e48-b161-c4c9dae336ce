import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  CheckboxControl,
  Dashicon,
  Dropdown,
  DropdownMenu,
  MenuGroup,
  MenuItem,
  Modal,
  Notice,
  Panel,
  PanelBody,
  PanelRow,
  Popover,
  SearchControl,
  SelectControl,
  Spinner,
  TabPanel,
  TextControl,
  ToggleControl,
  Tooltip
} from '@wordpress/components';

// Composants internes
import ContentTable from '../components/optimizer/ContentTable';
import FilterBar from '../components/optimizer/FilterBar';
import DetailPanel from '../components/optimizer/DetailPanel';
import ActionHeader from '../components/optimizer/ActionHeader';

// Services
import optimizerService from '../services/OptimizerService';

/**
 * Composant principal du module Boss Optimizer
 */
const BossOptimizer = () => {
  // États pour les données et la pagination
  const [contents, setContents] = useState([]);
  const [filteredContents, setFilteredContents] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // États pour les filtres
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    contentType: 'all',
    seoScore: 'all',
    status: 'all',
    author: 'all',
    dateRange: 'all'
  });

  // États pour la sélection et le panneau latéral
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [activeItem, setActiveItem] = useState(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);

  // États pour les colonnes personnalisables
  const [visibleColumns, setVisibleColumns] = useState({
    title: true,
    seoScore: true,
    status: true,
    date: true,
    author: true,
    type: true,
    actions: true
  });

  // État pour les erreurs
  const [error, setError] = useState(null);

  // Charger les contenus depuis l'API lors du chargement initial ou lorsque les filtres ou la recherche changent
  useEffect(() => {
    const fetchContents = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Réinitialiser la page à 1 lorsque les filtres ou la recherche changent
        setCurrentPage(1);

        // Convertir les filtres pour l'API
        const apiFilters = {
          contentType: filters.contentType,
          seoScore: filters.seoScore,
          status: filters.status,
          author: filters.author,
          dateRange: filters.dateRange
        };

        // Appeler le service API
        const response = await optimizerService.getContents(
          apiFilters,
          searchQuery,
          1, // Toujours commencer à la première page lorsque les filtres changent
          itemsPerPage
        );

        // Mettre à jour les états avec les données reçues
        setContents(response.contents);
        setFilteredContents(response.contents);
        setTotalItems(response.total_items);
        setTotalPages(response.total_pages);
        setIsLoading(false);
      } catch (err) {
        console.error('Erreur lors du chargement des contenus:', err);
        setError(__('Impossible de charger les contenus. Veuillez réessayer.', 'boss-seo'));
        setIsLoading(false);
      }
    };

    fetchContents();

    // Ne pas inclure currentPage et itemsPerPage dans les dépendances
    // car nous gérons ces changements dans handlePageChange et handleItemsPerPageChange
  }, [filters, searchQuery]);

  // Nous n'avons plus besoin de l'effet de filtrage local car le filtrage est géré par l'API
  // Le useEffect précédent s'occupe déjà de récupérer les contenus filtrés

  // Gérer la sélection de tous les éléments
  useEffect(() => {
    if (selectAll) {
      const currentPageItems = getCurrentPageItems().map(item => item.id);
      setSelectedItems(currentPageItems);
    } else if (selectedItems.length === getCurrentPageItems().length) {
      // Si tous les éléments de la page sont sélectionnés mais que selectAll est false
      setSelectedItems([]);
    }
  }, [selectAll, currentPage, filteredContents]);

  // Obtenir les éléments de la page courante
  // Note: Cette fonction n'est plus utilisée car nous chargeons les données directement depuis l'API pour chaque page
  const getCurrentPageItems = () => {
    // Retourner directement les contenus filtrés, car ils sont déjà paginés par l'API
    return filteredContents;
  };

  // Gérer la sélection d'un élément
  const handleSelectItem = (itemId) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  // Gérer le clic sur un élément pour ouvrir le panneau latéral
  const handleItemClick = (item) => {
    setActiveItem(item);
    setIsPanelOpen(true);
  };

  // Gérer la fermeture du panneau latéral
  const handleClosePanel = () => {
    setIsPanelOpen(false);
    setActiveItem(null);
  };

  // Gérer le changement de page
  const handlePageChange = async (newPage) => {
    setCurrentPage(newPage);

    try {
      setIsLoading(true);
      setError(null);

      // Convertir les filtres pour l'API
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      // Appeler le service API avec la nouvelle page
      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        newPage,
        itemsPerPage
      );

      // Mettre à jour les états avec les données reçues
      setContents(response.contents);
      setFilteredContents(response.contents);
      setTotalItems(response.total_items);
      setTotalPages(response.total_pages);
    } catch (err) {
      console.error('Erreur lors du chargement des contenus pour la page ' + newPage + ':', err);
      setError(__('Impossible de charger les contenus. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };

  // Gérer le changement du nombre d'éléments par page
  const handleItemsPerPageChange = async (newItemsPerPage) => {
    const parsedItemsPerPage = parseInt(newItemsPerPage);
    setItemsPerPage(parsedItemsPerPage);

    // Réinitialiser la page à 1 lors du changement du nombre d'éléments par page
    setCurrentPage(1);

    try {
      setIsLoading(true);
      setError(null);

      // Convertir les filtres pour l'API
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      // Appeler le service API avec le nouveau nombre d'éléments par page
      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        1, // Réinitialiser à la première page
        parsedItemsPerPage
      );

      // Mettre à jour les états avec les données reçues
      setContents(response.contents);
      setFilteredContents(response.contents);
      setTotalItems(response.total_items);
      setTotalPages(response.total_pages);
    } catch (err) {
      console.error('Erreur lors du chargement des contenus avec ' + parsedItemsPerPage + ' éléments par page:', err);
      setError(__('Impossible de charger les contenus. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };

  // État pour suivre les processus d'optimisation
  const [optimizingItems, setOptimizingItems] = useState([]);
  const [optimizationResults, setOptimizationResults] = useState({});
  const [showOptimizationNotice, setShowOptimizationNotice] = useState(false);
  const [optimizationNotice, setOptimizationNotice] = useState({ type: 'info', message: '' });

  // État pour les options d'optimisation
  const [optimizationOptions, setOptimizationOptions] = useState({
    title_seo: true,
    meta_description: true,
    keywords: true,
    modify_content: false,
    modify_title: false,
    modify_headings: false,
    modify_images: false,
    modify_links: false,
    useAI: true
  });

  // État pour la modal de confirmation
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingOptimizationId, setPendingOptimizationId] = useState(null);
  const [pendingOptimizationType, setPendingOptimizationType] = useState(null); // 'single', 'bulk', 'all'
  const [pendingOptimizationIds, setPendingOptimizationIds] = useState([]);

  // Gérer l'optimisation d'un élément
  const handleOptimize = async (itemId) => {
    // Vérifier si l'utilisateur souhaite modifier le contenu principal
    if (optimizationOptions.modify_content ||
        optimizationOptions.modify_title ||
        optimizationOptions.modify_headings ||
        optimizationOptions.modify_images ||
        optimizationOptions.modify_links) {
      // Demander confirmation avant de modifier le contenu
      setPendingOptimizationId(itemId);
      setPendingOptimizationType('single');
      setShowConfirmModal(true);
      return;
    }

    // Si aucune modification du contenu n'est demandée, procéder directement
    await executeOptimization(itemId);
  };

  // Fonction d'exécution de l'optimisation après confirmation si nécessaire
  const executeOptimization = async (itemId) => {
    try {
      // Marquer l'élément comme en cours d'optimisation
      setOptimizingItems(prev => [...prev, itemId]);

      // Appeler l'API d'optimisation avec les options définies
      const result = await optimizerService.optimizeContent(itemId, optimizationOptions);

      // Mettre à jour les résultats d'optimisation
      setOptimizationResults(prev => ({
        ...prev,
        [itemId]: result
      }));

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __('Métadonnées SEO générées avec succès.', 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);

      // Si le panneau de détails est ouvert pour cet élément, mettre à jour l'élément actif
      if (activeItem && activeItem.id === itemId) {
        const updatedItem = response.contents.find(item => item.id === itemId);
        if (updatedItem) {
          setActiveItem(updatedItem);
        }
      }
    } catch (err) {
      console.error(`Erreur lors de l'optimisation du contenu ${itemId}:`, err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors de la génération des métadonnées SEO. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
    } finally {
      // Retirer l'élément de la liste des éléments en cours d'optimisation
      setOptimizingItems(prev => prev.filter(id => id !== itemId));

      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Gérer l'optimisation en masse
  const handleBulkOptimize = async () => {
    if (selectedItems.length === 0) return;

    // Vérifier si l'utilisateur souhaite modifier le contenu principal
    if (optimizationOptions.modify_content ||
        optimizationOptions.modify_title ||
        optimizationOptions.modify_headings ||
        optimizationOptions.modify_images ||
        optimizationOptions.modify_links) {
      // Demander confirmation avant de modifier le contenu
      setPendingOptimizationIds(selectedItems);
      setPendingOptimizationType('bulk');
      setShowConfirmModal(true);
      return;
    }

    // Si aucune modification du contenu n'est demandée, procéder directement
    await executeBulkOptimization(selectedItems);
  };

  // Fonction d'exécution de l'optimisation en masse après confirmation si nécessaire
  const executeBulkOptimization = async (itemIds) => {
    try {
      // Marquer tous les éléments sélectionnés comme en cours d'optimisation
      setOptimizingItems(prev => [...prev, ...itemIds]);

      // Appeler l'API d'optimisation en masse avec les options définies
      const result = await optimizerService.optimizeContentsBulk(itemIds, optimizationOptions);

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __(`Métadonnées SEO générées avec succès pour ${result.summary.success} contenu(s).`, 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);

      // Réinitialiser la sélection
      setSelectedItems([]);
      setSelectAll(false);
    } catch (err) {
      console.error('Erreur lors de la génération en masse des métadonnées SEO:', err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors de la génération en masse des métadonnées SEO. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
    } finally {
      // Retirer tous les éléments de la liste des éléments en cours d'optimisation
      setOptimizingItems([]);

      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Gérer l'analyse en masse
  const handleBulkAnalyze = async () => {
    if (selectedItems.length === 0) return;

    // Demander confirmation avant d'analyser
    if (!window.confirm(__(`Êtes-vous sûr de vouloir analyser ${selectedItems.length} élément(s) ?`, 'boss-seo'))) {
      return;
    }

    try {
      // Marquer tous les éléments sélectionnés comme en cours d'analyse
      setOptimizingItems(prev => [...prev, ...selectedItems]);

      // Appeler l'API d'analyse en masse
      const result = await optimizerService.analyzeContentsBulk(selectedItems);

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __(`${result.summary.success} contenus analysés avec succès.`, 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);

      // Réinitialiser la sélection
      setSelectedItems([]);
      setSelectAll(false);
    } catch (err) {
      console.error('Erreur lors de l\'analyse en masse des contenus:', err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors de l\'analyse en masse des contenus. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
    } finally {
      // Retirer tous les éléments de la liste des éléments en cours d'analyse
      setOptimizingItems([]);

      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Gérer l'optimisation de tous les contenus
  const handleOptimizeAll = async () => {
    // Vérifier si l'utilisateur souhaite modifier le contenu principal
    if (optimizationOptions.modify_content ||
        optimizationOptions.modify_title ||
        optimizationOptions.modify_headings ||
        optimizationOptions.modify_images ||
        optimizationOptions.modify_links) {
      // Demander confirmation avant de modifier le contenu
      setPendingOptimizationType('all');
      setShowConfirmModal(true);
      return;
    }

    // Si aucune modification du contenu n'est demandée, procéder directement
    await executeOptimizeAll();
  };

  // Fonction d'exécution de l'optimisation de tous les contenus après confirmation si nécessaire
  const executeOptimizeAll = async () => {
    try {
      // Afficher une notification d'information
      setOptimizationNotice({
        type: 'info',
        message: __('Génération des métadonnées SEO pour tous les contenus en cours. Cela peut prendre un certain temps...', 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Appeler l'API d'optimisation de tous les contenus avec les options définies
      const result = await optimizerService.optimizeAllContents(optimizationOptions);

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __(`Métadonnées SEO générées avec succès pour ${result.summary.success} contenu(s).`, 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);
    } catch (err) {
      console.error('Erreur lors de la génération des métadonnées SEO pour tous les contenus:', err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors de la génération des métadonnées SEO pour tous les contenus. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
    } finally {
      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Gérer l'analyse de tous les contenus
  const handleAnalyzeAll = async () => {
    // Demander confirmation avant d'analyser
    if (!window.confirm(__('Êtes-vous sûr de vouloir analyser TOUS les contenus ? Cette action peut prendre du temps.', 'boss-seo'))) {
      return;
    }

    try {
      // Afficher une notification d'information
      setOptimizationNotice({
        type: 'info',
        message: __('Analyse de tous les contenus en cours. Cela peut prendre un certain temps...', 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Appeler l'API d'analyse de tous les contenus
      const result = await optimizerService.analyzeAllContents();

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __(`${result.summary.success} contenus analysés avec succès.`, 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);
    } catch (err) {
      console.error('Erreur lors de l\'analyse de tous les contenus:', err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors de l\'analyse de tous les contenus. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
    } finally {
      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Gérer l'ajout de balises aux éléments sélectionnés
  const handleAddTags = async () => {
    if (selectedItems.length === 0) return;

    // Demander les balises à ajouter
    const tags = prompt(__('Entrez les balises à ajouter (séparées par des virgules) :', 'boss-seo'));

    if (!tags) return; // L'utilisateur a annulé

    try {
      // Afficher une notification d'information
      setOptimizationNotice({
        type: 'info',
        message: __('Ajout des balises en cours...', 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Appeler l'API pour ajouter les balises
      const result = await optimizerService.addTagsToContents(selectedItems, tags.split(',').map(tag => tag.trim()));

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __(`Balises ajoutées avec succès à ${result.success} contenu(s).`, 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);
    } catch (err) {
      console.error('Erreur lors de l\'ajout des balises:', err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors de l\'ajout des balises. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
    } finally {
      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Gérer la modification de la catégorie des éléments sélectionnés
  const handleChangeCategory = async () => {
    if (selectedItems.length === 0) return;

    // Simuler une liste de catégories
    const categories = [
      { id: 1, name: 'Non classé' },
      { id: 2, name: 'Blog' },
      { id: 3, name: 'Actualités' },
      { id: 4, name: 'Tutoriels' },
      { id: 5, name: 'Produits' }
    ];

    // Créer une liste de catégories pour l'affichage
    const categoryOptions = categories.map(cat => `${cat.id}: ${cat.name}`).join('\n');

    // Demander la catégorie à appliquer
    const categoryInput = prompt(__(`Entrez l'ID de la catégorie à appliquer :\n\n${categoryOptions}`, 'boss-seo'));

    if (!categoryInput) return; // L'utilisateur a annulé

    const categoryId = parseInt(categoryInput);

    if (isNaN(categoryId) || !categories.some(cat => cat.id === categoryId)) {
      alert(__('ID de catégorie invalide.', 'boss-seo'));
      return;
    }

    try {
      // Afficher une notification d'information
      setOptimizationNotice({
        type: 'info',
        message: __('Modification de la catégorie en cours...', 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Appeler l'API pour modifier la catégorie
      const result = await optimizerService.changeCategoryForContents(selectedItems, categoryId);

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __(`Catégorie modifiée avec succès pour ${result.success} contenu(s).`, 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);
    } catch (err) {
      console.error('Erreur lors de la modification de la catégorie:', err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors de la modification de la catégorie. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
    } finally {
      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Gérer le changement d'auteur des éléments sélectionnés
  const handleChangeAuthor = async () => {
    if (selectedItems.length === 0) return;

    // Simuler une liste d'auteurs
    const authors = [
      { id: 1, name: 'Admin' },
      { id: 2, name: 'Éditeur' },
      { id: 3, name: 'Auteur 1' },
      { id: 4, name: 'Auteur 2' },
      { id: 5, name: 'Contributeur' }
    ];

    // Créer une liste d'auteurs pour l'affichage
    const authorOptions = authors.map(author => `${author.id}: ${author.name}`).join('\n');

    // Demander l'auteur à appliquer
    const authorInput = prompt(__(`Entrez l'ID de l'auteur à appliquer :\n\n${authorOptions}`, 'boss-seo'));

    if (!authorInput) return; // L'utilisateur a annulé

    const authorId = parseInt(authorInput);

    if (isNaN(authorId) || !authors.some(author => author.id === authorId)) {
      alert(__('ID d\'auteur invalide.', 'boss-seo'));
      return;
    }

    try {
      // Afficher une notification d'information
      setOptimizationNotice({
        type: 'info',
        message: __('Changement d\'auteur en cours...', 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Appeler l'API pour changer l'auteur
      const result = await optimizerService.changeAuthorForContents(selectedItems, authorId);

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __(`Auteur modifié avec succès pour ${result.success} contenu(s).`, 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);
    } catch (err) {
      console.error('Erreur lors du changement d\'auteur:', err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors du changement d\'auteur. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
    } finally {
      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Gérer l'analyse d'un contenu
  const handleAnalyze = async (itemId) => {
    try {
      // Marquer l'élément comme en cours d'analyse
      setOptimizingItems(prev => [...prev, itemId]);

      // Appeler l'API d'analyse
      const result = await optimizerService.analyzeContent(itemId);

      // Afficher une notification de succès
      setOptimizationNotice({
        type: 'success',
        message: __('Contenu analysé avec succès.', 'boss-seo')
      });
      setShowOptimizationNotice(true);

      // Rafraîchir les données
      const apiFilters = {
        contentType: filters.contentType,
        seoScore: filters.seoScore,
        status: filters.status,
        author: filters.author,
        dateRange: filters.dateRange
      };

      const response = await optimizerService.getContents(
        apiFilters,
        searchQuery,
        currentPage,
        itemsPerPage
      );

      setContents(response.contents);
      setFilteredContents(response.contents);

      // Si le panneau de détails est ouvert pour cet élément, mettre à jour l'élément actif
      if (activeItem && activeItem.id === itemId) {
        const updatedItem = response.contents.find(item => item.id === itemId);
        if (updatedItem) {
          setActiveItem(updatedItem);
        }
      }

      return result;
    } catch (err) {
      console.error(`Erreur lors de l'analyse du contenu ${itemId}:`, err);
      setOptimizationNotice({
        type: 'error',
        message: __('Erreur lors de l\'analyse du contenu. Veuillez réessayer.', 'boss-seo')
      });
      setShowOptimizationNotice(true);
      return null;
    } finally {
      // Retirer l'élément de la liste des éléments en cours d'optimisation
      setOptimizingItems(prev => prev.filter(id => id !== itemId));

      // Masquer la notification après 5 secondes
      setTimeout(() => {
        setShowOptimizationNotice(false);
      }, 5000);
    }
  };

  // Fonction pour confirmer l'optimisation
  const confirmOptimization = () => {
    setShowConfirmModal(false);

    if (pendingOptimizationType === 'single' && pendingOptimizationId) {
      executeOptimization(pendingOptimizationId);
    } else if (pendingOptimizationType === 'bulk' && pendingOptimizationIds.length > 0) {
      executeBulkOptimization(pendingOptimizationIds);
    } else if (pendingOptimizationType === 'all') {
      executeOptimizeAll();
    }

    // Réinitialiser les états
    setPendingOptimizationId(null);
    setPendingOptimizationType(null);
    setPendingOptimizationIds([]);
  };

  // Fonction pour annuler l'optimisation
  const cancelOptimization = () => {
    setShowConfirmModal(false);
    setPendingOptimizationId(null);
    setPendingOptimizationType(null);
    setPendingOptimizationIds([]);
  };

  return (
    <div className="boss-flex boss-flex-col boss-min-h-screen">
      <div className="boss-p-6">
        <div className="boss-mb-6">
          <h1 className="boss-text-2xl boss-font-bold boss-text-boss-dark boss-mb-2">
            {__('Boss Optimizer', 'boss-seo')}
          </h1>
          <p className="boss-text-boss-gray">
            {__('Générez des métadonnées SEO pour améliorer votre référencement naturel', 'boss-seo')}
          </p>
        </div>

        {/* Notification */}
        {showOptimizationNotice && (
          <Notice
            status={optimizationNotice.type}
            isDismissible={true}
            onRemove={() => setShowOptimizationNotice(false)}
            className="boss-mb-4"
          >
            {optimizationNotice.message}
          </Notice>
        )}

        {/* Message d'erreur */}
        {error && (
          <Notice
            status="error"
            isDismissible={true}
            onRemove={() => setError(null)}
            className="boss-mb-4"
          >
            {error}
          </Notice>
        )}

        {/* Options d'optimisation */}
        <Card className="boss-mb-4">
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Options d\'optimisation', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
              <div>
                <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                  {__('Métadonnées SEO', 'boss-seo')}
                </h3>
                <ToggleControl
                  label={__('Générer le titre SEO', 'boss-seo')}
                  checked={optimizationOptions.title_seo}
                  onChange={(value) => setOptimizationOptions({...optimizationOptions, title_seo: value})}
                  className="boss-mb-2"
                />
                <ToggleControl
                  label={__('Générer la meta description', 'boss-seo')}
                  checked={optimizationOptions.meta_description}
                  onChange={(value) => setOptimizationOptions({...optimizationOptions, meta_description: value})}
                  className="boss-mb-2"
                />
                <ToggleControl
                  label={__('Générer les mots-clés', 'boss-seo')}
                  checked={optimizationOptions.keywords}
                  onChange={(value) => setOptimizationOptions({...optimizationOptions, keywords: value})}
                  className="boss-mb-2"
                />
              </div>
              <div>
                <h3 className="boss-text-md boss-font-semibold boss-mb-2">
                  {__('Modifications du contenu (désactivées par défaut)', 'boss-seo')}
                </h3>
                <ToggleControl
                  label={__('Modifier le contenu principal', 'boss-seo')}
                  checked={optimizationOptions.modify_content}
                  onChange={(value) => setOptimizationOptions({...optimizationOptions, modify_content: value})}
                  className="boss-mb-2"
                />
                <ToggleControl
                  label={__('Modifier le titre', 'boss-seo')}
                  checked={optimizationOptions.modify_title}
                  onChange={(value) => setOptimizationOptions({...optimizationOptions, modify_title: value})}
                  className="boss-mb-2"
                />
                <ToggleControl
                  label={__('Modifier les titres de sections (H1-H6)', 'boss-seo')}
                  checked={optimizationOptions.modify_headings}
                  onChange={(value) => setOptimizationOptions({...optimizationOptions, modify_headings: value})}
                  className="boss-mb-2"
                />
                <ToggleControl
                  label={__('Modifier les images (alt text)', 'boss-seo')}
                  checked={optimizationOptions.modify_images}
                  onChange={(value) => setOptimizationOptions({...optimizationOptions, modify_images: value})}
                  className="boss-mb-2"
                />
                <ToggleControl
                  label={__('Modifier les liens', 'boss-seo')}
                  checked={optimizationOptions.modify_links}
                  onChange={(value) => setOptimizationOptions({...optimizationOptions, modify_links: value})}
                  className="boss-mb-2"
                />
              </div>
            </div>
          </CardBody>
        </Card>

        {/* En-tête avec actions */}
        <ActionHeader
          selectedItems={selectedItems}
          onBulkOptimize={handleBulkOptimize}
          onBulkAnalyze={handleBulkAnalyze}
          onAddTags={handleAddTags}
          onChangeCategory={handleChangeCategory}
          onChangeAuthor={handleChangeAuthor}
          onOptimizeAll={handleOptimizeAll}
          onAnalyzeAll={handleAnalyzeAll}
          onClearSelection={() => setSelectedItems([])}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
        />

        {/* Barre de filtres */}
        <FilterBar
          filters={filters}
          onFilterChange={(key, value) => setFilters({...filters, [key]: value})}
          visibleColumns={visibleColumns}
          onColumnToggle={(column) => setVisibleColumns({...visibleColumns, [column]: !visibleColumns[column]})}
        />

        {/* Tableau de contenus */}
        <Card className="boss-mb-6">
          <CardBody className="boss-p-0">
            {isLoading ? (
              <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                <Spinner />
              </div>
            ) : (
              <ContentTable
                items={getCurrentPageItems()}
                selectedItems={selectedItems}
                onSelectItem={handleSelectItem}
                onSelectAll={() => setSelectAll(!selectAll)}
                selectAll={selectAll}
                onItemClick={handleItemClick}
                onOptimize={handleOptimize}
                onAnalyze={handleAnalyze}
                optimizingItems={optimizingItems}
                visibleColumns={visibleColumns}
                currentPage={currentPage}
                totalPages={totalPages}
                itemsPerPage={itemsPerPage}
                totalItems={totalItems}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </CardBody>
        </Card>
      </div>

      {/* Panneau latéral de détails */}
      {isPanelOpen && activeItem && (
        <DetailPanel
          item={activeItem}
          onClose={handleClosePanel}
          onOptimize={() => handleOptimize(activeItem.id)}
          onAnalyze={() => handleAnalyze(activeItem.id)}
          isOptimizing={optimizingItems.includes(activeItem.id)}
          optimizationResults={optimizationResults[activeItem.id] || null}
        />
      )}

      {/* Modal de confirmation pour la modification du contenu */}
      {showConfirmModal && (
        <Modal
          title={__('Confirmation de modification du contenu', 'boss-seo')}
          onRequestClose={cancelOptimization}
          className="boss-max-w-lg"
        >
          <div className="boss-p-6">
            <p className="boss-mb-4 boss-text-red-600 boss-font-bold">
              {__('Attention : Vous avez activé des options qui modifieront le contenu principal des articles.', 'boss-seo')}
            </p>

            <p className="boss-mb-4">
              {pendingOptimizationType === 'single' && (
                __('Vous êtes sur le point de modifier le contenu d\'un article. Cette action peut altérer le texte, les titres, les images ou les liens existants.', 'boss-seo')
              )}
              {pendingOptimizationType === 'bulk' && (
                __(`Vous êtes sur le point de modifier le contenu de ${pendingOptimizationIds.length} articles. Cette action peut altérer le texte, les titres, les images ou les liens existants.`, 'boss-seo')
              )}
              {pendingOptimizationType === 'all' && (
                __('Vous êtes sur le point de modifier le contenu de TOUS les articles. Cette action peut altérer le texte, les titres, les images ou les liens existants.', 'boss-seo')
              )}
            </p>

            <p className="boss-mb-6">
              {__('Êtes-vous sûr de vouloir continuer ?', 'boss-seo')}
            </p>

            <div className="boss-flex boss-justify-end boss-space-x-2">
              <Button
                isSecondary
                onClick={cancelOptimization}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                onClick={confirmOptimization}
              >
                {__('Confirmer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default BossOptimizer;
