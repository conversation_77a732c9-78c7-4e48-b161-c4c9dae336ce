<?php
/**
 * Analyseur SEO intelligent pour Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe d'analyse SEO intelligente.
 *
 * Cette classe fournit toutes les méthodes d'analyse, d'optimisation
 * et de génération de contenu SEO.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Analyzer {

    /**
     * Instance du service IA.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_AI    $ai    Instance du service IA.
     */
    private $ai;

    /**
     * Instance des paramètres.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_Settings    $settings    Instance des paramètres.
     */
    private $settings;

    /**
     * Constructeur.
     *
     * @since    1.2.0
     */
    public function __construct() {
        // Charger les dépendances
        if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-ai.php';
        }
        if ( ! class_exists( 'Boss_Optimizer_Settings' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-settings.php';
        }

        $this->settings = new Boss_Optimizer_Settings( 'boss-seo' );
        $this->ai = new Boss_Optimizer_AI( $this->settings );
    }

    /**
     * Effectue une analyse complète du contenu SEO avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id    ID du post.
     * @param    string    $content    Contenu à analyser.
     * @return   array                 Résultats de l'analyse.
     */
    public function perform_content_analysis( $post_id, $content ) {
        $score = 100;
        $recommendations = array();

        // Récupérer les métadonnées
        $title = get_post_meta( $post_id, '_boss_seo_title', true ) ?: get_the_title( $post_id );
        $meta_desc = get_post_meta( $post_id, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );

        // Analyse du titre
        $title_analysis = $this->analyze_title( $title, $focus_keyword );
        $score += $title_analysis['score_impact'];
        $recommendations = array_merge( $recommendations, $title_analysis['recommendations'] );

        // Analyse de la meta description
        $desc_analysis = $this->analyze_meta_description( $meta_desc, $focus_keyword );
        $score += $desc_analysis['score_impact'];
        $recommendations = array_merge( $recommendations, $desc_analysis['recommendations'] );

        // Analyse du contenu
        $content_analysis = $this->analyze_content( $content, $focus_keyword );
        $score += $content_analysis['score_impact'];
        $recommendations = array_merge( $recommendations, $content_analysis['recommendations'] );

        // Analyse des images
        $images_analysis = $this->analyze_images( $post_id, $focus_keyword );
        $score += $images_analysis['score_impact'];
        $recommendations = array_merge( $recommendations, $images_analysis['recommendations'] );

        // Limiter le score entre 0 et 100
        $score = max( 0, min( 100, $score ) );

        // Sauvegarder les résultats
        update_post_meta( $post_id, '_boss_seo_score', $score );
        update_post_meta( $post_id, '_boss_seo_recommendations', $recommendations );
        update_post_meta( $post_id, '_boss_seo_analysis_date', current_time( 'mysql' ) );

        return array(
            'score' => $score,
            'recommendations' => $recommendations,
            'analysis_date' => current_time( 'mysql' ),
            'details' => array(
                'title' => $title_analysis,
                'meta_description' => $desc_analysis,
                'content' => $content_analysis,
                'images' => $images_analysis
            )
        );
    }

    /**
     * Analyse le titre SEO.
     *
     * @since    1.2.0
     * @param    string    $title           Titre à analyser.
     * @param    string    $focus_keyword   Mot-clé principal.
     * @return   array                      Résultats de l'analyse.
     */
    private function analyze_title( $title, $focus_keyword ) {
        $score_impact = 0;
        $recommendations = array();

        $title_length = mb_strlen( $title );

        // Vérifier la longueur
        if ( $title_length === 0 ) {
            $score_impact -= 20;
            $recommendations[] = array(
                'type' => 'critical',
                'text' => 'Aucun titre défini. Ajoutez un titre optimisé.'
            );
        } elseif ( $title_length < 30 ) {
            $score_impact -= 10;
            $recommendations[] = array(
                'type' => 'warning',
                'text' => 'Titre trop court. Idéalement entre 50-60 caractères.'
            );
        } elseif ( $title_length > 60 ) {
            $score_impact -= 5;
            $recommendations[] = array(
                'type' => 'warning',
                'text' => 'Titre trop long. Il sera tronqué dans les résultats de recherche.'
            );
        } else {
            $score_impact += 5;
        }

        // Vérifier la présence du mot-clé principal
        if ( ! empty( $focus_keyword ) ) {
            if ( stripos( $title, $focus_keyword ) !== false ) {
                $score_impact += 10;
            } else {
                $score_impact -= 15;
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => 'Le mot-clé principal n\'apparaît pas dans le titre.'
                );
            }

            // Vérifier la position du mot-clé
            if ( stripos( $title, $focus_keyword ) === 0 ) {
                $score_impact += 5;
            }
        }

        return array(
            'score_impact' => $score_impact,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse la meta description.
     *
     * @since    1.2.0
     * @param    string    $meta_desc       Meta description à analyser.
     * @param    string    $focus_keyword   Mot-clé principal.
     * @return   array                      Résultats de l'analyse.
     */
    private function analyze_meta_description( $meta_desc, $focus_keyword ) {
        $score_impact = 0;
        $recommendations = array();

        $desc_length = mb_strlen( $meta_desc );

        // Vérifier la longueur
        if ( $desc_length === 0 ) {
            $score_impact -= 15;
            $recommendations[] = array(
                'type' => 'critical',
                'text' => 'Aucune meta description définie. Ajoutez une description attrayante.'
            );
        } elseif ( $desc_length < 120 ) {
            $score_impact -= 5;
            $recommendations[] = array(
                'type' => 'warning',
                'text' => 'Meta description trop courte. Idéalement entre 120-160 caractères.'
            );
        } elseif ( $desc_length > 160 ) {
            $score_impact -= 3;
            $recommendations[] = array(
                'type' => 'warning',
                'text' => 'Meta description trop longue. Elle sera tronquée.'
            );
        } else {
            $score_impact += 5;
        }

        // Vérifier la présence du mot-clé principal
        if ( ! empty( $focus_keyword ) && ! empty( $meta_desc ) ) {
            if ( stripos( $meta_desc, $focus_keyword ) !== false ) {
                $score_impact += 8;
            } else {
                $score_impact -= 10;
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => 'Le mot-clé principal n\'apparaît pas dans la meta description.'
                );
            }
        }

        return array(
            'score_impact' => $score_impact,
            'recommendations' => $recommendations
        );
    }

    /**
     * Analyse le contenu principal.
     *
     * @since    1.2.0
     * @param    string    $content         Contenu à analyser.
     * @param    string    $focus_keyword   Mot-clé principal.
     * @return   array                      Résultats de l'analyse.
     */
    private function analyze_content( $content, $focus_keyword ) {
        $score_impact = 0;
        $recommendations = array();

        // Nettoyer le contenu
        $clean_content = wp_strip_all_tags( $content );
        $word_count = str_word_count( $clean_content );

        // Vérifier la longueur du contenu
        if ( $word_count < 300 ) {
            $score_impact -= 10;
            $recommendations[] = array(
                'type' => 'warning',
                'text' => 'Contenu trop court. Visez au moins 300 mots pour un bon référencement.'
            );
        } elseif ( $word_count > 2000 ) {
            $score_impact += 5;
        }

        // Analyser la densité des mots-clés
        if ( ! empty( $focus_keyword ) ) {
            $keyword_count = substr_count( strtolower( $clean_content ), strtolower( $focus_keyword ) );
            $keyword_density = $word_count > 0 ? ( $keyword_count / $word_count ) * 100 : 0;

            if ( $keyword_density === 0 ) {
                $score_impact -= 15;
                $recommendations[] = array(
                    'type' => 'critical',
                    'text' => 'Le mot-clé principal n\'apparaît pas dans le contenu.'
                );
            } elseif ( $keyword_density < 0.5 ) {
                $score_impact -= 5;
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => 'Densité du mot-clé trop faible. Utilisez-le plus naturellement.'
                );
            } elseif ( $keyword_density > 3 ) {
                $score_impact -= 10;
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => 'Densité du mot-clé trop élevée. Risque de sur-optimisation.'
                );
            } else {
                $score_impact += 10;
            }
        }

        // Analyser la structure (titres H1, H2, etc.)
        $h1_count = substr_count( $content, '<h1' );
        $h2_count = substr_count( $content, '<h2' );

        if ( $h1_count === 0 ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => 'Ajoutez des titres H1 pour structurer votre contenu.'
            );
        }

        if ( $h2_count === 0 && $word_count > 500 ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => 'Ajoutez des sous-titres H2 pour améliorer la lisibilité.'
            );
        }

        return array(
            'score_impact' => $score_impact,
            'recommendations' => $recommendations,
            'word_count' => $word_count,
            'keyword_density' => $keyword_density ?? 0
        );
    }

    /**
     * Analyse les images du contenu.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $focus_keyword   Mot-clé principal.
     * @return   array                      Résultats de l'analyse.
     */
    private function analyze_images( $post_id, $focus_keyword ) {
        $score_impact = 0;
        $recommendations = array();

        // Récupérer les images attachées
        $images = get_attached_media( 'image', $post_id );
        $image_count = count( $images );

        if ( $image_count === 0 ) {
            $recommendations[] = array(
                'type' => 'info',
                'text' => 'Ajoutez des images pour enrichir votre contenu.'
            );
        } else {
            $score_impact += 2;

            // Vérifier les attributs alt
            $images_without_alt = 0;
            foreach ( $images as $image ) {
                $alt_text = get_post_meta( $image->ID, '_wp_attachment_image_alt', true );
                if ( empty( $alt_text ) ) {
                    $images_without_alt++;
                }
            }

            if ( $images_without_alt > 0 ) {
                $score_impact -= 3;
                $recommendations[] = array(
                    'type' => 'warning',
                    'text' => sprintf( '%d image(s) sans attribut alt. Ajoutez des descriptions.', $images_without_alt )
                );
            }
        }

        return array(
            'score_impact' => $score_impact,
            'recommendations' => $recommendations,
            'image_count' => $image_count
        );
    }

    /**
     * Génère des suggestions intelligentes de mots-clés avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id    ID du post.
     * @param    string    $content    Contenu à analyser.
     * @return   array                 Suggestions de mots-clés.
     */
    public function generate_smart_suggestions( $post_id, $content ) {
        // Vérifier si l'IA est configurée
        if ( ! $this->settings->is_ai_configured() ) {
            return $this->generate_fallback_suggestions( $post_id, $content );
        }

        // Récupérer le titre et les métadonnées
        $title = get_the_title( $post_id );
        $clean_content = wp_strip_all_tags( $content );
        $excerpt = wp_trim_words( $clean_content, 50 );

        // Récupérer les catégories pour le contexte
        $categories = get_the_category( $post_id );
        $category_context = ! empty( $categories ) ? $categories[0]->name : '';

        // Construire le prompt pour l'IA
        $prompt = sprintf(
            'Génère 10 mots-clés SEO pertinents et recherchés pour ce contenu :

Titre : "%s"
Catégorie : "%s"
Extrait : "%s"

Critères :
- Mots-clés pertinents pour le SEO
- Volume de recherche potentiel
- Longue traîne incluse
- En français
- Format : liste simple séparée par des virgules

Mots-clés :',
            $title,
            $category_context,
            $excerpt
        );

        // Générer avec l'IA
        $ai_result = $this->ai->generate_content( $prompt, array(
            'temperature' => 0.7,
            'max_tokens' => 200
        ) );

        if ( $ai_result['success'] && ! empty( $ai_result['content'] ) ) {
            // Parser la réponse de l'IA
            $ai_suggestions = $this->parse_ai_keywords( $ai_result['content'] );

            if ( ! empty( $ai_suggestions ) ) {
                return array_slice( $ai_suggestions, 0, 10 );
            }
        }

        // Fallback si l'IA échoue
        return $this->generate_fallback_suggestions( $post_id, $content );
    }

    /**
     * Parse les mots-clés générés par l'IA.
     *
     * @since    1.2.0
     * @param    string    $ai_content    Contenu généré par l'IA.
     * @return   array                    Mots-clés parsés.
     */
    private function parse_ai_keywords( $ai_content ) {
        $keywords = array();

        // Nettoyer le contenu
        $content = trim( $ai_content );

        // Essayer de parser comme liste séparée par des virgules
        if ( strpos( $content, ',' ) !== false ) {
            $raw_keywords = explode( ',', $content );
        } else {
            // Essayer de parser comme liste avec des tirets ou des numéros
            $lines = explode( "\n", $content );
            $raw_keywords = array();
            foreach ( $lines as $line ) {
                $line = trim( $line );
                // Supprimer les numéros, tirets, etc.
                $line = preg_replace( '/^[\d\-\*\•\s]+/', '', $line );
                if ( ! empty( $line ) ) {
                    $raw_keywords[] = $line;
                }
            }
        }

        // Nettoyer chaque mot-clé
        foreach ( $raw_keywords as $keyword ) {
            $keyword = trim( $keyword );
            $keyword = trim( $keyword, '"-' );
            $keyword = strtolower( $keyword );

            if ( ! empty( $keyword ) && strlen( $keyword ) > 2 ) {
                $keywords[] = $keyword;
            }
        }

        return array_unique( $keywords );
    }

    /**
     * Génère des suggestions de fallback sans IA.
     *
     * @since    1.2.0
     * @param    int       $post_id    ID du post.
     * @param    string    $content    Contenu à analyser.
     * @return   array                 Suggestions de mots-clés.
     */
    private function generate_fallback_suggestions( $post_id, $content ) {
        $suggestions = array();

        // Récupérer le titre et le contenu
        $title = get_the_title( $post_id );
        $clean_content = wp_strip_all_tags( $content );
        $text = $title . ' ' . $clean_content;

        // Extraire les mots significatifs
        $words = str_word_count( strtolower( $text ), 1, 'àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ' );

        // Filtrer les mots vides
        $stop_words = array(
            'le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour',
            'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus',
            'par', 'grand', 'mais', 'que', 'très', 'bien', 'autre', 'depuis', 'sans'
        );

        $filtered_words = array_filter( $words, function( $word ) use ( $stop_words ) {
            return strlen( $word ) > 3 && ! in_array( $word, $stop_words );
        });

        // Compter les occurrences
        $word_counts = array_count_values( $filtered_words );
        arsort( $word_counts );

        // Prendre les mots les plus fréquents
        $suggestions = array_slice( array_keys( $word_counts ), 0, 5 );

        // Ajouter des suggestions génériques
        $generic_suggestions = array( 'guide', 'tutoriel', 'conseils', 'astuces', 'optimisation' );
        $suggestions = array_merge( $suggestions, $generic_suggestions );

        return array_unique( array_slice( $suggestions, 0, 8 ) );
    }
}
