<?php
/**
 * Test de l'Analyzer corrigé
 * 
 * Ce fichier teste si l'analyse SEO fonctionne maintenant correctement
 */

// Simuler l'environnement WordPress
if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $key, $single = false) {
        $meta_data = array(
            '_boss_seo_title' => 'Guide WordPress SEO Complet',
            '_boss_seo_meta_description' => 'Découvrez comment optimiser votre site WordPress pour le référencement naturel avec ce guide complet.',
            '_boss_seo_focus_keyword' => 'wordpress seo'
        );
        return isset($meta_data[$key]) ? $meta_data[$key] : '';
    }
}

if (!function_exists('get_the_title')) {
    function get_the_title($post_id = null) {
        return 'Guide WordPress SEO Complet';
    }
}

if (!function_exists('update_post_meta')) {
    function update_post_meta($post_id, $key, $value) {
        echo "<p class='success'>✅ Sauvegardé : {$key} = " . (is_array($value) ? json_encode($value) : $value) . "</p>";
        return true;
    }
}

if (!function_exists('current_time')) {
    function current_time($format) {
        return date($format);
    }
}

if (!function_exists('wp_strip_all_tags')) {
    function wp_strip_all_tags($string) {
        return strip_tags($string);
    }
}

if (!function_exists('str_word_count')) {
    function str_word_count($string, $format = 0) {
        return count(explode(' ', trim($string)));
    }
}

if (!function_exists('get_attached_media')) {
    function get_attached_media($type, $post_id) {
        return array(); // Simuler aucune image
    }
}

if (!function_exists('get_the_category')) {
    function get_the_category($post_id) {
        return array((object)array('name' => 'WordPress'));
    }
}

if (!function_exists('wp_trim_words')) {
    function wp_trim_words($text, $num_words = 55) {
        $words = explode(' ', $text);
        return implode(' ', array_slice($words, 0, $num_words));
    }
}

echo "<!DOCTYPE html><html><head><title>🔧 Test Analyzer Corrigé</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔧 Test de l'Analyzer Corrigé</h1>";

// Charger les classes nécessaires
require_once 'includes/class-boss-optimizer-settings.php';
require_once 'includes/class-boss-optimizer-ai.php';
require_once 'includes/class-boss-seo-analyzer.php';

echo "<h2>📊 Test d'Analyse SEO</h2>";

try {
    // Créer l'analyzer
    $analyzer = new Boss_SEO_Analyzer();
    
    // Contenu de test
    $post_id = 123;
    $content = "
    <h1>Guide WordPress SEO Complet</h1>
    <p>WordPress SEO est essentiel pour améliorer le référencement de votre site web. Dans ce guide complet, nous allons explorer toutes les techniques d'optimisation SEO pour WordPress.</p>
    
    <h2>Pourquoi le SEO WordPress est important</h2>
    <p>Le référencement naturel permet d'augmenter la visibilité de votre site WordPress dans les moteurs de recherche. Une bonne stratégie SEO WordPress peut considérablement améliorer votre trafic organique.</p>
    
    <h2>Techniques d'optimisation SEO</h2>
    <p>Il existe de nombreuses techniques pour optimiser le SEO de votre site WordPress. Voici les principales méthodes que nous recommandons pour améliorer votre référencement WordPress.</p>
    
    <p>Ce contenu fait plus de 300 mots et contient le mot-clé 'wordpress seo' plusieurs fois pour tester la densité des mots-clés.</p>
    ";
    
    echo "<h3>🔍 Lancement de l'analyse...</h3>";
    
    // Effectuer l'analyse
    $result = $analyzer->perform_content_analysis($post_id, $content);
    
    echo "<div style='background:#f0f8ff;padding:20px;border-radius:8px;margin:20px 0;'>";
    
    if ($result['success']) {
        echo "<h3 class='success'>✅ ANALYSE RÉUSSIE !</h3>";
        
        // Afficher le score
        $score = $result['score'];
        $score_class = 'success';
        if ($score < 60) $score_class = 'warning';
        if ($score < 40) $score_class = 'error';
        
        echo "<p><strong>📊 Score SEO :</strong> <span class='{$score_class}'>{$score}/100</span></p>";
        
        // Afficher les recommandations
        echo "<h4>💡 Recommandations :</h4>";
        if (!empty($result['recommendations'])) {
            echo "<ul>";
            foreach ($result['recommendations'] as $rec) {
                $class = $rec['type'] === 'critical' ? 'error' : ($rec['type'] === 'warning' ? 'warning' : 'info');
                echo "<li class='{$class}'>[{$rec['type']}] {$rec['text']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p class='info'>Aucune recommandation</p>";
        }
        
        // Afficher les détails
        if (!empty($result['details'])) {
            echo "<h4>📋 Détails de l'analyse :</h4>";
            foreach ($result['details'] as $section => $data) {
                echo "<p><strong>" . ucfirst($section) . " :</strong> Impact score = " . ($data['score_impact'] ?? 'N/A') . "</p>";
            }
        }
        
    } else {
        echo "<h3 class='error'>❌ ÉCHEC DE L'ANALYSE</h3>";
        echo "<p class='error'>Message : " . $result['message'] . "</p>";
        
        if (!empty($result['recommendations'])) {
            echo "<h4>Recommandations d'erreur :</h4>";
            echo "<ul>";
            foreach ($result['recommendations'] as $rec) {
                echo "<li class='error'>{$rec['text']}</li>";
            }
            echo "</ul>";
        }
    }
    
    echo "</div>";
    
    // Test des suggestions
    echo "<h3>🎯 Test des suggestions de mots-clés</h3>";
    
    try {
        $suggestions = $analyzer->generate_smart_suggestions($post_id, $content);
        
        if (!empty($suggestions)) {
            echo "<p class='success'>✅ Suggestions générées :</p>";
            echo "<ul>";
            foreach ($suggestions as $suggestion) {
                echo "<li class='info'>{$suggestion}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p class='warning'>⚠️ Aucune suggestion générée</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Erreur suggestions : " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h3 class='error'>❌ ERREUR CRITIQUE</h3>";
    echo "<p class='error'>Exception : " . $e->getMessage() . "</p>";
    echo "<p class='error'>Fichier : " . $e->getFile() . " ligne " . $e->getLine() . "</p>";
}

echo "<h2>🎯 Conclusion</h2>";
echo "<div style='background:#f9f9f9;padding:20px;border-radius:8px;'>";
echo "<p><strong>Si vous voyez un score et des recommandations ci-dessus, l'analyzer fonctionne maintenant correctement !</strong></p>";
echo "<p>Les corrections apportées :</p>";
echo "<ul>";
echo "<li class='success'>✅ Gestion d'erreurs avec try/catch</li>";
echo "<li class='success'>✅ Structure de retour garantie</li>";
echo "<li class='success'>✅ Sauvegarde automatique des résultats</li>";
echo "<li class='success'>✅ Recommandations par défaut si aucune</li>";
echo "<li class='success'>✅ Vérifications sécurisées pour l'IA</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
