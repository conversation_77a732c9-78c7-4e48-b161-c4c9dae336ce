<?php
/**
 * Classe principale pour le module e-commerce.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 */

/**
 * Classe principale pour le module e-commerce.
 *
 * Cette classe gère le module e-commerce.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/ecommerce
 * <AUTHOR> SEO Team
 */
class Boss_Ecommerce {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_ecommerce_';

    /**
     * L'instance de la classe Rich_Snippets.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Rich_Snippets    $rich_snippets    L'instance de la classe Rich_Snippets.
     */
    protected $rich_snippets;

    /**
     * L'instance de la classe Seo_Analyzer.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Seo_Analyzer    $seo_analyzer    L'instance de la classe Seo_Analyzer.
     */
    protected $seo_analyzer;

    /**
     * L'instance de la classe Product_Brands.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Product_Brands    $product_brands    L'instance de la classe Product_Brands.
     */
    protected $product_brands;

    /**
     * L'instance de la classe Ecommerce_Dashboard.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce_Dashboard    $ecommerce_dashboard    L'instance de la classe Ecommerce_Dashboard.
     */
    protected $ecommerce_dashboard;

    /**
     * L'instance de la classe Product_Optimizer.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Product_Optimizer    $product_optimizer    L'instance de la classe Product_Optimizer.
     */
    protected $product_optimizer;

    /**
     * L'instance de la classe Product_Generator.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Product_Generator    $product_generator    L'instance de la classe Product_Generator.
     */
    protected $product_generator;

    /**
     * L'instance de la classe Google_Shopping.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Google_Shopping    $google_shopping    L'instance de la classe Google_Shopping.
     */
    protected $google_shopping;

    /**
     * L'instance de la classe Ecommerce_Analysis.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce_Analysis    $ecommerce_analysis    L'instance de la classe Ecommerce_Analysis.
     */
    protected $ecommerce_analysis;

    /**
     * L'instance de la classe Ecommerce_Dashboard_Fix.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce_Dashboard_Fix    $ecommerce_dashboard_fix    L'instance de la classe Ecommerce_Dashboard_Fix.
     */
    protected $ecommerce_dashboard_fix;

    /**
     * L'instance de la classe Ecommerce_Products_Fix.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce_Products_Fix    $ecommerce_products_fix    L'instance de la classe Ecommerce_Products_Fix.
     */
    protected $ecommerce_products_fix;

    /**
     * L'instance de la classe Ecommerce_Routes_Debug.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Ecommerce_Routes_Debug    $ecommerce_routes_debug    L'instance de la classe Ecommerce_Routes_Debug.
     */
    protected $ecommerce_routes_debug;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_modules();
    }

    /**
     * Charge les dépendances du module.
     *
     * @since    1.2.0
     */
    private function load_dependencies() {
        /**
         * La classe qui gère les extraits enrichis.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-rich-snippets.php';

        /**
         * La classe qui gère l'analyse SEO des produits.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-seo-analyzer.php';

        /**
         * La classe qui gère les marques de produits.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-product-brands.php';

        /**
         * La classe qui gère le tableau de bord e-commerce.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-ecommerce-dashboard.php';

        /**
         * La classe qui gère le tableau de bord e-commerce (correction).
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-ecommerce-dashboard-fix.php';

        /**
         * La classe qui gère les produits e-commerce (correction).
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-ecommerce-products-fix.php';

        /**
         * La classe qui gère le débogage des routes REST API.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-ecommerce-routes-debug.php';

        /**
         * La classe qui gère l'optimisation des produits.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-product-optimizer.php';

        /**
         * La classe qui gère le générateur IA de fiches produit.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-product-generator.php';

        /**
         * La classe qui gère Google Shopping.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-google-shopping.php';

        /**
         * La classe qui gère l'analyse SEO e-commerce.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/class-boss-ecommerce-analysis.php';
    }

    /**
     * Initialise les modules.
     *
     * @since    1.2.0
     */
    private function init_modules() {
        $this->rich_snippets = new Boss_Rich_Snippets( $this->plugin_name, $this->version );
        $this->seo_analyzer = new Boss_Seo_Analyzer( $this->plugin_name, $this->version );
        $this->product_brands = new Boss_Product_Brands( $this->plugin_name, $this->version );
        $this->ecommerce_dashboard = new Boss_Ecommerce_Dashboard( $this->plugin_name, $this->version );
        $this->ecommerce_dashboard_fix = new Boss_Ecommerce_Dashboard_Fix();
        $this->ecommerce_products_fix = new Boss_Ecommerce_Products_Fix();
        $this->ecommerce_routes_debug = new Boss_Ecommerce_Routes_Debug();
        $this->product_optimizer = new Boss_Product_Optimizer( $this->plugin_name, $this->version );
        $this->product_generator = new Boss_Product_Generator( $this->plugin_name, $this->version );
        $this->google_shopping = new Boss_Google_Shopping( $this->plugin_name, $this->version );
        $this->ecommerce_analysis = new Boss_Ecommerce_Analysis( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Enregistrer les hooks des modules
        $this->rich_snippets->register_hooks();
        $this->seo_analyzer->register_hooks();
        $this->product_brands->register_hooks();
        $this->ecommerce_dashboard->register_hooks();
        $this->product_optimizer->register_hooks();
        $this->product_generator->register_hooks();
        $this->google_shopping->register_hooks();
        $this->ecommerce_analysis->register_hooks();

        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_get_ecommerce_settings', array( $this, 'ajax_get_ecommerce_settings' ) );
        add_action( 'wp_ajax_boss_seo_save_ecommerce_settings', array( $this, 'ajax_save_ecommerce_settings' ) );

        // Ajouter les actions pour l'interface d'administration
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );

        // Ajouter les actions pour les métaboxes
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
        add_action( 'save_post_product', array( $this, 'save_meta_boxes' ) );

        // Ajouter les actions pour les onglets de produit WooCommerce
        add_filter( 'woocommerce_product_data_tabs', array( $this, 'add_product_data_tabs' ) );
        add_action( 'woocommerce_product_data_panels', array( $this, 'add_product_data_panels' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API des modules
        $this->rich_snippets->register_rest_routes();
        $this->seo_analyzer->register_rest_routes();
        $this->product_brands->register_rest_routes();
        $this->ecommerce_dashboard->register_rest_routes();
        $this->ecommerce_dashboard_fix->register_rest_routes();
        $this->ecommerce_products_fix->register_rest_routes();
        $this->product_optimizer->register_rest_routes();
        $this->product_generator->register_rest_routes();
        $this->google_shopping->register_rest_routes();
        $this->ecommerce_analysis->register_rest_routes();

        register_rest_route(
            'boss-seo/v1',
            '/ecommerce/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_ecommerce_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_ecommerce_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les paramètres du module e-commerce.
     *
     * @since    1.2.0
     */
    public function ajax_get_ecommerce_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les paramètres
        $settings = $this->get_settings();

        wp_send_json_success( array(
            'message'  => __( 'Paramètres récupérés avec succès.', 'boss-seo' ),
            'settings' => $settings,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer les paramètres du module e-commerce.
     *
     * @since    1.2.0
     */
    public function ajax_save_ecommerce_settings() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_ecommerce_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['settings'] ) || ! is_array( $_POST['settings'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Paramètres invalides.', 'boss-seo' ) ) );
        }

        $settings = $_POST['settings'];

        // Enregistrer les paramètres
        $result = $this->save_settings( $settings );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Paramètres enregistrés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les paramètres du module e-commerce via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_ecommerce_settings( $request ) {
        $settings = $this->get_settings();

        return rest_ensure_response( $settings );
    }

    /**
     * Enregistre les paramètres du module e-commerce via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_ecommerce_settings( $request ) {
        $params = $request->get_params();

        if ( ! isset( $params['settings'] ) || ! is_array( $params['settings'] ) ) {
            return new WP_Error( 'invalid_settings', __( 'Paramètres invalides.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $settings = $params['settings'];

        $result = $this->save_settings( $settings );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return rest_ensure_response( array(
            'message' => __( 'Paramètres enregistrés avec succès.', 'boss-seo' ),
        ) );
    }

    /**
     * Ajoute les éléments de menu pour le module e-commerce.
     *
     * @since    1.2.0
     */
    public function add_admin_menu() {
        // Menu désactivé pour éviter l'ajout d'un élément de menu supplémentaire
        // Le module e-commerce sera intégré dans une autre section du plugin
    }

    /**
     * Enregistre les scripts et les styles pour l'interface d'administration.
     *
     * @since    1.2.0
     * @param    string    $hook    Le hook de la page actuelle.
     */
    public function enqueue_admin_scripts( $hook ) {
        if ( 'boss-seo_page_boss-seo-ecommerce' !== $hook ) {
            return;
        }

        wp_enqueue_style( 'boss-seo-ecommerce', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/css/boss-seo-ecommerce.css', array(), $this->version, 'all' );
        wp_enqueue_script( 'boss-seo-ecommerce', plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/js/boss-seo-ecommerce.js', array( 'jquery' ), $this->version, false );

        wp_localize_script( 'boss-seo-ecommerce', 'boss_seo_ecommerce', array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce'    => wp_create_nonce( 'boss_seo_ecommerce_nonce' ),
        ) );
    }

    /**
     * Affiche la page d'administration du module e-commerce.
     *
     * @since    1.2.0
     */
    public function render_admin_page() {
        // Inclure le template de la page d'administration
        include_once plugin_dir_path( dirname( __FILE__ ) ) . 'ecommerce/partials/boss-ecommerce-admin-display.php';
    }

    /**
     * Ajoute les métaboxes pour les produits.
     *
     * @since    1.2.0
     */
    public function add_meta_boxes() {
        add_meta_box(
            'boss_seo_rich_snippets',
            __( 'Extraits enrichis', 'boss-seo' ),
            array( $this->rich_snippets, 'render_meta_box' ),
            'product',
            'normal',
            'default'
        );

        add_meta_box(
            'boss_seo_analyzer',
            __( 'Analyse SEO', 'boss-seo' ),
            array( $this->seo_analyzer, 'render_meta_box' ),
            'product',
            'normal',
            'default'
        );

        add_meta_box(
            'boss_seo_product_optimizer',
            __( 'Optimisation du produit', 'boss-seo' ),
            array( $this->product_optimizer, 'render_meta_box' ),
            'product',
            'normal',
            'default'
        );

        add_meta_box(
            'boss_seo_product_generator',
            __( 'Générateur IA de fiche produit', 'boss-seo' ),
            array( $this->product_generator, 'render_meta_box' ),
            'product',
            'normal',
            'default'
        );
    }

    /**
     * Enregistre les métaboxes pour les produits.
     *
     * @since    1.2.0
     * @param    int    $post_id    L'ID du produit.
     */
    public function save_meta_boxes( $post_id ) {
        $this->rich_snippets->save_meta( $post_id );
        $this->seo_analyzer->save_meta( $post_id );
        $this->product_optimizer->save_product_optimization( $post_id );
    }

    /**
     * Ajoute les onglets de données de produit WooCommerce.
     *
     * @since    1.2.0
     * @param    array    $tabs    Les onglets existants.
     * @return   array             Les onglets modifiés.
     */
    public function add_product_data_tabs( $tabs ) {
        $tabs['boss_seo_rich_snippets'] = array(
            'label'    => __( 'Extraits enrichis', 'boss-seo' ),
            'target'   => 'boss_seo_rich_snippets',
            'class'    => array(),
            'priority' => 70,
        );

        $tabs['boss_seo_analyzer'] = array(
            'label'    => __( 'Analyse SEO', 'boss-seo' ),
            'target'   => 'boss_seo_analyzer',
            'class'    => array(),
            'priority' => 71,
        );

        return $tabs;
    }

    /**
     * Ajoute les panneaux de données de produit WooCommerce.
     *
     * @since    1.2.0
     */
    public function add_product_data_panels() {
        ?>
        <div id="boss_seo_rich_snippets" class="panel woocommerce_options_panel">
            <?php $this->rich_snippets->render_product_data_panel(); ?>
        </div>
        <div id="boss_seo_analyzer" class="panel woocommerce_options_panel">
            <?php $this->seo_analyzer->render_product_data_panel(); ?>
        </div>
        <?php
    }

    /**
     * Récupère les paramètres du module e-commerce.
     *
     * @since    1.2.0
     * @return   array    Les paramètres.
     */
    private function get_settings() {
        $default_settings = array(
            'enable_rich_snippets'      => true,
            'enable_seo_analyzer'       => true,
            'enable_product_brands'     => true,
            'enable_product_optimizer'  => true,
            'enable_product_generator'  => true,
            'enable_google_shopping'    => true,
            'rich_snippets_types'       => array( 'product', 'offer', 'review', 'aggregate_rating' ),
            'auto_analyze'              => true,
            'analyze_elements'          => array( 'title', 'description', 'images', 'categories_tags', 'attributes', 'price', 'stock', 'reviews', 'sku', 'permalink' ),
            'product_generator_api_key' => '',
            'google_shopping_merchant_id' => '',
        );

        $settings = get_option( $this->option_prefix . 'settings', $default_settings );

        return $settings;
    }

    /**
     * Enregistre les paramètres du module e-commerce.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres à enregistrer.
     * @return   bool|WP_Error         True si les paramètres ont été enregistrés, une erreur sinon.
     */
    private function save_settings( $settings ) {
        // Valider les paramètres
        $valid_settings = array();

        // Valider les paramètres booléens
        $valid_settings['enable_rich_snippets'] = isset( $settings['enable_rich_snippets'] ) && $settings['enable_rich_snippets'];
        $valid_settings['enable_seo_analyzer'] = isset( $settings['enable_seo_analyzer'] ) && $settings['enable_seo_analyzer'];
        $valid_settings['enable_product_brands'] = isset( $settings['enable_product_brands'] ) && $settings['enable_product_brands'];
        $valid_settings['enable_product_optimizer'] = isset( $settings['enable_product_optimizer'] ) && $settings['enable_product_optimizer'];
        $valid_settings['enable_product_generator'] = isset( $settings['enable_product_generator'] ) && $settings['enable_product_generator'];
        $valid_settings['enable_google_shopping'] = isset( $settings['enable_google_shopping'] ) && $settings['enable_google_shopping'];
        $valid_settings['auto_analyze'] = isset( $settings['auto_analyze'] ) && $settings['auto_analyze'];

        // Valider les types d'extraits enrichis
        $valid_settings['rich_snippets_types'] = array();

        if ( isset( $settings['rich_snippets_types'] ) && is_array( $settings['rich_snippets_types'] ) ) {
            $valid_types = array( 'product', 'offer', 'review', 'aggregate_rating' );

            foreach ( $settings['rich_snippets_types'] as $type ) {
                if ( in_array( $type, $valid_types ) ) {
                    $valid_settings['rich_snippets_types'][] = $type;
                }
            }
        }

        // Valider les éléments à analyser
        $valid_settings['analyze_elements'] = array();

        if ( isset( $settings['analyze_elements'] ) && is_array( $settings['analyze_elements'] ) ) {
            $valid_elements = array( 'title', 'description', 'images', 'categories_tags', 'attributes', 'price', 'stock', 'reviews', 'sku', 'permalink' );

            foreach ( $settings['analyze_elements'] as $element ) {
                if ( in_array( $element, $valid_elements ) ) {
                    $valid_settings['analyze_elements'][] = $element;
                }
            }
        }

        // Valider les paramètres de l'API du générateur de produits
        if ( isset( $settings['product_generator_api_key'] ) ) {
            $valid_settings['product_generator_api_key'] = sanitize_text_field( $settings['product_generator_api_key'] );
        }

        // Valider les paramètres de Google Shopping
        if ( isset( $settings['google_shopping_merchant_id'] ) ) {
            $valid_settings['google_shopping_merchant_id'] = sanitize_text_field( $settings['google_shopping_merchant_id'] );
        }

        // Enregistrer les paramètres
        $result = update_option( $this->option_prefix . 'settings', $valid_settings );

        if ( ! $result ) {
            return new WP_Error( 'save_failed', __( 'L\'enregistrement des paramètres a échoué.', 'boss-seo' ) );
        }

        return true;
    }
}
