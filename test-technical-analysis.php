<?php
/**
 * Script de test pour les nouvelles fonctionnalités d'analyse technique
 *
 * Ce fichier peut être exécuté pour tester les nouvelles classes et méthodes
 *
 * Usage: php test-technical-analysis.php
 */

// Simuler l'environnement WordPress pour les tests
define('ABSPATH', __DIR__ . '/');
define('BOSS_SEO_PLUGIN_DIR', __DIR__ . '/');

// Fonctions WordPress simulées pour les tests
if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('sprintf')) {
    // sprintf existe déjà en PHP
}

if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        // Simuler les options pour les tests
        $options = [
            'boss_optimizer_external_services' => [
                'google_pagespeed' => [
                    'api_key' => 'test_api_key_123'
                ]
            ]
        ];

        return isset($options[$option]) ? $options[$option] : $default;
    }
}

if (!function_exists('set_transient')) {
    function set_transient($transient, $value, $expiration) {
        return true;
    }
}

if (!function_exists('get_transient')) {
    function get_transient($transient) {
        return false; // Pas de cache pour les tests
    }
}

if (!function_exists('wp_remote_get')) {
    function wp_remote_get($url, $args = []) {
        // Simuler une réponse PageSpeed Insights pour les tests
        if (strpos($url, 'pagespeedonline') !== false) {
            return [
                'response' => ['code' => 200],
                'body' => json_encode([
                    'lighthouseResult' => [
                        'categories' => [
                            'performance' => ['title' => 'Performance', 'score' => 0.75],
                            'seo' => ['title' => 'SEO', 'score' => 0.85],
                            'accessibility' => ['title' => 'Accessibility', 'score' => 0.80],
                            'best-practices' => ['title' => 'Best Practices', 'score' => 0.70]
                        ],
                        'audits' => [
                            'largest-contentful-paint' => [
                                'numericValue' => 2800,
                                'score' => 0.75
                            ],
                            'interaction-to-next-paint' => [
                                'numericValue' => 150,
                                'score' => 0.90
                            ],
                            'cumulative-layout-shift' => [
                                'numericValue' => 0.08,
                                'score' => 0.85
                            ],
                            'server-response-time' => [
                                'numericValue' => 600,
                                'score' => 0.70
                            ],
                            'first-contentful-paint' => [
                                'numericValue' => 1600,
                                'score' => 0.85
                            ]
                        ]
                    ]
                ])
            ];
        }

        // Simuler une page HTML pour les tests Schema/Hreflang
        return [
            'response' => ['code' => 200],
            'body' => '
                <!DOCTYPE html>
                <html>
                <head>
                    <script type="application/ld+json">
                    {
                        "@context": "https://schema.org",
                        "@type": "Organization",
                        "name": "Test Company",
                        "url": "https://example.com"
                    }
                    </script>
                    <link rel="alternate" hreflang="en" href="https://example.com/en/" />
                    <link rel="alternate" hreflang="fr" href="https://example.com/fr/" />
                    <link rel="alternate" hreflang="x-default" href="https://example.com/" />
                </head>
                <body>
                    <h1>Test Page</h1>
                </body>
                </html>
            '
        ];
    }
}

if (!function_exists('wp_remote_retrieve_response_code')) {
    function wp_remote_retrieve_response_code($response) {
        return $response['response']['code'];
    }
}

if (!function_exists('wp_remote_retrieve_body')) {
    function wp_remote_retrieve_body($response) {
        return $response['body'];
    }
}

if (!function_exists('wp_remote_head')) {
    function wp_remote_head($url, $args = []) {
        return ['response' => ['code' => 200]];
    }
}

if (!function_exists('is_wp_error')) {
    function is_wp_error($thing) {
        return false;
    }
}

if (!function_exists('current_time')) {
    function current_time($type) {
        return date('Y-m-d H:i:s');
    }
}

if (!function_exists('home_url')) {
    function home_url() {
        return 'https://example.com';
    }
}

if (!function_exists('error_log')) {
    // error_log existe déjà en PHP
}

if (!function_exists('add_query_arg')) {
    function add_query_arg($args, $url = '') {
        if (empty($url)) {
            $url = 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed';
        }

        $query_string = http_build_query($args);
        $separator = (strpos($url, '?') !== false) ? '&' : '?';

        return $url . $separator . $query_string;
    }
}

if (!function_exists('update_option')) {
    function update_option($option, $value, $autoload = null) {
        return true;
    }
}

// Inclure les classes à tester
require_once 'includes/class-boss-pagespeed-manager.php';
require_once 'includes/class-boss-schema-analyzer.php';

echo "🚀 Test des nouvelles fonctionnalités d'analyse technique Boss SEO\n";
echo "================================================================\n\n";

// Test 1: PageSpeed Manager
echo "📊 Test 1: Boss_PageSpeed_Manager\n";
echo "---------------------------------\n";

try {
    $pagespeed_manager = new Boss_PageSpeed_Manager();
    echo "✅ Classe Boss_PageSpeed_Manager instanciée avec succès\n";

    // Test de l'analyse d'URL
    $results = $pagespeed_manager->analyze_url('https://example.com', 'mobile');

    if ($results) {
        echo "✅ Analyse PageSpeed réussie\n";
        echo "   - URL: " . $results['url'] . "\n";
        echo "   - Stratégie: " . $results['strategy'] . "\n";
        echo "   - Scores: " . count($results['scores']) . " catégories\n";
        echo "   - Core Web Vitals: " . count($results['core_web_vitals']) . " métriques\n";
        echo "   - Opportunités: " . count($results['opportunities']) . " trouvées\n";

        // Afficher quelques métriques
        foreach ($results['core_web_vitals'] as $metric => $data) {
            echo "   - {$data['name']}: {$data['value']}{$data['unit']} ({$data['status']})\n";
        }
    } else {
        echo "❌ Échec de l'analyse PageSpeed\n";
    }

    // Test du cache
    echo "\n🗄️  Test du cache:\n";
    $cached_results = $pagespeed_manager->analyze_url('https://example.com', 'mobile');
    echo "✅ Récupération depuis le cache réussie\n";

    // Test de validation de clé API
    echo "\n🔑 Test de validation de clé API:\n";
    $api_test = $pagespeed_manager->test_api_key();
    if ($api_test['success']) {
        echo "✅ Clé API valide\n";
    } else {
        echo "⚠️  Test de clé API: " . $api_test['message'] . "\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur PageSpeed Manager: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Schema Analyzer
echo "🏗️  Test 2: Boss_Schema_Analyzer\n";
echo "--------------------------------\n";

try {
    $schema_analyzer = new Boss_Schema_Analyzer();
    echo "✅ Classe Boss_Schema_Analyzer instanciée avec succès\n";

    // Test de l'analyse Schema
    $schema_results = $schema_analyzer->analyze_url('https://example.com');

    if ($schema_results['success']) {
        echo "✅ Analyse Schema réussie\n";
        echo "   - URL: " . $schema_results['url'] . "\n";
        echo "   - Schemas trouvés: " . $schema_results['schemas_found'] . "\n";
        echo "   - Issues: " . count($schema_results['issues']) . "\n";
        echo "   - Recommandations: " . count($schema_results['recommendations']) . "\n";
        echo "   - Score: " . $schema_results['score'] . "/100\n";

        // Afficher les schemas trouvés
        foreach ($schema_results['schemas'] as $schema) {
            echo "   - Schema {$schema['schema_type']} ({$schema['type']})\n";
        }

        // Afficher les issues
        foreach ($schema_results['issues'] as $issue) {
            echo "   - Issue: {$issue['message']} ({$issue['severity']})\n";
        }
    } else {
        echo "❌ Échec de l'analyse Schema: " . $schema_results['message'] . "\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur Schema Analyzer: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Simulation d'intégration avec la classe d'analyse technique
echo "🔧 Test 3: Intégration avec l'analyse technique\n";
echo "-----------------------------------------------\n";

// Simuler l'utilisation dans la classe d'analyse technique
try {
    // Test de récupération des données PageSpeed avec le nouveau gestionnaire
    if (!class_exists('Boss_PageSpeed_Manager')) {
        require_once 'includes/class-boss-pagespeed-manager.php';
    }

    $pagespeed_manager = new Boss_PageSpeed_Manager();
    $categories = ['performance', 'seo', 'accessibility', 'best-practices'];

    $pagespeed_results = $pagespeed_manager->analyze_url(home_url(), 'mobile', $categories);

    if ($pagespeed_results) {
        echo "✅ Intégration PageSpeed réussie\n";
        echo "   - Compatible avec l'ancienne interface\n";
        echo "   - Nouvelles métriques disponibles\n";
        echo "   - Cache et retry implémentés\n";
    }

    // Test d'analyse Schema
    $schema_analyzer = new Boss_Schema_Analyzer();
    $schema_results = $schema_analyzer->analyze_url(home_url());

    if ($schema_results['success']) {
        echo "✅ Intégration Schema réussie\n";
        echo "   - Prêt pour l'API REST\n";
        echo "   - Interface utilisateur compatible\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur d'intégration: " . $e->getMessage() . "\n";
}

echo "\n";

// Résumé des améliorations
echo "📈 Résumé des améliorations implémentées\n";
echo "========================================\n";
echo "✅ Gestionnaire PageSpeed Insights avec cache et retry\n";
echo "✅ Support de la métrique INP (remplace FID)\n";
echo "✅ Seuils Core Web Vitals mis à jour (2024)\n";
echo "✅ Analyseur Schema Markup complet\n";
echo "✅ Analyseur Hreflang pour le SEO international\n";
echo "✅ Gestion d'erreurs robuste\n";
echo "✅ Interface utilisateur améliorée\n";
echo "✅ Nouvelles routes API REST\n";
echo "✅ Composants React pour les audits avancés\n";
echo "✅ Styles CSS pour une meilleure UX\n";

echo "\n🎉 Tous les tests sont terminés !\n";
echo "\nPour utiliser ces fonctionnalités :\n";
echo "1. Configurez votre clé API PageSpeed Insights\n";
echo "2. Accédez au module Analyse technique\n";
echo "3. Explorez les nouveaux onglets Schema et Hreflang\n";
echo "4. Profitez des Core Web Vitals améliorés\n";
?>
