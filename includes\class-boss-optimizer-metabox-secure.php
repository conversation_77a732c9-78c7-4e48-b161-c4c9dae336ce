<?php
/**
 * Version sécurisée et optimisée de la Meta Box Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe Meta Box sécurisée pour Boss SEO.
 *
 * Cette version corrige tous les problèmes de sécurité, performance et UX
 * identifiés dans l'analyse expert.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Metabox_Secure {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance des paramètres.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Instance des paramètres.
     */
    protected $settings;

    /**
     * Types de posts supportés.
     *
     * @since    1.2.0
     * @access   protected
     * @var      array    $supported_post_types    Types de posts supportés.
     */
    protected $supported_post_types;

    /**
     * Cache des validations.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $validation_cache    Cache des validations.
     */
    private $validation_cache = array();

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string                     $plugin_name    Le nom du plugin.
     * @param    string                     $version        La version du plugin.
     * @param    Boss_Optimizer_Settings    $settings       Instance des paramètres.
     */
    public function __construct( $plugin_name, $version, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->settings = $settings;

        // Types de posts supportés avec validation
        $this->supported_post_types = apply_filters( 'boss_seo_supported_post_types', array( 'post', 'page', 'product' ) );

        // Valider les types de posts
        $this->supported_post_types = array_filter( $this->supported_post_types, function( $post_type ) {
            return post_type_exists( $post_type );
        } );
    }

    /**
     * Enregistre les hooks nécessaires pour les meta boxes.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les meta boxes seulement sur les pages nécessaires
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );

        // Enregistrer les données avec validation renforcée
        add_action( 'save_post', array( $this, 'save_meta_boxes_secure' ), 10, 2 );

        // Charger les scripts seulement quand nécessaire
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts_conditional' ) );

        // Ajouter les hooks AJAX sécurisés
        add_action( 'wp_ajax_boss_seo_analyze_content_secure', array( $this, 'ajax_analyze_content' ) );
        add_action( 'wp_ajax_boss_seo_optimize_content_secure', array( $this, 'ajax_optimize_content' ) );
        add_action( 'wp_ajax_boss_seo_validate_metadata', array( $this, 'ajax_validate_metadata' ) );
    }

    /**
     * Ajoute les meta boxes dans l'éditeur WordPress.
     *
     * @since    1.2.0
     */
    public function add_meta_boxes() {
        $current_screen = get_current_screen();

        // Vérifier si on est sur une page d'édition supportée
        if ( ! $current_screen || ! in_array( $current_screen->post_type, $this->supported_post_types ) ) {
            return;
        }

        foreach ( $this->supported_post_types as $post_type ) {
            add_meta_box(
                'boss_seo_metabox_secure',
                __( 'Boss SEO - Optimisation Avancée', 'boss-seo' ),
                array( $this, 'render_meta_box_secure' ),
                $post_type,
                'normal',
                'high',
                array(
                    '__back_compat_meta_box' => false, // Désactiver la compatibilité ancienne
                    '__block_editor_compatible_meta_box' => true // Compatible Gutenberg
                )
            );
        }
    }

    /**
     * Affiche le contenu sécurisé de la meta box.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post actuel.
     */
    public function render_meta_box_secure( $post ) {
        // Générer un nonce unique pour cette meta box et ce post
        $nonce_action = 'boss_seo_meta_box_secure_' . $post->ID;
        $nonce_name = 'boss_seo_meta_box_secure_nonce';

        wp_nonce_field( $nonce_action, $nonce_name );

        // Récupérer et valider les métadonnées existantes
        $metadata = $this->get_validated_metadata( $post->ID );

        // Récupérer les suggestions de mots-clés sécurisées
        $keyword_suggestions = $this->get_secure_keyword_suggestions( $post );

        // Afficher l'interface sécurisée
        $this->render_secure_interface( $post, $metadata, $keyword_suggestions );
    }

    /**
     * Récupère les métadonnées validées d'un post.
     *
     * @since    1.2.0
     * @param    int    $post_id    ID du post.
     * @return   array              Métadonnées validées.
     */
    private function get_validated_metadata( $post_id ) {
        $cache_key = "metadata_{$post_id}";

        if ( isset( $this->validation_cache[ $cache_key ] ) ) {
            return $this->validation_cache[ $cache_key ];
        }

        $metadata = array(
            'meta_description' => get_post_meta( $post_id, '_boss_seo_meta_description', true ),
            'focus_keyword' => get_post_meta( $post_id, '_boss_seo_focus_keyword', true ),
            'secondary_keywords' => get_post_meta( $post_id, '_boss_seo_secondary_keywords', true ),
            'seo_title' => get_post_meta( $post_id, '_boss_seo_title', true ),
            'canonical_url' => get_post_meta( $post_id, '_boss_seo_canonical_url', true ),
            'robots_index' => get_post_meta( $post_id, '_boss_seo_robots_index', true ) ?: 'index',
            'robots_follow' => get_post_meta( $post_id, '_boss_seo_robots_follow', true ) ?: 'follow',
            'og_title' => get_post_meta( $post_id, '_boss_seo_og_title', true ),
            'og_description' => get_post_meta( $post_id, '_boss_seo_og_description', true ),
            'og_image' => get_post_meta( $post_id, '_boss_seo_og_image', true ),
            'twitter_title' => get_post_meta( $post_id, '_boss_seo_twitter_title', true ),
            'twitter_description' => get_post_meta( $post_id, '_boss_seo_twitter_description', true ),
            'twitter_image' => get_post_meta( $post_id, '_boss_seo_twitter_image', true ),
            'twitter_card_type' => get_post_meta( $post_id, '_boss_seo_twitter_card_type', true ) ?: 'summary_large_image',
            'seo_score' => get_post_meta( $post_id, '_boss_seo_score', true ),
            'analysis_date' => get_post_meta( $post_id, '_boss_seo_analysis_date', true ),
            'recommendations' => get_post_meta( $post_id, '_boss_seo_recommendations', true )
        );

        // Valider et nettoyer chaque champ
        $metadata = $this->validate_metadata_fields( $metadata );

        // Mettre en cache
        $this->validation_cache[ $cache_key ] = $metadata;

        return $metadata;
    }

    /**
     * Valide et nettoie les champs de métadonnées.
     *
     * @since    1.2.0
     * @param    array    $metadata    Métadonnées à valider.
     * @return   array                 Métadonnées validées.
     */
    private function validate_metadata_fields( $metadata ) {
        // Validation de la meta description
        if ( ! empty( $metadata['meta_description'] ) ) {
            $metadata['meta_description'] = wp_strip_all_tags( $metadata['meta_description'] );
            $metadata['meta_description'] = substr( $metadata['meta_description'], 0, 320 ); // Limite stricte
        }

        // Validation du titre SEO
        if ( ! empty( $metadata['seo_title'] ) ) {
            $metadata['seo_title'] = wp_strip_all_tags( $metadata['seo_title'] );
            $metadata['seo_title'] = substr( $metadata['seo_title'], 0, 120 ); // Limite stricte
        }

        // Validation de l'URL canonique
        if ( ! empty( $metadata['canonical_url'] ) ) {
            $metadata['canonical_url'] = esc_url_raw( $metadata['canonical_url'] );
            if ( ! filter_var( $metadata['canonical_url'], FILTER_VALIDATE_URL ) ) {
                $metadata['canonical_url'] = '';
            }
        }

        // Validation des mots-clés
        if ( ! empty( $metadata['focus_keyword'] ) ) {
            $metadata['focus_keyword'] = sanitize_text_field( $metadata['focus_keyword'] );
            $metadata['focus_keyword'] = substr( $metadata['focus_keyword'], 0, 100 );
        }

        if ( ! empty( $metadata['secondary_keywords'] ) ) {
            $metadata['secondary_keywords'] = sanitize_text_field( $metadata['secondary_keywords'] );
            $metadata['secondary_keywords'] = substr( $metadata['secondary_keywords'], 0, 500 );
        }

        // Validation des robots
        $valid_robots_index = array( 'index', 'noindex' );
        $valid_robots_follow = array( 'follow', 'nofollow' );

        if ( ! in_array( $metadata['robots_index'], $valid_robots_index ) ) {
            $metadata['robots_index'] = 'index';
        }

        if ( ! in_array( $metadata['robots_follow'], $valid_robots_follow ) ) {
            $metadata['robots_follow'] = 'follow';
        }

        // Validation des images Open Graph et Twitter
        foreach ( array( 'og_image', 'twitter_image' ) as $image_field ) {
            if ( ! empty( $metadata[ $image_field ] ) ) {
                $metadata[ $image_field ] = esc_url_raw( $metadata[ $image_field ] );
                if ( ! filter_var( $metadata[ $image_field ], FILTER_VALIDATE_URL ) ) {
                    $metadata[ $image_field ] = '';
                }
            }
        }

        // Validation du type de carte Twitter
        $valid_twitter_cards = array( 'summary', 'summary_large_image' );
        if ( ! in_array( $metadata['twitter_card_type'], $valid_twitter_cards ) ) {
            $metadata['twitter_card_type'] = 'summary_large_image';
        }

        // Validation du score SEO
        if ( ! empty( $metadata['seo_score'] ) ) {
            $metadata['seo_score'] = max( 0, min( 100, intval( $metadata['seo_score'] ) ) );
        }

        return $metadata;
    }

    /**
     * Récupère des suggestions de mots-clés sécurisées.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    L'objet post actuel.
     * @return   array               Suggestions de mots-clés sécurisées.
     */
    private function get_secure_keyword_suggestions( $post ) {
        // Vérifier la validité du post
        if ( empty( $post ) || empty( $post->post_content ) || strlen( $post->post_content ) < 100 ) {
            return array();
        }

        // Nettoyer et valider le contenu
        $content = wp_strip_all_tags( $post->post_content );
        $content = wp_specialchars_decode( $content );
        $content = preg_replace( '/\s+/', ' ', $content );

        if ( strlen( $content ) < 100 ) {
            return array();
        }

        // Liste étendue et mise à jour des stop words français
        $stop_words = $this->get_french_stop_words();

        // Analyser le contenu de manière sécurisée
        $suggestions = $this->extract_keywords_secure( $content, $stop_words );

        // Limiter et valider les suggestions
        $suggestions = array_slice( $suggestions, 0, 8 );
        $suggestions = array_map( 'sanitize_text_field', $suggestions );
        $suggestions = array_filter( $suggestions, function( $suggestion ) {
            return strlen( $suggestion ) >= 3 && strlen( $suggestion ) <= 50;
        } );

        return array_values( $suggestions );
    }

    /**
     * Récupère la liste complète des stop words français.
     *
     * @since    1.2.0
     * @return   array    Liste des stop words.
     */
    private function get_french_stop_words() {
        return array(
            // Articles
            'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'da', 'au', 'aux',
            // Prépositions
            'à', 'avec', 'par', 'pour', 'en', 'vers', 'sur', 'sous', 'dans', 'sans', 'entre', 'parmi', 'chez', 'depuis', 'pendant', 'avant', 'après', 'jusqu',
            // Conjonctions
            'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or',
            // Pronoms
            'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'me', 'te', 'se', 'lui', 'leur', 'ce', 'cette', 'ces', 'cet', 'mon', 'ton', 'son', 'notre', 'votre', 'leur', 'mes', 'tes', 'ses', 'nos', 'vos', 'leurs',
            // Interrogatifs
            'qui', 'que', 'quoi', 'dont', 'où', 'comment', 'pourquoi', 'quand', 'combien',
            // Verbes auxiliaires et fréquents
            'être', 'avoir', 'faire', 'dire', 'aller', 'voir', 'savoir', 'pouvoir', 'vouloir', 'falloir', 'devoir', 'est', 'sont', 'était', 'étaient', 'sera', 'seront', 'été', 'ai', 'as', 'a', 'avons', 'avez', 'ont', 'avait', 'avaient', 'aura', 'auront', 'eu',
            // Adverbes
            'très', 'peu', 'plus', 'moins', 'autant', 'aussi', 'bien', 'mal', 'vite', 'lentement', 'souvent', 'toujours', 'jamais', 'parfois', 'alors', 'ensuite', 'enfin', 'puis', 'soudain', 'déjà', 'encore', 'ici', 'là', 'partout', 'nulle', 'ailleurs',
            // Déterminants
            'tout', 'tous', 'toute', 'toutes', 'aucun', 'aucune', 'chaque', 'plusieurs', 'certain', 'certaine', 'certains', 'certaines', 'quelque', 'quelques', 'autre', 'autres'
        );
    }

    /**
     * Extrait les mots-clés de manière sécurisée.
     *
     * @since    1.2.0
     * @param    string    $content      Contenu à analyser.
     * @param    array     $stop_words   Mots à ignorer.
     * @return   array                   Mots-clés extraits.
     */
    private function extract_keywords_secure( $content, $stop_words ) {
        // Normaliser le contenu
        $content = strtolower( $content );
        $content = preg_replace( '/[^\p{L}\p{N}\s]/u', ' ', $content );
        $content = preg_replace( '/\s+/', ' ', $content );
        $content = trim( $content );

        // Extraire les mots individuels
        $words = explode( ' ', $content );
        $words = array_filter( $words, function( $word ) use ( $stop_words ) {
            return strlen( $word ) >= 3 && strlen( $word ) <= 25 && ! in_array( $word, $stop_words );
        } );

        // Compter les occurrences
        $word_counts = array_count_values( $words );
        arsort( $word_counts );

        // Extraire les expressions de 2-3 mots
        $phrases = $this->extract_phrases_secure( $content, $stop_words );

        // Combiner mots et expressions
        $keywords = array_slice( array_keys( $word_counts ), 0, 5 );
        $top_phrases = array_slice( array_keys( $phrases ), 0, 3 );

        return array_merge( $keywords, $top_phrases );
    }

    /**
     * Extrait les expressions de manière sécurisée.
     *
     * @since    1.2.0
     * @param    string    $content      Contenu à analyser.
     * @param    array     $stop_words   Mots à ignorer.
     * @return   array                   Expressions extraites.
     */
    private function extract_phrases_secure( $content, $stop_words ) {
        $words = explode( ' ', $content );
        $phrases = array();

        for ( $i = 0; $i < count( $words ) - 1; $i++ ) {
            if ( strlen( $words[$i] ) >= 3 && ! in_array( $words[$i], $stop_words ) ) {
                // Expressions de 2 mots
                if ( isset( $words[$i + 1] ) && strlen( $words[$i + 1] ) >= 3 && ! in_array( $words[$i + 1], $stop_words ) ) {
                    $phrase = $words[$i] . ' ' . $words[$i + 1];
                    if ( strlen( $phrase ) <= 50 ) {
                        $phrases[ $phrase ] = isset( $phrases[ $phrase ] ) ? $phrases[ $phrase ] + 1 : 1;
                    }
                }
            }
        }

        // Filtrer les expressions qui apparaissent au moins 2 fois
        $phrases = array_filter( $phrases, function( $count ) {
            return $count >= 2;
        } );

        arsort( $phrases );
        return $phrases;
    }

    /**
     * Affiche l'interface sécurisée de la meta box avec onglets.
     *
     * @since    1.2.0
     * @param    WP_Post    $post                   Post actuel.
     * @param    array      $metadata               Métadonnées validées.
     * @param    array      $keyword_suggestions    Suggestions de mots-clés.
     */
    private function render_secure_interface( $post, $metadata, $keyword_suggestions ) {
        ?>
        <div class="boss-seo-metabox-secure" data-post-id="<?php echo esc_attr( $post->ID ); ?>">

            <!-- Navigation par onglets -->
            <div class="boss-seo-tabs-nav" role="tablist" aria-label="<?php esc_attr_e( 'Options SEO', 'boss-seo' ); ?>">
                <button type="button" class="boss-seo-tab-button active"
                        role="tab"
                        aria-selected="true"
                        aria-controls="boss-seo-tab-keywords"
                        id="boss-seo-tab-keywords-button"
                        data-tab="keywords">
                    <span class="dashicons dashicons-tag" aria-hidden="true"></span>
                    <?php esc_html_e( 'Mots-clés', 'boss-seo' ); ?>
                </button>

                <button type="button" class="boss-seo-tab-button"
                        role="tab"
                        aria-selected="false"
                        aria-controls="boss-seo-tab-metadata"
                        id="boss-seo-tab-metadata-button"
                        data-tab="metadata">
                    <span class="dashicons dashicons-admin-appearance" aria-hidden="true"></span>
                    <?php esc_html_e( 'Métadonnées', 'boss-seo' ); ?>
                </button>

                <button type="button" class="boss-seo-tab-button"
                        role="tab"
                        aria-selected="false"
                        aria-controls="boss-seo-tab-social"
                        id="boss-seo-tab-social-button"
                        data-tab="social">
                    <span class="dashicons dashicons-share" aria-hidden="true"></span>
                    <?php esc_html_e( 'Réseaux Sociaux', 'boss-seo' ); ?>
                </button>

                <button type="button" class="boss-seo-tab-button"
                        role="tab"
                        aria-selected="false"
                        aria-controls="boss-seo-tab-advanced"
                        id="boss-seo-tab-advanced-button"
                        data-tab="advanced">
                    <span class="dashicons dashicons-admin-tools" aria-hidden="true"></span>
                    <?php esc_html_e( 'Avancé', 'boss-seo' ); ?>
                </button>

                <button type="button" class="boss-seo-tab-button"
                        role="tab"
                        aria-selected="false"
                        aria-controls="boss-seo-tab-analysis"
                        id="boss-seo-tab-analysis-button"
                        data-tab="analysis">
                    <span class="dashicons dashicons-chart-bar" aria-hidden="true"></span>
                    <?php esc_html_e( 'Analyse', 'boss-seo' ); ?>
                    <?php if ( ! empty( $metadata['seo_score'] ) ) : ?>
                        <span class="boss-seo-score-badge boss-seo-score-<?php echo esc_attr( $this->get_score_class( $metadata['seo_score'] ) ); ?>">
                            <?php echo intval( $metadata['seo_score'] ); ?>
                        </span>
                    <?php endif; ?>
                </button>
            </div>

            <!-- Contenu des onglets -->
            <div class="boss-seo-tabs-content">

                <!-- Onglet 1: Mots-clés -->
                <div class="boss-seo-tab-panel active"
                     role="tabpanel"
                     id="boss-seo-tab-keywords"
                     aria-labelledby="boss-seo-tab-keywords-button"
                     data-tab="keywords">

                    <div class="boss-seo-field-group">
                        <label for="boss_seo_keywords_input" class="boss-seo-label">
                            <span class="dashicons dashicons-tag" aria-hidden="true"></span>
                            <?php esc_html_e( 'Ajouter des mots-clés', 'boss-seo' ); ?>
                        </label>
                        <input
                            type="text"
                            id="boss_seo_keywords_input"
                            class="boss-seo-input"
                            placeholder="<?php esc_attr_e( 'Tapez un mot-clé et appuyez sur Entrée', 'boss-seo' ); ?>"
                            maxlength="50"
                            autocomplete="off"
                            aria-describedby="boss_seo_keywords_help"
                        >
                        <div id="boss_seo_keywords_help" class="boss-seo-help">
                            <?php esc_html_e( 'Définissez un mot-clé principal (⭐) et des mots-clés secondaires pour optimiser votre contenu.', 'boss-seo' ); ?>
                        </div>

                        <div class="boss-seo-keywords-tags" role="list" aria-label="<?php esc_attr_e( 'Mots-clés sélectionnés', 'boss-seo' ); ?>">
                            <!-- Les mots-clés seront ajoutés ici par JavaScript -->
                        </div>

                        <?php if ( ! empty( $keyword_suggestions ) ) : ?>
                        <div class="boss-seo-suggestions">
                            <div class="boss-seo-suggestions-title">
                                <span class="dashicons dashicons-lightbulb" aria-hidden="true"></span>
                                <?php esc_html_e( 'Suggestions intelligentes', 'boss-seo' ); ?>
                            </div>
                            <div class="boss-seo-suggestions-list" role="list">
                                <?php foreach ( $keyword_suggestions as $suggestion ) : ?>
                                <button
                                    type="button"
                                    class="boss-seo-suggestion"
                                    data-keyword="<?php echo esc_attr( $suggestion ); ?>"
                                    role="listitem"
                                >
                                    <span class="dashicons dashicons-plus-alt2" aria-hidden="true"></span>
                                    <?php echo esc_html( $suggestion ); ?>
                                </button>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Champs cachés pour stocker les valeurs -->
                    <input type="hidden" name="boss_seo_focus_keyword" id="boss_seo_focus_keyword" value="<?php echo esc_attr( $metadata['focus_keyword'] ); ?>">
                    <input type="hidden" name="boss_seo_secondary_keywords" id="boss_seo_secondary_keywords" value="<?php echo esc_attr( $metadata['secondary_keywords'] ); ?>">
                </div>

                <!-- Onglet 2: Métadonnées SEO -->
                <div class="boss-seo-tab-panel"
                     role="tabpanel"
                     id="boss-seo-tab-metadata"
                     aria-labelledby="boss-seo-tab-metadata-button"
                     data-tab="metadata">

                    <div class="boss-seo-field-group">
                        <label for="boss_seo_title" class="boss-seo-label">
                            <span class="dashicons dashicons-admin-appearance" aria-hidden="true"></span>
                            <?php esc_html_e( 'Titre SEO', 'boss-seo' ); ?>
                        </label>
                        <input
                            type="text"
                            name="boss_seo_title"
                            id="boss_seo_title"
                            value="<?php echo esc_attr( $metadata['seo_title'] ); ?>"
                            class="boss-seo-input"
                            placeholder="<?php esc_attr_e( 'Titre optimisé pour les moteurs de recherche', 'boss-seo' ); ?>"
                            maxlength="120"
                            aria-describedby="boss_seo_title_help boss_seo_title_counter"
                        >
                        <div class="boss-seo-counter" id="boss_seo_title_counter" aria-live="polite">
                            <span class="boss-seo-counter-current"><?php echo mb_strlen( $metadata['seo_title'] ); ?></span>
                            <span class="boss-seo-counter-separator">/</span>
                            <span class="boss-seo-counter-max">60</span>
                            <span class="boss-seo-counter-label"><?php esc_html_e( 'caractères', 'boss-seo' ); ?></span>
                        </div>
                        <div id="boss_seo_title_help" class="boss-seo-help">
                            <?php esc_html_e( 'Laissez vide pour utiliser le titre de l\'article. Idéalement entre 50 et 60 caractères.', 'boss-seo' ); ?>
                        </div>
                    </div>

                <div class="boss-seo-field-group">
                    <label for="boss_seo_meta_description"><?php esc_html_e( 'Meta description', 'boss-seo' ); ?></label>
                    <textarea
                        name="boss_seo_meta_description"
                        id="boss_seo_meta_description"
                        class="boss-seo-textarea"
                        rows="3"
                        placeholder="<?php esc_attr_e( 'Entrez votre méta-description', 'boss-seo' ); ?>"
                        maxlength="320"
                        aria-describedby="boss_seo_meta_description_help boss_seo_meta_description_counter"
                    ><?php echo esc_textarea( $metadata['meta_description'] ); ?></textarea>
                    <div class="boss-seo-counter" id="boss_seo_meta_description_counter" aria-live="polite">
                        <span class="boss-seo-counter-current"><?php echo mb_strlen( $metadata['meta_description'] ); ?></span>
                        <span class="boss-seo-counter-separator">/</span>
                        <span class="boss-seo-counter-max">160</span>
                        <span class="boss-seo-counter-label"><?php esc_html_e( 'caractères', 'boss-seo' ); ?></span>
                    </div>
                    <div id="boss_seo_meta_description_help" class="boss-seo-help">
                        <?php esc_html_e( 'La méta-description apparaît dans les résultats de recherche. Idéalement entre 120 et 160 caractères.', 'boss-seo' ); ?>
                    </div>
                </div>

                <div class="boss-seo-field-group">
                    <label for="boss_seo_canonical_url"><?php esc_html_e( 'URL canonique', 'boss-seo' ); ?></label>
                    <input
                        type="url"
                        name="boss_seo_canonical_url"
                        id="boss_seo_canonical_url"
                        value="<?php echo esc_url( $metadata['canonical_url'] ); ?>"
                        class="boss-seo-input"
                        placeholder="<?php esc_attr_e( 'https://example.com/page-canonique', 'boss-seo' ); ?>"
                        aria-describedby="boss_seo_canonical_help"
                    >
                    <div id="boss_seo_canonical_help" class="boss-seo-help">
                        <?php esc_html_e( 'Laissez vide pour utiliser l\'URL actuelle. Utilisez cette option uniquement si cette page est un duplicata d\'une autre page.', 'boss-seo' ); ?>
                    </div>
                </div>

                <div class="boss-seo-field-grid">
                    <div class="boss-seo-field-group">
                        <label for="boss_seo_robots_index"><?php esc_html_e( 'Indexation', 'boss-seo' ); ?></label>
                        <select name="boss_seo_robots_index" id="boss_seo_robots_index" class="boss-seo-select">
                            <option value="index" <?php selected( $metadata['robots_index'], 'index' ); ?>><?php esc_html_e( 'Index', 'boss-seo' ); ?></option>
                            <option value="noindex" <?php selected( $metadata['robots_index'], 'noindex' ); ?>><?php esc_html_e( 'Noindex', 'boss-seo' ); ?></option>
                        </select>
                    </div>

                    <div class="boss-seo-field-group">
                        <label for="boss_seo_robots_follow"><?php esc_html_e( 'Suivi des liens', 'boss-seo' ); ?></label>
                        <select name="boss_seo_robots_follow" id="boss_seo_robots_follow" class="boss-seo-select">
                            <option value="follow" <?php selected( $metadata['robots_follow'], 'follow' ); ?>><?php esc_html_e( 'Follow', 'boss-seo' ); ?></option>
                            <option value="nofollow" <?php selected( $metadata['robots_follow'], 'nofollow' ); ?>><?php esc_html_e( 'Nofollow', 'boss-seo' ); ?></option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Section Score SEO avec indicateur temps réel -->
            <div class="boss-seo-section" data-section="score">
                <h4>
                    <span class="dashicons dashicons-chart-bar" aria-hidden="true"></span>
                    <?php esc_html_e( 'Score SEO', 'boss-seo' ); ?>
                </h4>

                <div class="boss-seo-score-container">
                    <?php if ( ! empty( $metadata['seo_score'] ) ) : ?>
                        <div class="boss-seo-score-indicator boss-seo-score-<?php echo esc_attr( $this->get_score_class( $metadata['seo_score'] ) ); ?>" role="img" aria-label="<?php echo esc_attr( sprintf( __( 'Score SEO: %d sur 100', 'boss-seo' ), $metadata['seo_score'] ) ); ?>">
                            <span class="boss-seo-score-value"><?php echo intval( $metadata['seo_score'] ); ?></span>
                        </div>
                    <?php else : ?>
                        <div class="boss-seo-score-indicator boss-seo-score-none" role="img" aria-label="<?php esc_attr_e( 'Score SEO non calculé', 'boss-seo' ); ?>">
                            <span class="boss-seo-score-value">?</span>
                        </div>
                    <?php endif; ?>

                    <div class="boss-seo-score-details">
                        <p>
                            <?php esc_html_e( 'Dernière analyse:', 'boss-seo' ); ?>
                            <strong>
                                <?php
                                echo ! empty( $metadata['analysis_date'] )
                                    ? esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $metadata['analysis_date'] ) ) )
                                    : esc_html__( 'Jamais', 'boss-seo' );
                                ?>
                            </strong>
                        </p>
                        <button type="button" class="button" id="boss_seo_analyze_button" aria-describedby="boss_seo_analyze_help">
                            <?php esc_html_e( 'Analyser maintenant', 'boss-seo' ); ?>
                        </button>
                        <div id="boss_seo_analyze_help" class="boss-seo-help">
                            <?php esc_html_e( 'Lance une analyse SEO complète de votre contenu.', 'boss-seo' ); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Actions avec boutons sécurisés -->
            <div class="boss-seo-section" data-section="actions">
                <div class="boss-seo-actions">
                    <button type="button" class="button button-primary" id="boss_seo_optimize_button" aria-describedby="boss_seo_optimize_help">
                        <span class="dashicons dashicons-superhero" aria-hidden="true"></span>
                        <?php esc_html_e( 'Optimiser avec l\'IA', 'boss-seo' ); ?>
                    </button>
                    <div id="boss_seo_optimize_help" class="boss-seo-help">
                        <?php esc_html_e( 'Utilise l\'intelligence artificielle pour optimiser votre contenu pour le SEO.', 'boss-seo' ); ?>
                    </div>
                </div>
            </div>

            <!-- Section Recommandations (affichée dynamiquement) -->
            <?php if ( ! empty( $metadata['recommendations'] ) && is_array( $metadata['recommendations'] ) ) : ?>
                <div class="boss-seo-section" data-section="recommendations">
                    <h4>
                        <span class="dashicons dashicons-lightbulb" aria-hidden="true"></span>
                        <?php esc_html_e( 'Recommandations', 'boss-seo' ); ?>
                    </h4>
                    <div class="boss-seo-recommendations" role="list">
                        <?php foreach ( $metadata['recommendations'] as $recommendation ) : ?>
                            <div class="boss-seo-recommendation boss-seo-recommendation-<?php echo esc_attr( $recommendation['type'] ); ?>" role="listitem">
                                <span class="boss-seo-recommendation-icon dashicons dashicons-<?php echo esc_attr( $this->get_recommendation_icon( $recommendation['type'] ) ); ?>" aria-hidden="true"></span>
                                <span class="boss-seo-recommendation-text"><?php echo esc_html( $recommendation['text'] ); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Retourne la classe CSS en fonction du score SEO.
     *
     * @since    1.2.0
     * @param    int       $score    Le score SEO.
     * @return   string              La classe CSS.
     */
    private function get_score_class( $score ) {
        if ( $score >= 80 ) {
            return 'good';
        } elseif ( $score >= 60 ) {
            return 'ok';
        } elseif ( $score >= 40 ) {
            return 'poor';
        } else {
            return 'bad';
        }
    }

    /**
     * Retourne l'icône en fonction du type de recommandation.
     *
     * @since    1.2.0
     * @param    string    $type    Le type de recommandation.
     * @return   string             L'icône.
     */
    private function get_recommendation_icon( $type ) {
        switch ( $type ) {
            case 'critical':
                return 'warning';
            case 'warning':
                return 'flag';
            case 'info':
                return 'info';
            default:
                return 'info';
        }
    }
}
