<?php
/**
 * Plugin Name: Boss SEO
 * Plugin URI: https://bossseo.com
 * Description: Un plugin SEO avancé avec intelligence artificielle pour WordPress
 * Version: 1.1.0
 * Author: Boss SEO Team
 * Author URI: https://bossseo.com
 * Text Domain: boss-seo
 * Domain Path: /languages
 */

// Si ce fichier est appelé directement, on sort.
if (!defined('WPINC')) {
    die;
}

// Définition des constantes
define('BOSS_SEO_VERSION', '1.1.0');
define('BOSS_SEO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BOSS_SEO_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * La fonction exécutée lors de l'activation du plugin.
 */
function activate_boss_seo() {
    require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-seo-activator.php';
    Boss_SEO_Activator::activate();
}

/**
 * La fonction exécutée lors de la désactivation du plugin.
 */
function deactivate_boss_seo() {
    require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-seo-deactivator.php';
    Boss_SEO_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'activate_boss_seo');
register_deactivation_hook(__FILE__, 'deactivate_boss_seo');

/**
 * Le noyau du plugin.
 */
require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-seo.php';

/**
 * Commence l'exécution du plugin.
 */
function run_boss_seo() {
    $plugin = new Boss_SEO();
    $plugin->run();
}

run_boss_seo();
