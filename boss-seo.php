<?php
/**
 * Plugin Name: Boss SEO
 * Plugin URI: https://bossseo.com
 * Description: Un plugin SEO avancé avec intelligence artificielle pour WordPress
 * Version: 1.1.0
 * Author: Boss SEO Team
 * Author URI: https://bossseo.com
 * Text Domain: boss-seo
 * Domain Path: /languages
 */

// Si ce fichier est appelé directement, on sort.
if (!defined('WPINC')) {
    die;
}

// Définition des constantes
define('BOSS_SEO_VERSION', '1.1.0');
define('BOSS_SEO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BOSS_SEO_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * La fonction exécutée lors de l'activation du plugin.
 */
function activate_boss_seo() {
    require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-seo-activator.php';
    Boss_SEO_Activator::activate();
}

/**
 * La fonction exécutée lors de la désactivation du plugin.
 */
function deactivate_boss_seo() {
    require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-seo-deactivator.php';
    Boss_SEO_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'activate_boss_seo');
register_deactivation_hook(__FILE__, 'deactivate_boss_seo');

/**
 * Initialisation simple pour éviter les erreurs d'activation.
 */
function run_boss_seo() {
    // Vérifier si WordPress est complètement chargé
    if ( ! function_exists( 'add_action' ) ) {
        return;
    }

    // Ajouter un menu admin simple
    add_action( 'admin_menu', 'boss_seo_add_admin_menu' );
}

/**
 * Ajouter le menu admin.
 */
function boss_seo_add_admin_menu() {
    add_menu_page(
        'Boss SEO',
        'Boss SEO',
        'manage_options',
        'boss-seo',
        'boss_seo_admin_page',
        'dashicons-search',
        30
    );
}

/**
 * Page d'administration.
 */
function boss_seo_admin_page() {
    echo '<div class="wrap">';
    echo '<h1>Boss SEO</h1>';
    echo '<div class="notice notice-success"><p><strong>Boss SEO est activé avec succès !</strong></p></div>';
    echo '<p>Version: ' . BOSS_SEO_VERSION . '</p>';
    echo '<p>Le plugin est maintenant actif. Les fonctionnalités complètes seront chargées progressivement.</p>';
    echo '</div>';
}

run_boss_seo();
