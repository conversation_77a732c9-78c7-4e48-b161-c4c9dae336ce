<?php
/**
 * Fired during plugin activation.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Activator {

    /**
     * Activation du plugin.
     *
     * @since    1.1.0
     */
    public static function activate() {
        // Créer les tables pour le module de gestion technique
        self::create_technical_management_tables();
    }

    /**
     * Crée les tables pour le module de gestion technique.
     *
     * @since    1.1.0
     */
    private static function create_technical_management_tables() {
        // Charger les classes nécessaires
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-redirections.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-media-optimization.php';

        // Créer une instance temporaire pour accéder aux méthodes
        $plugin_name = 'boss-seo';
        $version = '1.1.0';

        // Créer les tables pour les redirections
        $redirections = new Boss_Redirections( $plugin_name, $version );
        $redirections->create_tables();

        // Créer les tables pour l'optimisation des médias
        $media_optimization = new Boss_Media_Optimization( $plugin_name, $version );
        $media_optimization->create_tables();
    }

}
