<?php
/**
 * La classe de gestion des contenus du module Boss Optimizer.
 *
 * Cette classe gère la récupération et le filtrage des contenus WordPress.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe de gestion des contenus du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Content {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Instance de la classe de cache.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Cache    $cache    Gère le cache du module.
     */
    protected $cache;

    /**
     * Les types de contenu supportés.
     *
     * @since    1.1.0
     * @access   protected
     * @var      array    $supported_post_types    Les types de contenu supportés.
     */
    protected $supported_post_types;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->settings = $settings;

        // Définir les types de contenu supportés
        $this->supported_post_types = array( 'post', 'page' );

        // Ajouter WooCommerce si actif
        if ( class_exists( 'WooCommerce' ) ) {
            $this->supported_post_types[] = 'product';
        }

        // Filtrer les types de contenu supportés
        $this->supported_post_types = apply_filters( 'boss_optimizer_supported_post_types', $this->supported_post_types );

        // Charger la classe de cache
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-cache.php';
        $this->cache = Boss_Optimizer_Cache::get_instance();
    }

    /**
     * Récupère les contenus selon les filtres spécifiés.
     *
     * @since    1.1.0
     * @param    array    $args    Les arguments de filtrage.
     * @return   array             Les contenus et les métadonnées de pagination.
     */
    public function get_contents( $args = array() ) {
        $defaults = array(
            'content_type' => 'all',
            'seo_score' => 'all',
            'status' => 'all',
            'author' => 'all',
            'date_range' => 'all',
            'search' => '',
            'page' => 1,
            'per_page' => $this->settings->get( 'optimizer', 'items_per_page', 10 ),
            'orderby' => 'date',
            'order' => 'DESC'
        );

        $args = wp_parse_args( $args, $defaults );

        // Vérifier le cache d'abord
        $cached_results = $this->cache->get_contents_query(
            array(
                'content_type' => $args['content_type'],
                'seo_score' => $args['seo_score'],
                'status' => $args['status'],
                'author' => $args['author'],
                'date_range' => $args['date_range']
            ),
            $args['search'],
            $args['page'],
            $args['per_page']
        );

        if ( false !== $cached_results ) {
            return $cached_results;
        }

        // Construire les arguments pour WP_Query
        $query_args = array(
            'post_status' => 'any',
            'posts_per_page' => -1, // Récupérer tous les posts d'abord
            'orderby' => $args['orderby'],
            'order' => $args['order'],
            'post_type' => $this->supported_post_types,
            'update_post_meta_cache' => true,
            'update_post_term_cache' => true,
            'fields' => 'ids' // Récupérer seulement les IDs pour optimiser
        );

        // Filtrer par type de contenu
        if ( $args['content_type'] !== 'all' && in_array( $args['content_type'], $this->supported_post_types ) ) {
            $query_args['post_type'] = $args['content_type'];
        }

        // Filtrer par statut
        if ( $args['status'] !== 'all' ) {
            $query_args['post_status'] = $args['status'];
        }

        // Filtrer par auteur
        if ( $args['author'] !== 'all' && is_numeric( $args['author'] ) ) {
            $query_args['author'] = $args['author'];
        }

        // Filtrer par recherche
        if ( ! empty( $args['search'] ) ) {
            $query_args['s'] = $args['search'];
        }

        // Filtrer par date
        if ( $args['date_range'] !== 'all' ) {
            $date_query = array();

            switch ( $args['date_range'] ) {
                case 'today':
                    $date_query = array(
                        'after' => '1 day ago'
                    );
                    break;
                case 'week':
                    $date_query = array(
                        'after' => '1 week ago'
                    );
                    break;
                case 'month':
                    $date_query = array(
                        'after' => '1 month ago'
                    );
                    break;
                case 'year':
                    $date_query = array(
                        'after' => '1 year ago'
                    );
                    break;
            }

            if ( ! empty( $date_query ) ) {
                $query_args['date_query'] = array( $date_query );
            }
        }

        // Ajouter meta_query pour le filtrage par score SEO
        if ( $args['seo_score'] !== 'all' ) {
            $meta_query = array();

            switch ( $args['seo_score'] ) {
                case 'good':
                    $meta_query[] = array(
                        'key' => '_boss_seo_score',
                        'value' => 80,
                        'compare' => '>=',
                        'type' => 'NUMERIC'
                    );
                    break;
                case 'average':
                    $meta_query[] = array(
                        'key' => '_boss_seo_score',
                        'value' => array( 50, 79 ),
                        'compare' => 'BETWEEN',
                        'type' => 'NUMERIC'
                    );
                    break;
                case 'poor':
                    $meta_query[] = array(
                        'key' => '_boss_seo_score',
                        'value' => 50,
                        'compare' => '<',
                        'type' => 'NUMERIC'
                    );
                    break;
            }

            if ( ! empty( $meta_query ) ) {
                $query_args['meta_query'] = $meta_query;
            }
        }

        // Exécuter la requête pour récupérer les IDs
        $query = new WP_Query( $query_args );
        $post_ids = $query->posts;

        // Calculer la pagination
        $total_items = count( $post_ids );
        $total_pages = ceil( $total_items / $args['per_page'] );
        $offset = ( $args['page'] - 1 ) * $args['per_page'];

        // Récupérer seulement les posts de la page courante
        $current_page_ids = array_slice( $post_ids, $offset, $args['per_page'] );

        // Récupérer les données complètes des posts de la page courante
        $contents = array();
        if ( ! empty( $current_page_ids ) ) {
            // Récupérer tous les scores SEO en une seule requête
            $seo_scores = $this->get_seo_scores_bulk( $current_page_ids );

            // Récupérer toutes les recommandations en une seule requête
            $recommendations_bulk = $this->get_recommendations_bulk( $current_page_ids );

            foreach ( $current_page_ids as $post_id ) {
                $post = get_post( $post_id );
                if ( ! $post ) {
                    continue;
                }

                $seo_score = isset( $seo_scores[ $post_id ] ) ? $seo_scores[ $post_id ] : 0;
                $recommendations = isset( $recommendations_bulk[ $post_id ] ) ? $recommendations_bulk[ $post_id ] : array();

                // Ajouter le contenu au tableau des résultats
                $contents[] = array(
                    'id' => $post->ID,
                    'title' => $post->post_title,
                    'type' => $post->post_type,
                    'status' => $post->post_status,
                    'seo_score' => $seo_score,
                    'date' => $post->post_date,
                    'author' => get_the_author_meta( 'display_name', $post->post_author ),
                    'excerpt' => $post->post_excerpt ? $post->post_excerpt : wp_trim_words( $post->post_content, 30 ),
                    'url' => get_permalink( $post->ID ),
                    'recommendations' => $recommendations
                );
            }
        }

        $results = array(
            'contents' => $contents,
            'total_items' => $total_items,
            'total_pages' => $total_pages,
            'current_page' => $args['page'],
            'per_page' => $args['per_page']
        );

        // Mettre en cache les résultats
        $this->cache->set_contents_query(
            array(
                'content_type' => $args['content_type'],
                'seo_score' => $args['seo_score'],
                'status' => $args['status'],
                'author' => $args['author'],
                'date_range' => $args['date_range']
            ),
            $args['search'],
            $args['page'],
            $args['per_page'],
            $results
        );

        return $results;
    }

    /**
     * Récupère un contenu spécifique avec ses métadonnées.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   array|false           Les données du contenu ou false si non trouvé.
     */
    public function get_content( $post_id ) {
        $post = get_post( $post_id );

        if ( ! $post || ! in_array( $post->post_type, $this->supported_post_types ) ) {
            return false;
        }

        // Récupérer les métadonnées SEO
        $seo_score = $this->get_seo_score( $post_id );
        $recommendations = $this->get_recommendations( $post_id );

        // Récupérer les métadonnées supplémentaires
        $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );

        return array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'content' => $post->post_content,
            'excerpt' => $post->post_excerpt,
            'type' => $post->post_type,
            'status' => $post->post_status,
            'seo_score' => $seo_score,
            'date' => $post->post_date,
            'modified' => $post->post_modified,
            'author' => get_the_author_meta( 'display_name', $post->post_author ),
            'url' => get_permalink( $post->ID ),
            'meta_description' => $meta_description,
            'focus_keyword' => $focus_keyword,
            'recommendations' => $recommendations
        );
    }

    /**
     * Récupère le score SEO d'un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   int                   Le score SEO.
     */
    public function get_seo_score( $post_id ) {
        // Vérifier le cache d'abord
        $cached_score = $this->cache->get_seo_score( $post_id );
        if ( false !== $cached_score ) {
            return $cached_score;
        }

        $score = get_post_meta( $post_id, '_boss_seo_score', true );

        if ( empty( $score ) ) {
            $score = 0; // Score par défaut si non défini
        } else {
            $score = intval( $score );
        }

        // Mettre en cache le score
        $this->cache->set_seo_score( $post_id, $score );

        return $score;
    }

    /**
     * Récupère les recommandations pour un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   array                 Les recommandations.
     */
    public function get_recommendations( $post_id ) {
        // Vérifier le cache d'abord
        $cached_recommendations = $this->cache->get_recommendations( $post_id );
        if ( false !== $cached_recommendations ) {
            return $cached_recommendations;
        }

        $recommendations = get_post_meta( $post_id, '_boss_seo_recommendations', true );

        if ( empty( $recommendations ) || ! is_array( $recommendations ) ) {
            $recommendations = array();
        }

        // Mettre en cache les recommandations
        $this->cache->set_recommendations( $post_id, $recommendations );

        return $recommendations;
    }

    /**
     * Récupère les scores SEO pour plusieurs contenus en une seule requête.
     *
     * @since    1.1.0
     * @param    array     $post_ids    Les IDs des contenus.
     * @return   array                  Les scores SEO indexés par ID.
     */
    public function get_seo_scores_bulk( $post_ids ) {
        if ( empty( $post_ids ) ) {
            return array();
        }

        global $wpdb;

        $post_ids_string = implode( ',', array_map( 'intval', $post_ids ) );

        $results = $wpdb->get_results( $wpdb->prepare(
            "SELECT post_id, meta_value
             FROM {$wpdb->postmeta}
             WHERE meta_key = %s
             AND post_id IN ({$post_ids_string})",
            '_boss_seo_score'
        ) );

        $scores = array();
        foreach ( $results as $result ) {
            $scores[ $result->post_id ] = intval( $result->meta_value );
        }

        // Ajouter les scores par défaut pour les posts sans score
        foreach ( $post_ids as $post_id ) {
            if ( ! isset( $scores[ $post_id ] ) ) {
                $scores[ $post_id ] = 0;
            }
        }

        return $scores;
    }

    /**
     * Récupère les recommandations pour plusieurs contenus en une seule requête.
     *
     * @since    1.1.0
     * @param    array     $post_ids    Les IDs des contenus.
     * @return   array                  Les recommandations indexées par ID.
     */
    public function get_recommendations_bulk( $post_ids ) {
        if ( empty( $post_ids ) ) {
            return array();
        }

        global $wpdb;

        $post_ids_string = implode( ',', array_map( 'intval', $post_ids ) );

        $results = $wpdb->get_results( $wpdb->prepare(
            "SELECT post_id, meta_value
             FROM {$wpdb->postmeta}
             WHERE meta_key = %s
             AND post_id IN ({$post_ids_string})",
            '_boss_seo_recommendations'
        ) );

        $recommendations = array();
        foreach ( $results as $result ) {
            $unserialized = maybe_unserialize( $result->meta_value );
            $recommendations[ $result->post_id ] = is_array( $unserialized ) ? $unserialized : array();
        }

        // Ajouter des tableaux vides pour les posts sans recommandations
        foreach ( $post_ids as $post_id ) {
            if ( ! isset( $recommendations[ $post_id ] ) ) {
                $recommendations[ $post_id ] = array();
            }
        }

        return $recommendations;
    }

    /**
     * Met à jour le score SEO d'un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @param    int       $score      Le nouveau score SEO.
     * @return   bool                  True si la mise à jour a réussi.
     */
    public function update_seo_score( $post_id, $score ) {
        return update_post_meta( $post_id, '_boss_seo_score', intval( $score ) );
    }

    /**
     * Met à jour les recommandations d'un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id           L'ID du contenu.
     * @param    array     $recommendations   Les nouvelles recommandations.
     * @return   bool                         True si la mise à jour a réussi.
     */
    public function update_recommendations( $post_id, $recommendations ) {
        return update_post_meta( $post_id, '_boss_seo_recommendations', $recommendations );
    }

    /**
     * Calcule automatiquement le score SEO d'un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   int                   Le score SEO calculé.
     */
    public function calculate_seo_score( $post_id ) {
        $post = get_post( $post_id );
        if ( ! $post ) {
            return 0;
        }

        $score = 0;
        $max_score = 100;

        // Vérifier le titre SEO (20 points)
        $seo_title = get_post_meta( $post_id, '_boss_seo_title', true );
        if ( ! empty( $seo_title ) && strlen( $seo_title ) <= 60 ) {
            $score += 20;
        } elseif ( ! empty( $post->post_title ) && strlen( $post->post_title ) <= 60 ) {
            $score += 15;
        }

        // Vérifier la meta description (20 points)
        $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
        if ( ! empty( $meta_description ) && strlen( $meta_description ) <= 160 ) {
            $score += 20;
        } elseif ( ! empty( $post->post_excerpt ) && strlen( $post->post_excerpt ) <= 160 ) {
            $score += 15;
        }

        // Vérifier les mots-clés (15 points)
        $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
        if ( ! empty( $focus_keyword ) ) {
            $score += 15;
        }

        // Vérifier la longueur du contenu (15 points)
        $content_length = str_word_count( strip_tags( $post->post_content ) );
        if ( $content_length >= 300 ) {
            $score += 15;
        } elseif ( $content_length >= 150 ) {
            $score += 10;
        }

        // Vérifier l'image à la une (10 points)
        if ( has_post_thumbnail( $post_id ) ) {
            $score += 10;
        }

        // Vérifier les balises (10 points)
        $tags = get_the_tags( $post_id );
        if ( ! empty( $tags ) && count( $tags ) >= 3 ) {
            $score += 10;
        } elseif ( ! empty( $tags ) ) {
            $score += 5;
        }

        // Vérifier les catégories (10 points)
        $categories = get_the_category( $post_id );
        if ( ! empty( $categories ) ) {
            $score += 10;
        }

        // Mettre à jour le score dans la base de données
        $this->update_seo_score( $post_id, $score );

        return $score;
    }
}
