<?php
/**
 * La classe de gestion des contenus du module Boss Optimizer.
 *
 * Cette classe gère la récupération et le filtrage des contenus WordPress.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe de gestion des contenus du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Content {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * Instance de la classe de paramètres.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Settings    $settings    Gère les paramètres du module.
     */
    protected $settings;

    /**
     * Les types de contenu supportés.
     *
     * @since    1.1.0
     * @access   protected
     * @var      array    $supported_post_types    Les types de contenu supportés.
     */
    protected $supported_post_types;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string                   $plugin_name    Le nom du plugin.
     * @param    Boss_Optimizer_Settings  $settings       Instance de la classe de paramètres.
     */
    public function __construct( $plugin_name, $settings ) {
        $this->plugin_name = $plugin_name;
        $this->settings = $settings;
        
        // Définir les types de contenu supportés
        $this->supported_post_types = array( 'post', 'page' );
        
        // Ajouter WooCommerce si actif
        if ( class_exists( 'WooCommerce' ) ) {
            $this->supported_post_types[] = 'product';
        }
        
        // Filtrer les types de contenu supportés
        $this->supported_post_types = apply_filters( 'boss_optimizer_supported_post_types', $this->supported_post_types );
    }

    /**
     * Récupère les contenus selon les filtres spécifiés.
     *
     * @since    1.1.0
     * @param    array    $args    Les arguments de filtrage.
     * @return   array             Les contenus et les métadonnées de pagination.
     */
    public function get_contents( $args = array() ) {
        $defaults = array(
            'content_type' => 'all',
            'seo_score' => 'all',
            'status' => 'all',
            'author' => 'all',
            'date_range' => 'all',
            'search' => '',
            'page' => 1,
            'per_page' => $this->settings->get( 'optimizer', 'items_per_page', 10 ),
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args( $args, $defaults );
        
        // Construire les arguments pour WP_Query
        $query_args = array(
            'post_status' => 'any',
            'posts_per_page' => $args['per_page'],
            'paged' => $args['page'],
            'orderby' => $args['orderby'],
            'order' => $args['order'],
            'post_type' => $this->supported_post_types,
            'update_post_meta_cache' => true,
            'update_post_term_cache' => true
        );
        
        // Filtrer par type de contenu
        if ( $args['content_type'] !== 'all' && in_array( $args['content_type'], $this->supported_post_types ) ) {
            $query_args['post_type'] = $args['content_type'];
        }
        
        // Filtrer par statut
        if ( $args['status'] !== 'all' ) {
            $query_args['post_status'] = $args['status'];
        }
        
        // Filtrer par auteur
        if ( $args['author'] !== 'all' && is_numeric( $args['author'] ) ) {
            $query_args['author'] = $args['author'];
        }
        
        // Filtrer par recherche
        if ( ! empty( $args['search'] ) ) {
            $query_args['s'] = $args['search'];
        }
        
        // Filtrer par date
        if ( $args['date_range'] !== 'all' ) {
            $date_query = array();
            
            switch ( $args['date_range'] ) {
                case 'today':
                    $date_query = array(
                        'after' => '1 day ago'
                    );
                    break;
                case 'week':
                    $date_query = array(
                        'after' => '1 week ago'
                    );
                    break;
                case 'month':
                    $date_query = array(
                        'after' => '1 month ago'
                    );
                    break;
                case 'year':
                    $date_query = array(
                        'after' => '1 year ago'
                    );
                    break;
            }
            
            if ( ! empty( $date_query ) ) {
                $query_args['date_query'] = array( $date_query );
            }
        }
        
        // Exécuter la requête
        $query = new WP_Query( $query_args );
        
        // Préparer les résultats
        $contents = array();
        
        foreach ( $query->posts as $post ) {
            // Récupérer le score SEO
            $seo_score = $this->get_seo_score( $post->ID );
            
            // Filtrer par score SEO si nécessaire
            if ( $args['seo_score'] !== 'all' ) {
                switch ( $args['seo_score'] ) {
                    case 'good':
                        if ( $seo_score < 80 ) {
                            continue 2; // Passer au post suivant
                        }
                        break;
                    case 'average':
                        if ( $seo_score < 50 || $seo_score >= 80 ) {
                            continue 2; // Passer au post suivant
                        }
                        break;
                    case 'poor':
                        if ( $seo_score >= 50 ) {
                            continue 2; // Passer au post suivant
                        }
                        break;
                }
            }
            
            // Récupérer les recommandations
            $recommendations = $this->get_recommendations( $post->ID );
            
            // Ajouter le contenu au tableau des résultats
            $contents[] = array(
                'id' => $post->ID,
                'title' => $post->post_title,
                'type' => $post->post_type,
                'status' => $post->post_status,
                'seo_score' => $seo_score,
                'date' => $post->post_date,
                'author' => get_the_author_meta( 'display_name', $post->post_author ),
                'excerpt' => $post->post_excerpt ? $post->post_excerpt : wp_trim_words( $post->post_content, 30 ),
                'url' => get_permalink( $post->ID ),
                'recommendations' => $recommendations
            );
        }
        
        // Préparer les métadonnées de pagination
        $total_items = $query->found_posts;
        $total_pages = ceil( $total_items / $args['per_page'] );
        
        return array(
            'contents' => $contents,
            'total_items' => $total_items,
            'total_pages' => $total_pages,
            'current_page' => $args['page'],
            'per_page' => $args['per_page']
        );
    }

    /**
     * Récupère un contenu spécifique avec ses métadonnées.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   array|false           Les données du contenu ou false si non trouvé.
     */
    public function get_content( $post_id ) {
        $post = get_post( $post_id );
        
        if ( ! $post || ! in_array( $post->post_type, $this->supported_post_types ) ) {
            return false;
        }
        
        // Récupérer les métadonnées SEO
        $seo_score = $this->get_seo_score( $post_id );
        $recommendations = $this->get_recommendations( $post_id );
        
        // Récupérer les métadonnées supplémentaires
        $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
        
        return array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'content' => $post->post_content,
            'excerpt' => $post->post_excerpt,
            'type' => $post->post_type,
            'status' => $post->post_status,
            'seo_score' => $seo_score,
            'date' => $post->post_date,
            'modified' => $post->post_modified,
            'author' => get_the_author_meta( 'display_name', $post->post_author ),
            'url' => get_permalink( $post->ID ),
            'meta_description' => $meta_description,
            'focus_keyword' => $focus_keyword,
            'recommendations' => $recommendations
        );
    }

    /**
     * Récupère le score SEO d'un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   int                   Le score SEO.
     */
    public function get_seo_score( $post_id ) {
        $score = get_post_meta( $post_id, '_boss_seo_score', true );
        
        if ( empty( $score ) ) {
            return 0; // Score par défaut si non défini
        }
        
        return intval( $score );
    }

    /**
     * Récupère les recommandations pour un contenu.
     *
     * @since    1.1.0
     * @param    int       $post_id    L'ID du contenu.
     * @return   array                 Les recommandations.
     */
    public function get_recommendations( $post_id ) {
        $recommendations = get_post_meta( $post_id, '_boss_seo_recommendations', true );
        
        if ( empty( $recommendations ) || ! is_array( $recommendations ) ) {
            return array();
        }
        
        return $recommendations;
    }
}
