<?php
/**
 * Classe pour l'analyse technique
 *
 * @package Boss_Optimizer
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Classe pour gérer l'analyse technique du site
 */
class Boss_Optimizer_Technical_Analysis {
    /**
     * Instance unique de la classe
     *
     * @var Boss_Optimizer_Technical_Analysis
     */
    private static $instance = null;

    /**
     * Obtenir l'instance unique de la classe
     *
     * @return Boss_Optimizer_Technical_Analysis
     */
    public static function get_instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructeur
     */
    private function __construct() {
        // Enregistrer les endpoints REST API
        add_action( 'rest_api_init', array( $this, 'register_rest_routes' ) );
    }

    /**
     * Enregistre les routes REST API
     */
    public function register_rest_routes() {
        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/start',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'start_analysis' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/latest',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_latest_analysis' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/analyze-url',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'analyze_specific_url' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'url'      => array(
                        'required'          => false,
                        'sanitize_callback' => 'esc_url_raw',
                    ),
                    'strategy' => array(
                        'required'          => false,
                        'default'           => 'mobile',
                        'sanitize_callback' => 'sanitize_text_field',
                        'enum'              => array( 'mobile', 'desktop' ),
                    ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/url-history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_url_analysis_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/url-analysis/(?P<id>[a-zA-Z0-9]+)',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_url_analysis_results' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'id' => array(
                        'required'          => true,
                        'validate_callback' => function( $param ) {
                            return is_string( $param ) && ! empty( $param );
                        },
                    ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_analysis_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/issues',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_issues' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/issues/resolve',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'mark_issue_as_resolved' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/performance',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_performance_data' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/technical-analysis/compare',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'compare_analyses' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur
     *
     * @return bool
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Lance une analyse technique complète
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function start_analysis( $request ) {
        // Créer un nouvel ID d'analyse
        $analysis_id = 'a' . time();

        // Récupérer les résultats de l'analyse
        $results = $this->perform_analysis();

        // Sauvegarder les résultats
        $this->save_analysis_results( $analysis_id, $results );

        return rest_ensure_response( array(
            'success' => true,
            'analysis_id' => $analysis_id,
        ) );
    }

    /**
     * Récupère les résultats de la dernière analyse
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_latest_analysis( $request ) {
        // Récupérer l'historique des analyses
        $history = $this->get_analysis_history_data();

        // Si aucune analyse n'a été effectuée, retourner des données vides
        if ( empty( $history ) ) {
            return rest_ensure_response( $this->get_empty_analysis_data() );
        }

        // Récupérer la dernière analyse
        $latest_analysis = reset( $history );
        $analysis_id = $latest_analysis['id'];

        // Récupérer les résultats complets de l'analyse
        $results = $this->get_analysis_results( $analysis_id );

        return rest_ensure_response( $results );
    }

    /**
     * Récupère l'historique des analyses
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_analysis_history( $request ) {
        // Récupérer les paramètres de pagination
        $page = isset( $request['page'] ) ? intval( $request['page'] ) : 1;
        $per_page = isset( $request['per_page'] ) ? intval( $request['per_page'] ) : 10;

        // Récupérer l'historique des analyses
        $history = $this->get_analysis_history_data();

        // Paginer les résultats
        $offset = ( $page - 1 ) * $per_page;
        $history = array_slice( $history, $offset, $per_page );

        return rest_ensure_response( $history );
    }

    /**
     * Récupère les problèmes détectés lors de la dernière analyse
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_issues( $request ) {
        // Récupérer les paramètres de filtrage
        $category = isset( $request['category'] ) ? sanitize_text_field( $request['category'] ) : null;
        $severity = isset( $request['severity'] ) ? sanitize_text_field( $request['severity'] ) : null;

        // Récupérer la dernière analyse
        $latest_analysis = $this->get_latest_analysis( $request );
        $data = $latest_analysis->get_data();

        // Si aucune analyse n'a été effectuée, retourner un tableau vide
        if ( empty( $data ) || ! isset( $data['issues'] ) ) {
            return rest_ensure_response( array() );
        }

        $issues = $data['issues'];

        // Filtrer par catégorie si nécessaire
        if ( $category && isset( $issues[ $category ] ) ) {
            $issues = array( $category => $issues[ $category ] );
        }

        // Filtrer par gravité si nécessaire
        if ( $severity ) {
            foreach ( $issues as $cat => $cat_issues ) {
                $issues[ $cat ] = array_filter( $cat_issues, function( $issue ) use ( $severity ) {
                    return $issue['severity'] === $severity;
                } );
            }
        }

        return rest_ensure_response( $issues );
    }

    /**
     * Marque un problème comme résolu
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function mark_issue_as_resolved( $request ) {
        // Récupérer l'ID du problème
        $issue_id = isset( $request['issue_id'] ) ? sanitize_text_field( $request['issue_id'] ) : '';

        if ( empty( $issue_id ) ) {
            return new WP_Error( 'missing_issue_id', __( 'ID du problème manquant', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer la dernière analyse
        $latest_analysis = $this->get_latest_analysis( $request );
        $data = $latest_analysis->get_data();

        // Si aucune analyse n'a été effectuée, retourner une erreur
        if ( empty( $data ) || ! isset( $data['issues'] ) ) {
            return new WP_Error( 'no_analysis', __( 'Aucune analyse disponible', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Rechercher le problème dans toutes les catégories
        $found = false;
        foreach ( $data['issues'] as $category => $issues ) {
            foreach ( $issues as $index => $issue ) {
                if ( $issue['id'] === $issue_id ) {
                    // Marquer le problème comme résolu
                    $data['issues'][ $category ][ $index ]['status'] = 'resolved';
                    $found = true;
                    break 2;
                }
            }
        }

        if ( ! $found ) {
            return new WP_Error( 'issue_not_found', __( 'Problème non trouvé', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Sauvegarder les résultats mis à jour
        $this->save_analysis_results( $data['id'], $data );

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Problème marqué comme résolu', 'boss-seo' ),
        ) );
    }

    /**
     * Récupère les données de performance
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_performance_data( $request ) {
        // Récupérer les données de performance depuis la dernière analyse
        $latest_analysis = $this->get_latest_analysis( $request );
        $data = $latest_analysis->get_data();

        // Si aucune analyse n'a été effectuée, retourner des données vides
        if ( empty( $data ) || ! isset( $data['performance'] ) ) {
            return rest_ensure_response( array(
                'coreWebVitals' => array(
                    'lcp' => array(
                        'name' => 'LCP',
                        'value' => 0,
                        'unit' => 's',
                        'status' => 'unknown',
                        'description' => __( 'Largest Contentful Paint', 'boss-seo' ),
                    ),
                    'fid' => array(
                        'name' => 'FID',
                        'value' => 0,
                        'unit' => 'ms',
                        'status' => 'unknown',
                        'description' => __( 'First Input Delay', 'boss-seo' ),
                    ),
                    'cls' => array(
                        'name' => 'CLS',
                        'value' => 0,
                        'unit' => '',
                        'status' => 'unknown',
                        'description' => __( 'Cumulative Layout Shift', 'boss-seo' ),
                    ),
                    'ttfb' => array(
                        'name' => 'TTFB',
                        'value' => 0,
                        'unit' => 'ms',
                        'status' => 'unknown',
                        'description' => __( 'Time to First Byte', 'boss-seo' ),
                    ),
                    'fcp' => array(
                        'name' => 'FCP',
                        'value' => 0,
                        'unit' => 's',
                        'status' => 'unknown',
                        'description' => __( 'First Contentful Paint', 'boss-seo' ),
                    ),
                ),
                'history' => array(),
            ) );
        }

        return rest_ensure_response( $data['performance'] );
    }

    /**
     * Compare deux analyses
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function compare_analyses( $request ) {
        // Récupérer les IDs des analyses à comparer
        $analysis_id_1 = isset( $request['analysis_id_1'] ) ? sanitize_text_field( $request['analysis_id_1'] ) : '';
        $analysis_id_2 = isset( $request['analysis_id_2'] ) ? sanitize_text_field( $request['analysis_id_2'] ) : '';

        if ( empty( $analysis_id_1 ) || empty( $analysis_id_2 ) ) {
            return new WP_Error( 'missing_analysis_ids', __( 'IDs des analyses manquants', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les résultats des analyses
        $analysis_1 = $this->get_analysis_results( $analysis_id_1 );
        $analysis_2 = $this->get_analysis_results( $analysis_id_2 );

        if ( empty( $analysis_1 ) || empty( $analysis_2 ) ) {
            return new WP_Error( 'analyses_not_found', __( 'Une ou plusieurs analyses non trouvées', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Comparer les scores
        $score_diff = $analysis_2['score'] - $analysis_1['score'];

        // Comparer les problèmes
        $issues_diff = array();
        foreach ( array( 'critical', 'errors', 'warnings', 'improvements', 'successes' ) as $category ) {
            $count_1 = count( $analysis_1['issues'][ $category ] );
            $count_2 = count( $analysis_2['issues'][ $category ] );
            $issues_diff[ $category ] = $count_2 - $count_1;
        }

        // Comparer les performances
        $performance_diff = array();
        foreach ( $analysis_1['performance']['coreWebVitals'] as $metric => $data ) {
            $value_1 = $data['value'];
            $value_2 = $analysis_2['performance']['coreWebVitals'][ $metric ]['value'];
            $performance_diff[ $metric ] = $value_2 - $value_1;
        }

        return rest_ensure_response( array(
            'analysis_1' => $analysis_1,
            'analysis_2' => $analysis_2,
            'diff' => array(
                'score' => $score_diff,
                'issues' => $issues_diff,
                'performance' => $performance_diff,
            ),
        ) );
    }

    /**
     * Effectue une analyse technique complète
     *
     * @return array Résultats de l'analyse
     */
    private function perform_analysis() {
        // Initialiser les résultats
        $results = array(
            'score' => 0,
            'issues' => array(
                'critical' => array(),
                'errors' => array(),
                'warnings' => array(),
                'improvements' => array(),
                'successes' => array(),
            ),
            'performance' => array(
                'coreWebVitals' => array(),
                'history' => array(),
            ),
        );

        // Analyser les différents aspects du site
        $this->analyze_performance( $results );
        $this->analyze_seo( $results );
        $this->analyze_accessibility( $results );
        $this->analyze_best_practices( $results );
        $this->analyze_security( $results );

        // Calculer le score global
        $results['score'] = $this->calculate_overall_score( $results );

        return $results;
    }

    /**
     * Analyse les performances du site
     *
     * @param array $results Résultats de l'analyse
     */
    private function analyze_performance( &$results ) {
        // Récupérer les données de PageSpeed Insights si disponibles
        $pagespeed_data = $this->get_pagespeed_data();

        if ( $pagespeed_data ) {
            // Utiliser les données de PageSpeed Insights
            $results['performance']['coreWebVitals'] = $pagespeed_data;
        } else {
            // Utiliser des données fictives pour la démo
            $results['performance']['coreWebVitals'] = array(
                'lcp' => array(
                    'name' => 'LCP',
                    'value' => 3.2,
                    'unit' => 's',
                    'status' => 'needs-improvement',
                    'description' => __( 'Largest Contentful Paint', 'boss-seo' ),
                ),
                'fid' => array(
                    'name' => 'FID',
                    'value' => 75,
                    'unit' => 'ms',
                    'status' => 'good',
                    'description' => __( 'First Input Delay', 'boss-seo' ),
                ),
                'cls' => array(
                    'name' => 'CLS',
                    'value' => 0.12,
                    'unit' => '',
                    'status' => 'needs-improvement',
                    'description' => __( 'Cumulative Layout Shift', 'boss-seo' ),
                ),
                'ttfb' => array(
                    'name' => 'TTFB',
                    'value' => 520,
                    'unit' => 'ms',
                    'status' => 'needs-improvement',
                    'description' => __( 'Time to First Byte', 'boss-seo' ),
                ),
                'fcp' => array(
                    'name' => 'FCP',
                    'value' => 1.8,
                    'unit' => 's',
                    'status' => 'good',
                    'description' => __( 'First Contentful Paint', 'boss-seo' ),
                ),
            );
        }

        // Ajouter l'historique des performances
        $results['performance']['history'] = $this->get_performance_history();

        // Ajouter des problèmes liés aux performances
        if ( $results['performance']['coreWebVitals']['lcp']['status'] === 'poor' ) {
            $results['issues']['critical'][] = array(
                'id' => 'perf-lcp-poor',
                'title' => __( 'LCP très lent', 'boss-seo' ),
                'description' => sprintf(
                    __( 'Votre Largest Contentful Paint (LCP) est de %s, ce qui est bien supérieur à la valeur recommandée de 2.5s.', 'boss-seo' ),
                    $results['performance']['coreWebVitals']['lcp']['value'] . $results['performance']['coreWebVitals']['lcp']['unit']
                ),
                'severity' => 'critical',
                'category' => 'performance',
                'status' => 'pending',
            );
        } elseif ( $results['performance']['coreWebVitals']['lcp']['status'] === 'needs-improvement' ) {
            $results['issues']['errors'][] = array(
                'id' => 'perf-lcp-slow',
                'title' => __( 'LCP lent', 'boss-seo' ),
                'description' => sprintf(
                    __( 'Votre Largest Contentful Paint (LCP) est de %s, ce qui est supérieur à la valeur recommandée de 2.5s.', 'boss-seo' ),
                    $results['performance']['coreWebVitals']['lcp']['value'] . $results['performance']['coreWebVitals']['lcp']['unit']
                ),
                'severity' => 'error',
                'category' => 'performance',
                'status' => 'pending',
            );
        }

        if ( $results['performance']['coreWebVitals']['cls']['status'] === 'poor' ) {
            $results['issues']['critical'][] = array(
                'id' => 'perf-cls-poor',
                'title' => __( 'CLS très élevé', 'boss-seo' ),
                'description' => sprintf(
                    __( 'Votre Cumulative Layout Shift (CLS) est de %s, ce qui est bien supérieur à la valeur recommandée de 0.1.', 'boss-seo' ),
                    $results['performance']['coreWebVitals']['cls']['value']
                ),
                'severity' => 'critical',
                'category' => 'performance',
                'status' => 'pending',
            );
        } elseif ( $results['performance']['coreWebVitals']['cls']['status'] === 'needs-improvement' ) {
            $results['issues']['warnings'][] = array(
                'id' => 'perf-cls-high',
                'title' => __( 'CLS élevé', 'boss-seo' ),
                'description' => sprintf(
                    __( 'Votre Cumulative Layout Shift (CLS) est de %s, ce qui est légèrement supérieur à la valeur recommandée de 0.1.', 'boss-seo' ),
                    $results['performance']['coreWebVitals']['cls']['value']
                ),
                'severity' => 'warning',
                'category' => 'performance',
                'status' => 'pending',
            );
        }

        if ( $results['performance']['coreWebVitals']['ttfb']['status'] === 'poor' ) {
            $results['issues']['errors'][] = array(
                'id' => 'perf-ttfb-poor',
                'title' => __( 'TTFB très lent', 'boss-seo' ),
                'description' => sprintf(
                    __( 'Votre Time to First Byte (TTFB) est de %s, ce qui est bien supérieur à la valeur recommandée de 200ms.', 'boss-seo' ),
                    $results['performance']['coreWebVitals']['ttfb']['value'] . $results['performance']['coreWebVitals']['ttfb']['unit']
                ),
                'severity' => 'error',
                'category' => 'performance',
                'status' => 'pending',
            );
        } elseif ( $results['performance']['coreWebVitals']['ttfb']['status'] === 'needs-improvement' ) {
            $results['issues']['warnings'][] = array(
                'id' => 'perf-ttfb-slow',
                'title' => __( 'TTFB lent', 'boss-seo' ),
                'description' => sprintf(
                    __( 'Votre Time to First Byte (TTFB) est de %s, ce qui est supérieur à la valeur recommandée de 200ms.', 'boss-seo' ),
                    $results['performance']['coreWebVitals']['ttfb']['value'] . $results['performance']['coreWebVitals']['ttfb']['unit']
                ),
                'severity' => 'warning',
                'category' => 'performance',
                'status' => 'pending',
            );
        }
    }

    /**
     * Analyse les aspects SEO du site
     *
     * @param array $results Résultats de l'analyse
     */
    private function analyze_seo( &$results ) {
        // Vérifier les titres et méta-descriptions
        $this->check_titles_and_descriptions( $results );

        // Vérifier les balises canoniques
        $this->check_canonical_tags( $results );

        // Vérifier la structure des URLs
        $this->check_url_structure( $results );

        // Vérifier les liens internes et externes
        $this->check_links( $results );

        // Vérifier le sitemap
        $this->check_sitemap( $results );

        // Vérifier le fichier robots.txt
        $this->check_robots_txt( $results );
    }

    /**
     * Analyse l'accessibilité du site
     *
     * @param array $results Résultats de l'analyse
     */
    private function analyze_accessibility( &$results ) {
        // Vérifier les attributs alt des images
        $this->check_image_alt_attributes( $results );

        // Vérifier les contrastes de couleurs
        $this->check_color_contrasts( $results );

        // Vérifier la structure des titres
        $this->check_heading_structure( $results );

        // Vérifier les attributs ARIA
        $this->check_aria_attributes( $results );

        // Vérifier la navigation au clavier
        $this->check_keyboard_navigation( $results );
    }

    /**
     * Analyse les bonnes pratiques du site
     *
     * @param array $results Résultats de l'analyse
     */
    private function analyze_best_practices( &$results ) {
        // Vérifier les erreurs JavaScript
        $this->check_javascript_errors( $results );

        // Vérifier l'utilisation de HTTPS
        $this->check_https_usage( $results );

        // Vérifier les en-têtes de sécurité
        $this->check_security_headers( $results );

        // Vérifier les bibliothèques obsolètes
        $this->check_deprecated_libraries( $results );

        // Vérifier les problèmes de console
        $this->check_console_issues( $results );
    }

    /**
     * Analyse la sécurité du site
     *
     * @param array $results Résultats de l'analyse
     */
    private function analyze_security( &$results ) {
        // Vérifier la version de WordPress
        $this->check_wordpress_version( $results );

        // Vérifier les versions des plugins
        $this->check_plugin_versions( $results );

        // Vérifier les vulnérabilités connues
        $this->check_known_vulnerabilities( $results );

        // Vérifier les permissions des fichiers
        $this->check_file_permissions( $results );

        // Vérifier la protection contre les injections SQL
        $this->check_sql_injection_protection( $results );
    }

    /**
     * Calcule le score global de l'analyse
     *
     * @param array $results Résultats de l'analyse
     * @return int Score global (0-100)
     */
    private function calculate_overall_score( $results ) {
        // Compter le nombre de problèmes par catégorie
        $critical_count = count( $results['issues']['critical'] );
        $errors_count = count( $results['issues']['errors'] );
        $warnings_count = count( $results['issues']['warnings'] );
        $improvements_count = count( $results['issues']['improvements'] );
        $successes_count = count( $results['issues']['successes'] );

        // Calculer le score en fonction du nombre de problèmes
        $total_issues = $critical_count + $errors_count + $warnings_count + $improvements_count;

        if ( $total_issues === 0 ) {
            return 100;
        }

        // Pondération des problèmes
        $weighted_issues = $critical_count * 5 + $errors_count * 3 + $warnings_count * 1 + $improvements_count * 0.5;

        // Calculer le score (inversement proportionnel au nombre de problèmes pondérés)
        $max_weighted_issues = 100; // Valeur arbitraire pour le maximum de problèmes pondérés
        $score = 100 - min( 100, ( $weighted_issues / $max_weighted_issues ) * 100 );

        return round( $score );
    }

    /**
     * Récupère les données de PageSpeed Insights
     *
     * @param string $url URL à analyser (par défaut, la page d'accueil)
     * @param string $strategy Stratégie d'analyse (mobile ou desktop)
     * @return array|false Données de PageSpeed Insights ou false en cas d'erreur
     */
    private function get_pagespeed_data( $url = '', $strategy = 'mobile' ) {
        // Récupérer la clé API PageSpeed Insights
        $api_key = get_option( 'boss_optimizer_pagespeed_api_key' );

        if ( empty( $api_key ) ) {
            return false;
        }

        // URL du site (utiliser la page d'accueil si aucune URL n'est spécifiée)
        $site_url = ! empty( $url ) ? $url : home_url();

        // URL de l'API PageSpeed Insights
        $api_url = add_query_arg(
            array(
                'url' => urlencode( $site_url ),
                'key' => $api_key,
                'strategy' => $strategy,
                // Récupérer toutes les catégories pour une analyse complète
                'category' => 'performance,accessibility,best-practices,seo,pwa',
                'locale' => 'fr_FR',
            ),
            'https://www.googleapis.com/pagespeedonline/v5/runPagespeed'
        );

        // Effectuer la requête
        $response = wp_remote_get( $api_url, array( 'timeout' => 30 ) ); // Augmenter le timeout pour les analyses complètes

        if ( is_wp_error( $response ) ) {
            return false;
        }

        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        if ( empty( $data ) || ! isset( $data['lighthouseResult'] ) ) {
            return false;
        }

        // Extraire les métriques
        $metrics = $data['lighthouseResult']['audits'];

        // Résultats complets de l'analyse
        $pagespeed_results = array(
            'url' => $site_url,
            'strategy' => $strategy,
            'date' => current_time( 'mysql' ),
            'scores' => array(),
            'metrics' => array(),
            'audits' => array(),
            'opportunities' => array(),
            'diagnostics' => array(),
            'passed_audits' => array(),
        );

        // Extraire les scores par catégorie
        $categories = isset( $data['lighthouseResult']['categories'] ) ? $data['lighthouseResult']['categories'] : array();
        foreach ( $categories as $category_id => $category ) {
            $pagespeed_results['scores'][ $category_id ] = array(
                'title' => $category['title'],
                'score' => round( $category['score'] * 100 ),
            );
        }

        // Extraire les opportunités d'amélioration (audits avec des économies potentielles)
        foreach ( $metrics as $audit_id => $audit ) {
            if ( isset( $audit['details'] ) && isset( $audit['details']['type'] ) && $audit['details']['type'] === 'opportunity' ) {
                $pagespeed_results['opportunities'][ $audit_id ] = array(
                    'title' => $audit['title'],
                    'description' => $audit['description'],
                    'score' => isset( $audit['score'] ) ? round( $audit['score'] * 100 ) : 0,
                    'display_value' => isset( $audit['displayValue'] ) ? $audit['displayValue'] : '',
                    'savings_ms' => isset( $audit['details']['overallSavingsMs'] ) ? $audit['details']['overallSavingsMs'] : 0,
                    'savings_bytes' => isset( $audit['details']['overallSavingsBytes'] ) ? $audit['details']['overallSavingsBytes'] : 0,
                    'items' => isset( $audit['details']['items'] ) ? $audit['details']['items'] : array(),
                );
            }
        }

        // Extraire les diagnostics (audits informatifs)
        foreach ( $metrics as $audit_id => $audit ) {
            if ( isset( $audit['details'] ) && isset( $audit['details']['type'] ) && $audit['details']['type'] === 'diagnostic' ) {
                $pagespeed_results['diagnostics'][ $audit_id ] = array(
                    'title' => $audit['title'],
                    'description' => $audit['description'],
                    'score' => isset( $audit['score'] ) ? round( $audit['score'] * 100 ) : 0,
                    'display_value' => isset( $audit['displayValue'] ) ? $audit['displayValue'] : '',
                    'items' => isset( $audit['details']['items'] ) ? $audit['details']['items'] : array(),
                );
            }
        }

        // Extraire les audits réussis
        foreach ( $metrics as $audit_id => $audit ) {
            if ( isset( $audit['score'] ) && $audit['score'] === 1 && ! isset( $pagespeed_results['opportunities'][ $audit_id ] ) && ! isset( $pagespeed_results['diagnostics'][ $audit_id ] ) ) {
                $pagespeed_results['passed_audits'][ $audit_id ] = array(
                    'title' => $audit['title'],
                    'description' => $audit['description'],
                );
            }
        }

        // Formater les données des Core Web Vitals pour la compatibilité avec le code existant
        $core_web_vitals = array(
            'lcp' => array(
                'name' => 'LCP',
                'value' => round( $metrics['largest-contentful-paint']['numericValue'] / 1000, 1 ),
                'unit' => 's',
                'status' => $this->get_metric_status( 'lcp', $metrics['largest-contentful-paint']['numericValue'] / 1000 ),
                'description' => __( 'Largest Contentful Paint', 'boss-seo' ),
            ),
            'fid' => array(
                'name' => 'FID',
                'value' => round( $metrics['max-potential-fid']['numericValue'] ),
                'unit' => 'ms',
                'status' => $this->get_metric_status( 'fid', $metrics['max-potential-fid']['numericValue'] ),
                'description' => __( 'First Input Delay', 'boss-seo' ),
            ),
            'cls' => array(
                'name' => 'CLS',
                'value' => round( $metrics['cumulative-layout-shift']['numericValue'], 2 ),
                'unit' => '',
                'status' => $this->get_metric_status( 'cls', $metrics['cumulative-layout-shift']['numericValue'] ),
                'description' => __( 'Cumulative Layout Shift', 'boss-seo' ),
            ),
            'ttfb' => array(
                'name' => 'TTFB',
                'value' => round( $metrics['server-response-time']['numericValue'] ),
                'unit' => 'ms',
                'status' => $this->get_metric_status( 'ttfb', $metrics['server-response-time']['numericValue'] ),
                'description' => __( 'Time to First Byte', 'boss-seo' ),
            ),
            'fcp' => array(
                'name' => 'FCP',
                'value' => round( $metrics['first-contentful-paint']['numericValue'] / 1000, 1 ),
                'unit' => 's',
                'status' => $this->get_metric_status( 'fcp', $metrics['first-contentful-paint']['numericValue'] / 1000 ),
                'description' => __( 'First Contentful Paint', 'boss-seo' ),
            ),
        );

        // Stocker les résultats complets pour une utilisation ultérieure
        update_option( 'boss_optimizer_last_pagespeed_results', $pagespeed_results, false );

        return $core_web_vitals;
    }

    /**
     * Détermine le statut d'une métrique en fonction de sa valeur
     *
     * @param string $metric Nom de la métrique
     * @param float $value Valeur de la métrique
     * @return string Statut de la métrique (good, needs-improvement, poor)
     */
    private function get_metric_status( $metric, $value ) {
        switch ( $metric ) {
            case 'lcp':
                if ( $value <= 2.5 ) {
                    return 'good';
                } elseif ( $value <= 4 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            case 'fid':
                if ( $value <= 100 ) {
                    return 'good';
                } elseif ( $value <= 300 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            case 'cls':
                if ( $value <= 0.1 ) {
                    return 'good';
                } elseif ( $value <= 0.25 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            case 'ttfb':
                if ( $value <= 200 ) {
                    return 'good';
                } elseif ( $value <= 500 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            case 'fcp':
                if ( $value <= 1.8 ) {
                    return 'good';
                } elseif ( $value <= 3 ) {
                    return 'needs-improvement';
                } else {
                    return 'poor';
                }

            default:
                return 'unknown';
        }
    }

    /**
     * Analyse une URL spécifique avec PageSpeed Insights
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function analyze_specific_url( $request ) {
        // Récupérer l'URL à analyser
        $url = isset( $request['url'] ) ? esc_url_raw( $request['url'] ) : '';

        // Si aucune URL n'est spécifiée, utiliser la page d'accueil
        if ( empty( $url ) ) {
            $url = home_url();
        }

        // Récupérer la stratégie (mobile ou desktop)
        $strategy = isset( $request['strategy'] ) ? sanitize_text_field( $request['strategy'] ) : 'mobile';

        // Vérifier si l'URL appartient au site
        $site_url = home_url();
        $site_domain = parse_url( $site_url, PHP_URL_HOST );
        $url_domain = parse_url( $url, PHP_URL_HOST );

        if ( $site_domain !== $url_domain ) {
            return new WP_Error( 'invalid_url', __( 'L\'URL doit appartenir à votre site.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les données de PageSpeed Insights pour cette URL
        $pagespeed_data = $this->get_pagespeed_data( $url, $strategy );

        if ( ! $pagespeed_data ) {
            return new WP_Error( 'pagespeed_error', __( 'Erreur lors de l\'analyse avec PageSpeed Insights.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        // Récupérer les résultats complets stockés
        $pagespeed_results = get_option( 'boss_optimizer_last_pagespeed_results', array() );

        // Générer des suggestions d'amélioration avec l'IA
        $ai_suggestions = $this->generate_ai_suggestions( $pagespeed_results );

        // Ajouter les suggestions d'IA aux résultats
        $pagespeed_results['ai_suggestions'] = $ai_suggestions;

        // Sauvegarder l'analyse dans l'historique
        $analysis_id = 'a' . time();
        $this->save_url_analysis( $analysis_id, $url, $strategy, $pagespeed_results );

        return rest_ensure_response( array(
            'success' => true,
            'analysis_id' => $analysis_id,
            'url' => $url,
            'strategy' => $strategy,
            'results' => $pagespeed_results,
        ) );
    }

    /**
     * Génère des suggestions d'amélioration avec l'IA basées sur les résultats de PageSpeed Insights
     *
     * @param array $pagespeed_results Résultats de PageSpeed Insights
     * @return array Suggestions d'amélioration
     */
    private function generate_ai_suggestions( $pagespeed_results ) {
        // Vérifier si les résultats sont valides
        if ( empty( $pagespeed_results ) || ! isset( $pagespeed_results['opportunities'] ) ) {
            return array();
        }

        // Initialiser les suggestions
        $suggestions = array(
            'performance' => array(),
            'accessibility' => array(),
            'best_practices' => array(),
            'seo' => array(),
        );

        // Extraire les principales opportunités d'amélioration
        $opportunities = array_slice( $pagespeed_results['opportunities'], 0, 5 );

        // Pour chaque opportunité, générer une suggestion détaillée
        foreach ( $opportunities as $opportunity_id => $opportunity ) {
            // Déterminer la catégorie
            $category = 'performance'; // Par défaut

            if ( strpos( $opportunity_id, 'accessibility' ) !== false ) {
                $category = 'accessibility';
            } elseif ( strpos( $opportunity_id, 'best-practices' ) !== false ) {
                $category = 'best_practices';
            } elseif ( strpos( $opportunity_id, 'seo' ) !== false ) {
                $category = 'seo';
            }

            // Créer une suggestion détaillée
            $suggestion = array(
                'title' => $opportunity['title'],
                'description' => $opportunity['description'],
                'impact' => $this->get_opportunity_impact( $opportunity ),
                'solution' => $this->get_opportunity_solution( $opportunity_id, $opportunity ),
                'difficulty' => $this->get_opportunity_difficulty( $opportunity_id ),
                'items' => isset( $opportunity['items'] ) ? array_slice( $opportunity['items'], 0, 3 ) : array(),
            );

            $suggestions[ $category ][] = $suggestion;
        }

        // Ajouter des suggestions générales basées sur les scores
        if ( isset( $pagespeed_results['scores'] ) ) {
            foreach ( $pagespeed_results['scores'] as $category_id => $score_data ) {
                if ( $score_data['score'] < 90 ) {
                    $general_suggestion = $this->get_general_suggestion( $category_id, $score_data['score'] );
                    if ( ! empty( $general_suggestion ) ) {
                        $category_key = str_replace( '-', '_', $category_id );
                        if ( ! isset( $suggestions[ $category_key ] ) ) {
                            $suggestions[ $category_key ] = array();
                        }
                        $suggestions[ $category_key ][] = $general_suggestion;
                    }
                }
            }
        }

        return $suggestions;
    }

    /**
     * Détermine l'impact d'une opportunité d'amélioration
     *
     * @param array $opportunity Opportunité d'amélioration
     * @return string Impact (high, medium, low)
     */
    private function get_opportunity_impact( $opportunity ) {
        // Déterminer l'impact en fonction des économies potentielles
        if ( isset( $opportunity['savings_ms'] ) && $opportunity['savings_ms'] > 1000 ) {
            return 'high';
        } elseif ( isset( $opportunity['savings_ms'] ) && $opportunity['savings_ms'] > 500 ) {
            return 'medium';
        } elseif ( isset( $opportunity['savings_bytes'] ) && $opportunity['savings_bytes'] > 100000 ) {
            return 'high';
        } elseif ( isset( $opportunity['savings_bytes'] ) && $opportunity['savings_bytes'] > 50000 ) {
            return 'medium';
        } elseif ( isset( $opportunity['score'] ) && $opportunity['score'] < 50 ) {
            return 'high';
        } elseif ( isset( $opportunity['score'] ) && $opportunity['score'] < 80 ) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Détermine la difficulté de mise en œuvre d'une solution pour une opportunité
     *
     * @param string $opportunity_id Identifiant de l'opportunité
     * @return string Difficulté (high, medium, low)
     */
    private function get_opportunity_difficulty( $opportunity_id ) {
        // Opportunités faciles à mettre en œuvre
        $easy_opportunities = array(
            'uses-responsive-images',
            'offscreen-images',
            'uses-rel-preconnect',
            'meta-description',
            'document-title',
            'http-status-code',
            'link-text',
            'is-crawlable',
        );

        // Opportunités de difficulté moyenne
        $medium_opportunities = array(
            'render-blocking-resources',
            'uses-optimized-images',
            'uses-webp-images',
            'uses-text-compression',
            'uses-responsive-images',
            'efficient-animated-content',
            'duplicate-id-active',
            'duplicate-id-aria',
            'valid-lang',
            'hreflang',
            'canonical',
        );

        // Opportunités difficiles à mettre en œuvre
        $hard_opportunities = array(
            'third-party-summary',
            'third-party-facades',
            'legacy-javascript',
            'unused-javascript',
            'unused-css-rules',
            'unminified-javascript',
            'unminified-css',
            'server-response-time',
            'redirects',
            'critical-request-chains',
            'dom-size',
        );

        if ( in_array( $opportunity_id, $easy_opportunities ) ) {
            return 'low';
        } elseif ( in_array( $opportunity_id, $medium_opportunities ) ) {
            return 'medium';
        } elseif ( in_array( $opportunity_id, $hard_opportunities ) ) {
            return 'high';
        } else {
            return 'medium';
        }
    }

    /**
     * Génère une solution pour une opportunité d'amélioration
     *
     * @param string $opportunity_id Identifiant de l'opportunité
     * @param array $opportunity Opportunité d'amélioration
     * @return string Solution
     */
    private function get_opportunity_solution( $opportunity_id, $opportunity ) {
        // Solutions pour les opportunités courantes
        $solutions = array(
            'render-blocking-resources' => __( 'Utilisez le chargement asynchrone ou différé pour les ressources JavaScript non critiques. Combinez et minifiez les fichiers CSS et JavaScript. Utilisez l\'attribut media pour les feuilles de style conditionnelles.', 'boss-seo' ),

            'uses-optimized-images' => __( 'Optimisez vos images en utilisant des formats modernes comme WebP. Compressez les images sans perte de qualité perceptible. Utilisez des services d\'optimisation d\'images comme TinyPNG ou ShortPixel.', 'boss-seo' ),

            'uses-webp-images' => __( 'Convertissez vos images au format WebP qui offre une meilleure compression que PNG ou JPEG. Utilisez un plugin WordPress pour générer automatiquement des versions WebP de vos images.', 'boss-seo' ),

            'offscreen-images' => __( 'Implémentez le chargement paresseux (lazy loading) pour les images qui ne sont pas visibles dans la fenêtre d\'affichage initiale. Utilisez l\'attribut loading="lazy" ou un plugin de lazy loading.', 'boss-seo' ),

            'unused-css-rules' => __( 'Identifiez et supprimez les règles CSS inutilisées. Utilisez des outils comme PurgeCSS pour éliminer le CSS non utilisé. Divisez votre CSS en fichiers critiques et non critiques.', 'boss-seo' ),

            'unused-javascript' => __( 'Identifiez et supprimez le JavaScript inutilisé. Utilisez le fractionnement de code (code splitting) pour charger uniquement le JavaScript nécessaire. Implémentez le chargement à la demande pour les fonctionnalités non essentielles.', 'boss-seo' ),

            'server-response-time' => __( 'Optimisez votre serveur et votre base de données. Utilisez un système de mise en cache comme WP Rocket, W3 Total Cache ou LiteSpeed Cache. Envisagez d\'utiliser un CDN pour distribuer votre contenu.', 'boss-seo' ),

            'redirects' => __( 'Éliminez les redirections en chaîne. Mettez à jour les liens internes pour pointer directement vers l\'URL finale. Utilisez des redirections 301 pour les URL permanentes.', 'boss-seo' ),

            'uses-rel-preconnect' => __( 'Ajoutez des indications de préconnexion pour les domaines tiers importants. Utilisez les balises <link rel="preconnect"> pour établir des connexions anticipées.', 'boss-seo' ),

            'dom-size' => __( 'Réduisez la complexité de votre page en simplifiant la structure HTML. Limitez le nombre d\'éléments imbriqués. Utilisez la pagination ou le chargement à la demande pour les listes longues.', 'boss-seo' ),
        );

        // Retourner la solution spécifique si elle existe, sinon une solution générique
        if ( isset( $solutions[ $opportunity_id ] ) ) {
            return $solutions[ $opportunity_id ];
        } else {
            return sprintf(
                __( 'Examinez les détails de cette opportunité et suivez les recommandations de Google PageSpeed Insights. %s', 'boss-seo' ),
                isset( $opportunity['description'] ) ? $opportunity['description'] : ''
            );
        }
    }

    /**
     * Génère une suggestion générale basée sur la catégorie et le score
     *
     * @param string $category_id Identifiant de la catégorie
     * @param int $score Score
     * @return array Suggestion générale
     */
    private function get_general_suggestion( $category_id, $score ) {
        $suggestions = array(
            'performance' => array(
                'title' => __( 'Amélioration globale des performances', 'boss-seo' ),
                'description' => __( 'Votre site a des problèmes de performance qui peuvent affecter l\'expérience utilisateur et le référencement.', 'boss-seo' ),
                'solution' => __( 'Concentrez-vous sur l\'optimisation des images, la réduction des ressources bloquantes, l\'amélioration du temps de réponse du serveur et la mise en cache. Utilisez un plugin de cache comme WP Rocket ou LiteSpeed Cache.', 'boss-seo' ),
                'impact' => 'high',
                'difficulty' => 'medium',
            ),
            'accessibility' => array(
                'title' => __( 'Amélioration de l\'accessibilité', 'boss-seo' ),
                'description' => __( 'Votre site présente des problèmes d\'accessibilité qui peuvent rendre difficile son utilisation pour certains visiteurs.', 'boss-seo' ),
                'solution' => __( 'Assurez-vous que tous les éléments interactifs sont accessibles au clavier, que les images ont des textes alternatifs, que les contrastes de couleur sont suffisants et que la structure des titres est logique.', 'boss-seo' ),
                'impact' => 'medium',
                'difficulty' => 'medium',
            ),
            'best-practices' => array(
                'title' => __( 'Amélioration des bonnes pratiques', 'boss-seo' ),
                'description' => __( 'Votre site ne suit pas certaines bonnes pratiques web qui peuvent affecter la sécurité et la fiabilité.', 'boss-seo' ),
                'solution' => __( 'Assurez-vous d\'utiliser HTTPS, de mettre à jour les bibliothèques JavaScript, d\'éviter les erreurs JavaScript et d\'utiliser des en-têtes de sécurité appropriés.', 'boss-seo' ),
                'impact' => 'medium',
                'difficulty' => 'medium',
            ),
            'seo' => array(
                'title' => __( 'Amélioration du SEO technique', 'boss-seo' ),
                'description' => __( 'Votre site présente des problèmes de SEO technique qui peuvent affecter son classement dans les moteurs de recherche.', 'boss-seo' ),
                'solution' => __( 'Assurez-vous que toutes les pages ont des titres et des méta-descriptions uniques, que le contenu est indexable, que les liens ont des textes descriptifs et que le site est adapté aux mobiles.', 'boss-seo' ),
                'impact' => 'high',
                'difficulty' => 'low',
            ),
        );

        if ( isset( $suggestions[ $category_id ] ) ) {
            $suggestion = $suggestions[ $category_id ];

            // Ajuster l'impact en fonction du score
            if ( $score < 50 ) {
                $suggestion['impact'] = 'high';
            } elseif ( $score < 80 ) {
                $suggestion['impact'] = 'medium';
            } else {
                $suggestion['impact'] = 'low';
            }

            return $suggestion;
        }

        return array();
    }

    /**
     * Sauvegarde les résultats d'une analyse d'URL spécifique
     *
     * @param string $analysis_id Identifiant de l'analyse
     * @param string $url URL analysée
     * @param string $strategy Stratégie utilisée
     * @param array $results Résultats de l'analyse
     */
    private function save_url_analysis( $analysis_id, $url, $strategy, $results ) {
        // Récupérer l'historique des analyses d'URL
        $url_analyses = get_option( 'boss_optimizer_url_analyses', array() );

        // Limiter l'historique à 20 analyses
        if ( count( $url_analyses ) >= 20 ) {
            array_pop( $url_analyses );
        }

        // Ajouter la nouvelle analyse
        array_unshift( $url_analyses, array(
            'id' => $analysis_id,
            'url' => $url,
            'strategy' => $strategy,
            'date' => current_time( 'mysql' ),
            'scores' => isset( $results['scores'] ) ? $results['scores'] : array(),
        ) );

        // Sauvegarder l'historique mis à jour
        update_option( 'boss_optimizer_url_analyses', $url_analyses, false );

        // Sauvegarder les résultats complets
        update_option( 'boss_optimizer_url_analysis_' . $analysis_id, $results, false );
    }

    /**
     * Récupère l'historique des analyses d'URL
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_url_analysis_history( $request ) {
        // Récupérer l'historique des analyses d'URL
        $url_analyses = get_option( 'boss_optimizer_url_analyses', array() );

        return rest_ensure_response( $url_analyses );
    }

    /**
     * Récupère les résultats d'une analyse d'URL spécifique
     *
     * @param WP_REST_Request $request Requête REST.
     * @return WP_REST_Response
     */
    public function get_url_analysis_results( $request ) {
        // Récupérer l'ID de l'analyse
        $analysis_id = $request['id'];

        // Récupérer les résultats de l'analyse
        $results = get_option( 'boss_optimizer_url_analysis_' . $analysis_id, array() );

        if ( empty( $results ) ) {
            return new WP_Error( 'analysis_not_found', __( 'Analyse introuvable.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        return rest_ensure_response( $results );
    }

    /**
     * Récupère l'historique des performances
     *
     * @return array Historique des performances
     */
    private function get_performance_history() {
        // Récupérer l'historique des performances depuis la base de données
        $history = get_option( 'boss_optimizer_performance_history', array() );

        if ( empty( $history ) ) {
            // Générer des données fictives pour la démo
            $current_month = intval( date( 'n' ) );
            $history = array();

            for ( $i = 4; $i >= 0; $i-- ) {
                $month = $current_month - $i;
                if ( $month <= 0 ) {
                    $month += 12;
                }

                $history[] = array(
                    'date' => date( 'Y-m', strtotime( "-{$i} months" ) ),
                    'score' => mt_rand( 60, 90 ),
                );
            }

            // Trier par date (du plus récent au plus ancien)
            usort( $history, function( $a, $b ) {
                return strcmp( $b['date'], $a['date'] );
            } );
        }

        return $history;
    }

    /**
     * Sauvegarde les résultats d'une analyse
     *
     * @param string $analysis_id ID de l'analyse
     * @param array $results Résultats de l'analyse
     */
    private function save_analysis_results( $analysis_id, $results ) {
        // Ajouter l'ID de l'analyse aux résultats
        $results['id'] = $analysis_id;
        $results['date'] = current_time( 'mysql' );

        // Sauvegarder les résultats complets
        update_option( "boss_optimizer_analysis_{$analysis_id}", $results );

        // Mettre à jour l'historique des analyses
        $history = $this->get_analysis_history_data();

        // Ajouter la nouvelle analyse à l'historique
        $history_item = array(
            'id' => $analysis_id,
            'date' => $results['date'],
            'score' => $results['score'],
            'issues' => array(
                'critical' => count( $results['issues']['critical'] ),
                'errors' => count( $results['issues']['errors'] ),
                'warnings' => count( $results['issues']['warnings'] ),
                'improvements' => count( $results['issues']['improvements'] ),
                'successes' => count( $results['issues']['successes'] ),
            ),
        );

        // Ajouter au début de l'historique
        array_unshift( $history, $history_item );

        // Limiter l'historique à 20 analyses
        if ( count( $history ) > 20 ) {
            $history = array_slice( $history, 0, 20 );
        }

        // Sauvegarder l'historique
        update_option( 'boss_optimizer_analysis_history', $history );

        // Mettre à jour l'historique des performances
        $this->update_performance_history( $results );
    }

    /**
     * Récupère les résultats d'une analyse
     *
     * @param string $analysis_id ID de l'analyse
     * @return array|false Résultats de l'analyse ou false si non trouvée
     */
    private function get_analysis_results( $analysis_id ) {
        return get_option( "boss_optimizer_analysis_{$analysis_id}", false );
    }

    /**
     * Récupère l'historique des analyses
     *
     * @return array Historique des analyses
     */
    private function get_analysis_history_data() {
        return get_option( 'boss_optimizer_analysis_history', array() );
    }

    /**
     * Met à jour l'historique des performances
     *
     * @param array $results Résultats de l'analyse
     */
    private function update_performance_history( $results ) {
        // Récupérer l'historique des performances
        $history = $this->get_performance_history();

        // Calculer le score de performance
        $performance_score = 0;
        $metrics = $results['performance']['coreWebVitals'];
        $good_count = 0;
        $needs_improvement_count = 0;
        $total_count = count( $metrics );

        foreach ( $metrics as $metric ) {
            if ( $metric['status'] === 'good' ) {
                $good_count++;
            } elseif ( $metric['status'] === 'needs-improvement' ) {
                $needs_improvement_count++;
            }
        }

        // Pondération : bon = 1, à améliorer = 0.5, mauvais = 0
        $performance_score = round( ( $good_count + $needs_improvement_count * 0.5 ) / $total_count * 100 );

        // Ajouter le nouveau score à l'historique
        $current_date = date( 'Y-m' );
        $found = false;

        foreach ( $history as &$item ) {
            if ( $item['date'] === $current_date ) {
                $item['score'] = $performance_score;
                $found = true;
                break;
            }
        }

        if ( ! $found ) {
            array_unshift( $history, array(
                'date' => $current_date,
                'score' => $performance_score,
            ) );
        }

        // Limiter l'historique à 12 mois
        if ( count( $history ) > 12 ) {
            $history = array_slice( $history, 0, 12 );
        }

        // Sauvegarder l'historique
        update_option( 'boss_optimizer_performance_history', $history );
    }

    /**
     * Retourne des données d'analyse vides
     *
     * @return array Données d'analyse vides
     */
    private function get_empty_analysis_data() {
        return array(
            'id' => '',
            'date' => current_time( 'mysql' ),
            'score' => 0,
            'issues' => array(
                'critical' => array(),
                'errors' => array(),
                'warnings' => array(),
                'improvements' => array(),
                'successes' => array(),
            ),
            'performance' => array(
                'coreWebVitals' => array(
                    'lcp' => array(
                        'name' => 'LCP',
                        'value' => 0,
                        'unit' => 's',
                        'status' => 'unknown',
                        'description' => __( 'Largest Contentful Paint', 'boss-seo' ),
                    ),
                    'fid' => array(
                        'name' => 'FID',
                        'value' => 0,
                        'unit' => 'ms',
                        'status' => 'unknown',
                        'description' => __( 'First Input Delay', 'boss-seo' ),
                    ),
                    'cls' => array(
                        'name' => 'CLS',
                        'value' => 0,
                        'unit' => '',
                        'status' => 'unknown',
                        'description' => __( 'Cumulative Layout Shift', 'boss-seo' ),
                    ),
                    'ttfb' => array(
                        'name' => 'TTFB',
                        'value' => 0,
                        'unit' => 'ms',
                        'status' => 'unknown',
                        'description' => __( 'Time to First Byte', 'boss-seo' ),
                    ),
                    'fcp' => array(
                        'name' => 'FCP',
                        'value' => 0,
                        'unit' => 's',
                        'status' => 'unknown',
                        'description' => __( 'First Contentful Paint', 'boss-seo' ),
                    ),
                ),
                'history' => array(),
            ),
        );
    }

    /**
     * Méthodes de vérification spécifiques (stubs pour l'implémentation future)
     */
    private function check_titles_and_descriptions( &$results ) {}
    private function check_canonical_tags( &$results ) {}
    private function check_url_structure( &$results ) {}
    private function check_links( &$results ) {}
    private function check_sitemap( &$results ) {}
    private function check_robots_txt( &$results ) {}
    private function check_image_alt_attributes( &$results ) {}
    private function check_color_contrasts( &$results ) {}
    private function check_heading_structure( &$results ) {}
    private function check_aria_attributes( &$results ) {}
    private function check_keyboard_navigation( &$results ) {}
    private function check_javascript_errors( &$results ) {}
    private function check_https_usage( &$results ) {}
    private function check_security_headers( &$results ) {}
    private function check_deprecated_libraries( &$results ) {}
    private function check_console_issues( &$results ) {}
    private function check_wordpress_version( &$results ) {}
    private function check_plugin_versions( &$results ) {}
    private function check_known_vulnerabilities( &$results ) {}
    private function check_file_permissions( &$results ) {}
    private function check_sql_injection_protection( &$results ) {}
}