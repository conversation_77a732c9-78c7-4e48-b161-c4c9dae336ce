<?php
/**
 * Tests pour vérifier les corrections du module Boss Optimizer.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/tests
 */

class Test_Boss_Optimizer_Fixes extends WP_UnitTestCase {

    /**
     * Instance du module Boss Optimizer.
     *
     * @var Boss_Optimizer
     */
    private $optimizer;

    /**
     * Instance de la classe de contenu.
     *
     * @var Boss_Optimizer_Content
     */
    private $content;

    /**
     * Instance de l'API.
     *
     * @var Boss_Optimizer_API
     */
    private $api;

    /**
     * Configuration initiale des tests.
     */
    public function setUp() {
        parent::setUp();

        // Charger les classes nécessaires
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-settings.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-content.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-analysis.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-recommendations.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-ai.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-api.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-cache.php';

        // Initialiser les instances
        $settings = new Boss_Optimizer_Settings( 'boss-seo' );
        $this->content = new Boss_Optimizer_Content( 'boss-seo', $settings );
        
        $analysis = new Boss_Optimizer_Analysis( 'boss-seo', $settings );
        $recommendations = new Boss_Optimizer_Recommendations( 'boss-seo', $settings );
        $ai = new Boss_Optimizer_AI( 'boss-seo', $settings );
        
        $this->api = new Boss_Optimizer_API(
            'boss-seo',
            '1.1.0',
            $this->content,
            $analysis,
            $recommendations,
            $ai,
            $settings
        );
    }

    /**
     * Test de la pagination corrigée.
     */
    public function test_pagination_fix() {
        // Créer des posts de test
        $post_ids = array();
        for ( $i = 1; $i <= 25; $i++ ) {
            $post_id = $this->factory->post->create( array(
                'post_title' => 'Test Post ' . $i,
                'post_content' => 'Contenu de test pour le post ' . $i,
                'post_status' => 'publish'
            ) );
            $post_ids[] = $post_id;
            
            // Ajouter des scores SEO variés
            if ( $i <= 5 ) {
                update_post_meta( $post_id, '_boss_seo_score', 90 ); // Bon score
            } elseif ( $i <= 15 ) {
                update_post_meta( $post_id, '_boss_seo_score', 65 ); // Score moyen
            } else {
                update_post_meta( $post_id, '_boss_seo_score', 30 ); // Mauvais score
            }
        }

        // Test de pagination normale
        $results = $this->content->get_contents( array(
            'page' => 1,
            'per_page' => 10
        ) );

        $this->assertEquals( 10, count( $results['contents'] ) );
        $this->assertEquals( 25, $results['total_items'] );
        $this->assertEquals( 3, $results['total_pages'] );

        // Test de filtrage par score SEO avec pagination
        $results_good = $this->content->get_contents( array(
            'seo_score' => 'good',
            'page' => 1,
            'per_page' => 10
        ) );

        $this->assertEquals( 5, count( $results_good['contents'] ) );
        $this->assertEquals( 5, $results_good['total_items'] );
        $this->assertEquals( 1, $results_good['total_pages'] );

        // Vérifier que tous les scores sont >= 80
        foreach ( $results_good['contents'] as $content ) {
            $this->assertGreaterThanOrEqual( 80, $content['seo_score'] );
        }
    }

    /**
     * Test du système de cache.
     */
    public function test_cache_system() {
        $cache = Boss_Optimizer_Cache::get_instance();

        // Créer un post de test
        $post_id = $this->factory->post->create( array(
            'post_title' => 'Test Cache Post',
            'post_content' => 'Contenu de test pour le cache'
        ) );

        // Test du cache de score SEO
        $score = 85;
        $this->content->update_seo_score( $post_id, $score );
        
        // Le score devrait être en cache maintenant
        $cached_score = $cache->get_seo_score( $post_id );
        $this->assertEquals( $score, $cached_score );

        // Test du cache de recommandations
        $recommendations = array(
            array( 'type' => 'title', 'message' => 'Améliorer le titre' ),
            array( 'type' => 'content', 'message' => 'Ajouter plus de contenu' )
        );
        $this->content->update_recommendations( $post_id, $recommendations );
        
        // Les recommandations devraient être en cache
        $cached_recommendations = $cache->get_recommendations( $post_id );
        $this->assertEquals( $recommendations, $cached_recommendations );

        // Test d'invalidation du cache
        $cache->invalidate_post_cache( $post_id );
        $this->assertFalse( $cache->get_seo_score( $post_id ) );
        $this->assertFalse( $cache->get_recommendations( $post_id ) );
    }

    /**
     * Test des nouveaux endpoints API.
     */
    public function test_new_api_endpoints() {
        // Créer des posts de test
        $post_ids = array();
        for ( $i = 1; $i <= 5; $i++ ) {
            $post_id = $this->factory->post->create( array(
                'post_title' => 'API Test Post ' . $i,
                'post_content' => 'Contenu de test pour l\'API ' . $i
            ) );
            $post_ids[] = $post_id;
        }

        // Test de l'endpoint optimize/all
        $request = new WP_REST_Request( 'POST', '/boss-seo/v1/optimize/all' );
        $request->set_param( 'settings', array( 'title_seo' => true ) );
        $request->set_param( 'filters', array( 'content_type' => 'post' ) );

        // Simuler un utilisateur avec les bonnes permissions
        $user_id = $this->factory->user->create( array( 'role' => 'administrator' ) );
        wp_set_current_user( $user_id );

        $response = $this->api->optimize_all_contents( $request );
        $this->assertInstanceOf( 'WP_REST_Response', $response );

        // Test de l'endpoint analyze/all
        $request = new WP_REST_Request( 'POST', '/boss-seo/v1/analyze/all' );
        $request->set_param( 'filters', array( 'content_type' => 'post' ) );

        $response = $this->api->analyze_all_contents( $request );
        $this->assertInstanceOf( 'WP_REST_Response', $response );

        // Test de l'endpoint tags/add
        $request = new WP_REST_Request( 'POST', '/boss-seo/v1/tags/add' );
        $request->set_param( 'ids', array( $post_ids[0], $post_ids[1] ) );
        $request->set_param( 'tags', array( 'test', 'seo', 'optimization' ) );

        $response = $this->api->add_tags_to_contents( $request );
        $this->assertInstanceOf( 'WP_REST_Response', $response );
        
        $data = $response->get_data();
        $this->assertTrue( $data['success'] );
        $this->assertEquals( 2, $data['summary']['success'] );
    }

    /**
     * Test des permissions granulaires.
     */
    public function test_granular_permissions() {
        // Créer un post de test
        $post_id = $this->factory->post->create( array(
            'post_title' => 'Permission Test Post',
            'post_content' => 'Contenu de test pour les permissions'
        ) );

        // Test avec un utilisateur sans permissions
        $user_id = $this->factory->user->create( array( 'role' => 'subscriber' ) );
        wp_set_current_user( $user_id );

        $request = new WP_REST_Request( 'POST', '/boss-seo/v1/optimize/' . $post_id );
        $request->set_param( 'id', $post_id );

        $can_optimize = $this->api->optimize_content_permissions_check( $request );
        $this->assertFalse( $can_optimize );

        // Test avec un utilisateur avec permissions
        $admin_id = $this->factory->user->create( array( 'role' => 'administrator' ) );
        wp_set_current_user( $admin_id );

        $can_optimize = $this->api->optimize_content_permissions_check( $request );
        $this->assertTrue( $can_optimize );
    }

    /**
     * Test de la gestion d'erreurs améliorée.
     */
    public function test_error_handling() {
        // Test avec un ID de post inexistant
        $request = new WP_REST_Request( 'POST', '/boss-seo/v1/optimize/999999' );
        $request->set_param( 'id', 999999 );
        $request->set_param( 'settings', array( 'title_seo' => true ) );

        $user_id = $this->factory->user->create( array( 'role' => 'administrator' ) );
        wp_set_current_user( $user_id );

        $response = $this->api->optimize_content( $request );
        $this->assertInstanceOf( 'WP_REST_Response', $response );
        
        $data = $response->get_data();
        $this->assertFalse( $data['success'] );
        $this->assertContains( 'non trouvé', $data['message'] );
    }

    /**
     * Test des requêtes optimisées (bulk).
     */
    public function test_bulk_queries() {
        // Créer plusieurs posts
        $post_ids = array();
        for ( $i = 1; $i <= 10; $i++ ) {
            $post_id = $this->factory->post->create( array(
                'post_title' => 'Bulk Test Post ' . $i,
                'post_content' => 'Contenu de test pour les requêtes groupées ' . $i
            ) );
            $post_ids[] = $post_id;
            update_post_meta( $post_id, '_boss_seo_score', 50 + $i );
        }

        // Test des requêtes groupées pour les scores
        $scores = $this->content->get_seo_scores_bulk( $post_ids );
        $this->assertEquals( 10, count( $scores ) );
        
        foreach ( $post_ids as $index => $post_id ) {
            $expected_score = 50 + ( $index + 1 );
            $this->assertEquals( $expected_score, $scores[ $post_id ] );
        }

        // Test des requêtes groupées pour les recommandations
        $recommendations = $this->content->get_recommendations_bulk( $post_ids );
        $this->assertEquals( 10, count( $recommendations ) );
        
        foreach ( $post_ids as $post_id ) {
            $this->assertIsArray( $recommendations[ $post_id ] );
        }
    }

    /**
     * Nettoyage après les tests.
     */
    public function tearDown() {
        // Vider le cache
        $cache = Boss_Optimizer_Cache::get_instance();
        $cache->flush();
        
        parent::tearDown();
    }
}
