<?php
/**
 * Classe pour gérer les emplacements locaux.
 *
 * Cette classe gère les emplacements locaux pour le module SEO local.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour gérer les emplacements locaux.
 *
 * Cette classe gère les emplacements locaux pour le module SEO local.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Locations_Fix {

    /**
     * Récupère les emplacements.
     *
     * @since    1.2.0
     * @param    int       $page       Le numéro de page.
     * @param    int       $per_page   Le nombre d'éléments par page.
     * @param    string    $search     La recherche.
     * @return   array                 Les emplacements.
     */
    public function get_locations_data( $page, $per_page, $search ) {
        // Vérifier si le type de post existe
        if (!post_type_exists('boss_local_location')) {
            // Si le type de post n'existe pas, retourner un tableau vide
            return array(
                'locations' => array(),
                'total'     => 0,
                'pages'     => 0,
            );
        }

        // Paramètres de la requête
        $args = array(
            'post_type'      => 'boss_local_location',
            'posts_per_page' => $per_page,
            'paged'          => $page,
            'post_status'    => 'publish',
        );

        // Ajouter la recherche
        if ( ! empty( $search ) ) {
            $args['s'] = $search;
        }

        // Exécuter la requête
        $query = new WP_Query( $args );

        // Préparer les résultats
        $locations = array();

        if ($query->have_posts()) {
            foreach ( $query->posts as $post ) {
                $location_data = $this->get_location_data( $post->ID );
                if ($location_data) {
                    $locations[] = $location_data;
                }
            }
        }

        return array(
            'locations' => $locations,
            'total'     => $query->found_posts,
            'pages'     => ceil( $query->found_posts / $per_page ),
        );
    }

    /**
     * Récupère un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array|false               L'emplacement ou false si non trouvé.
     */
    public function get_location_data( $location_id ) {
        $post = get_post( $location_id );

        if ( ! $post || $post->post_type !== 'boss_local_location' ) {
            return false;
        }

        // Récupérer les métadonnées
        $address = get_post_meta( $location_id, '_boss_local_address', true );
        $city = get_post_meta( $location_id, '_boss_local_city', true );
        $state = get_post_meta( $location_id, '_boss_local_state', true );
        $postal_code = get_post_meta( $location_id, '_boss_local_postal_code', true );
        $country = get_post_meta( $location_id, '_boss_local_country', true );
        $phone = get_post_meta( $location_id, '_boss_local_phone', true );
        $email = get_post_meta( $location_id, '_boss_local_email', true );
        $website = get_post_meta( $location_id, '_boss_local_website', true );
        $latitude = get_post_meta( $location_id, '_boss_local_latitude', true );
        $longitude = get_post_meta( $location_id, '_boss_local_longitude', true );
        $types = get_post_meta( $location_id, '_boss_local_types', true );

        // Préparer les données
        return array(
            'id'          => $location_id,
            'title'       => $post->post_title,
            'content'     => $post->post_content,
            'excerpt'     => $post->post_excerpt,
            'address'     => $address,
            'city'        => $city,
            'state'       => $state,
            'postal_code' => $postal_code,
            'country'     => $country,
            'phone'       => $phone,
            'email'       => $email,
            'website'     => $website,
            'latitude'    => $latitude,
            'longitude'   => $longitude,
            'types'       => $types ? $types : array(),
        );
    }
}
