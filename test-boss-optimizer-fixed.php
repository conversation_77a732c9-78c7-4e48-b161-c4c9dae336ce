<?php
/**
 * Test des corrections du module Boss Optimizer
 * 
 * Ce fichier vérifie que toutes les erreurs ont été corrigées
 * et que le module fonctionne correctement.
 */

echo "<!DOCTYPE html><html><head><title>🔧 Test Corrections Boss Optimizer</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔧 Test des Corrections Boss Optimizer</h1>";

/**
 * Classe de test pour vérifier les corrections
 */
class Boss_Optimizer_Fix_Test {
    
    private $errors = array();
    private $warnings = array();
    private $successes = array();
    
    public function run_all_tests() {
        echo "<h2>🔍 Vérification des Classes</h2>";
        $this->test_class_loading();
        
        echo "<h2>🏗️ Test de l'Héritage</h2>";
        $this->test_inheritance();
        
        echo "<h2>⚙️ Test des Méthodes</h2>";
        $this->test_methods();
        
        echo "<h2>🔗 Test des Hooks AJAX</h2>";
        $this->test_ajax_hooks();
        
        echo "<h2>📊 Résumé des Tests</h2>";
        $this->display_summary();
    }
    
    /**
     * Test du chargement des classes
     */
    private function test_class_loading() {
        $classes_to_test = array(
            'Boss_Optimizer' => 'includes/class-boss-optimizer.php',
            'Boss_Optimizer_Metabox_Secure' => 'includes/class-boss-optimizer-metabox-secure.php',
            'Boss_Optimizer_Metabox_Tabbed' => 'includes/class-boss-optimizer-metabox-tabbed.php',
            'Boss_SEO_Analyzer' => 'includes/class-boss-seo-analyzer.php',
            'Boss_SEO_Optimizer' => 'includes/class-boss-seo-optimizer.php',
            'Boss_SEO_Schema_Generator' => 'includes/class-boss-seo-schema-generator.php'
        );
        
        foreach ($classes_to_test as $class_name => $file_path) {
            if (file_exists($file_path)) {
                echo "<p class='info'>📁 Fichier {$file_path} : <span class='success'>Existe ✅</span></p>";
                
                // Vérifier la syntaxe PHP
                $syntax_check = shell_exec("php -l {$file_path} 2>&1");
                if (strpos($syntax_check, 'No syntax errors') !== false) {
                    echo "<p class='info'>🔧 Syntaxe {$class_name} : <span class='success'>Valide ✅</span></p>";
                    $this->successes[] = "Syntaxe {$class_name} valide";
                } else {
                    echo "<p class='error'>❌ Erreur de syntaxe dans {$class_name} : {$syntax_check}</p>";
                    $this->errors[] = "Erreur de syntaxe dans {$class_name}";
                }
            } else {
                echo "<p class='error'>❌ Fichier manquant : {$file_path}</p>";
                $this->errors[] = "Fichier manquant : {$file_path}";
            }
        }
    }
    
    /**
     * Test de l'héritage des classes
     */
    private function test_inheritance() {
        // Simuler l'environnement WordPress
        $this->mock_wordpress_functions();
        
        try {
            // Charger les classes dans l'ordre
            require_once 'includes/class-boss-optimizer-metabox-secure.php';
            require_once 'includes/class-boss-optimizer-metabox-tabbed.php';
            
            echo "<p class='success'>✅ Classes chargées sans erreur</p>";
            
            // Vérifier l'héritage
            if (class_exists('Boss_Optimizer_Metabox_Tabbed')) {
                $reflection = new ReflectionClass('Boss_Optimizer_Metabox_Tabbed');
                $parent = $reflection->getParentClass();
                
                if ($parent && $parent->getName() === 'Boss_Optimizer_Metabox_Secure') {
                    echo "<p class='success'>✅ Héritage correct : Boss_Optimizer_Metabox_Tabbed extends Boss_Optimizer_Metabox_Secure</p>";
                    $this->successes[] = "Héritage de classe correct";
                } else {
                    echo "<p class='error'>❌ Problème d'héritage</p>";
                    $this->errors[] = "Problème d'héritage";
                }
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erreur lors du chargement des classes : " . $e->getMessage() . "</p>";
            $this->errors[] = "Erreur de chargement : " . $e->getMessage();
        }
    }
    
    /**
     * Test des méthodes requises
     */
    private function test_methods() {
        if (!class_exists('Boss_Optimizer_Metabox_Secure')) {
            echo "<p class='error'>❌ Classe Boss_Optimizer_Metabox_Secure non disponible pour les tests</p>";
            return;
        }
        
        $required_methods = array(
            'get_score_class' => 'protected',
            'get_recommendation_icon' => 'private'
        );
        
        $reflection = new ReflectionClass('Boss_Optimizer_Metabox_Secure');
        
        foreach ($required_methods as $method_name => $expected_visibility) {
            if ($reflection->hasMethod($method_name)) {
                $method = $reflection->getMethod($method_name);
                
                $actual_visibility = 'public';
                if ($method->isProtected()) $actual_visibility = 'protected';
                if ($method->isPrivate()) $actual_visibility = 'private';
                
                if ($actual_visibility === $expected_visibility) {
                    echo "<p class='success'>✅ Méthode {$method_name} : {$actual_visibility}</p>";
                    $this->successes[] = "Méthode {$method_name} correcte";
                } else {
                    echo "<p class='warning'>⚠️ Méthode {$method_name} : {$actual_visibility} (attendu: {$expected_visibility})</p>";
                    $this->warnings[] = "Visibilité incorrecte pour {$method_name}";
                }
            } else {
                echo "<p class='error'>❌ Méthode manquante : {$method_name}</p>";
                $this->errors[] = "Méthode manquante : {$method_name}";
            }
        }
        
        // Vérifier les méthodes de rendu dans la classe enfant
        if (class_exists('Boss_Optimizer_Metabox_Tabbed')) {
            $tabbed_reflection = new ReflectionClass('Boss_Optimizer_Metabox_Tabbed');
            
            $render_methods = array(
                'render_keywords_tab',
                'render_metadata_tab', 
                'render_social_tab',
                'render_advanced_tab',
                'render_analysis_tab'
            );
            
            foreach ($render_methods as $method_name) {
                if ($tabbed_reflection->hasMethod($method_name)) {
                    $method = $tabbed_reflection->getMethod($method_name);
                    if ($method->isProtected()) {
                        echo "<p class='success'>✅ Méthode {$method_name} : protected</p>";
                        $this->successes[] = "Méthode {$method_name} accessible";
                    } else {
                        echo "<p class='warning'>⚠️ Méthode {$method_name} devrait être protected</p>";
                        $this->warnings[] = "Visibilité incorrecte pour {$method_name}";
                    }
                } else {
                    echo "<p class='error'>❌ Méthode de rendu manquante : {$method_name}</p>";
                    $this->errors[] = "Méthode de rendu manquante : {$method_name}";
                }
            }
        }
    }
    
    /**
     * Test des hooks AJAX
     */
    private function test_ajax_hooks() {
        $expected_hooks = array(
            'boss_seo_analyze_content_secure',
            'boss_seo_optimize_content_secure',
            'boss_seo_auto_save',
            'boss_seo_refresh_suggestions',
            'boss_seo_validate_field',
            'boss_seo_generate_schema'
        );
        
        echo "<p class='info'>🔗 Hooks AJAX attendus :</p>";
        echo "<ul>";
        foreach ($expected_hooks as $hook) {
            echo "<li class='success'>✅ wp_ajax_{$hook}</li>";
        }
        echo "</ul>";
        
        // Vérifier que les méthodes AJAX existent
        if (class_exists('Boss_Optimizer_Metabox_Tabbed')) {
            $reflection = new ReflectionClass('Boss_Optimizer_Metabox_Tabbed');
            
            $ajax_methods = array(
                'ajax_analyze_content',
                'ajax_optimize_content',
                'ajax_auto_save',
                'ajax_refresh_suggestions',
                'ajax_validate_field',
                'ajax_generate_schema'
            );
            
            foreach ($ajax_methods as $method_name) {
                if ($reflection->hasMethod($method_name)) {
                    echo "<p class='success'>✅ Méthode AJAX {$method_name} : Existe</p>";
                    $this->successes[] = "Méthode AJAX {$method_name} implémentée";
                } else {
                    echo "<p class='error'>❌ Méthode AJAX manquante : {$method_name}</p>";
                    $this->errors[] = "Méthode AJAX manquante : {$method_name}";
                }
            }
        }
    }
    
    /**
     * Affiche le résumé des tests
     */
    private function display_summary() {
        echo "<div style='background:#f0f0f0;padding:20px;border-radius:8px;'>";
        
        echo "<h3>📊 Résultats des Tests</h3>";
        
        echo "<p><strong>✅ Succès :</strong> " . count($this->successes) . "</p>";
        if (!empty($this->successes)) {
            echo "<ul>";
            foreach ($this->successes as $success) {
                echo "<li class='success'>{$success}</li>";
            }
            echo "</ul>";
        }
        
        echo "<p><strong>⚠️ Avertissements :</strong> " . count($this->warnings) . "</p>";
        if (!empty($this->warnings)) {
            echo "<ul>";
            foreach ($this->warnings as $warning) {
                echo "<li class='warning'>{$warning}</li>";
            }
            echo "</ul>";
        }
        
        echo "<p><strong>❌ Erreurs :</strong> " . count($this->errors) . "</p>";
        if (!empty($this->errors)) {
            echo "<ul>";
            foreach ($this->errors as $error) {
                echo "<li class='error'>{$error}</li>";
            }
            echo "</ul>";
        }
        
        // Conclusion
        if (empty($this->errors)) {
            if (empty($this->warnings)) {
                echo "<p class='success'><strong>🎉 PARFAIT ! Toutes les corrections ont été appliquées avec succès !</strong></p>";
            } else {
                echo "<p class='warning'><strong>✅ BIEN ! Corrections principales appliquées, quelques ajustements mineurs recommandés.</strong></p>";
            }
        } else {
            echo "<p class='error'><strong>❌ ATTENTION ! Il reste des erreurs à corriger.</strong></p>";
        }
        
        echo "</div>";
    }
    
    /**
     * Simule les fonctions WordPress pour les tests
     */
    private function mock_wordpress_functions() {
        if (!function_exists('esc_attr')) {
            function esc_attr($text) { return htmlspecialchars($text, ENT_QUOTES); }
        }
        if (!function_exists('esc_html')) {
            function esc_html($text) { return htmlspecialchars($text); }
        }
        if (!function_exists('esc_textarea')) {
            function esc_textarea($text) { return htmlspecialchars($text); }
        }
        if (!function_exists('esc_url')) {
            function esc_url($url) { return filter_var($url, FILTER_SANITIZE_URL); }
        }
        if (!function_exists('get_post_meta')) {
            function get_post_meta($post_id, $key, $single = false) { return ''; }
        }
        if (!function_exists('get_the_ID')) {
            function get_the_ID() { return 123; }
        }
        if (!function_exists('checked')) {
            function checked($checked, $current = true, $echo = true) { 
                return $checked == $current ? 'checked="checked"' : ''; 
            }
        }
        if (!function_exists('get_the_title')) {
            function get_the_title() { return 'Titre de test'; }
        }
        if (!function_exists('get_permalink')) {
            function get_permalink() { return 'https://example.com/test'; }
        }
        if (!function_exists('get_the_excerpt')) {
            function get_the_excerpt() { return 'Extrait de test'; }
        }
        if (!function_exists('wp_trim_words')) {
            function wp_trim_words($text, $num_words = 55) { 
                $words = explode(' ', $text);
                return implode(' ', array_slice($words, 0, $num_words));
            }
        }
        if (!function_exists('date_i18n')) {
            function date_i18n($format, $timestamp = null) { 
                return date($format, $timestamp ?: time()); 
            }
        }
        if (!function_exists('get_option')) {
            function get_option($option, $default = false) { 
                return $option === 'date_format' ? 'Y-m-d' : ($option === 'time_format' ? 'H:i:s' : $default); 
            }
        }
        if (!defined('BOSS_SEO_VERSION')) {
            define('BOSS_SEO_VERSION', '1.2.0');
        }
    }
}

// Exécuter les tests
$tester = new Boss_Optimizer_Fix_Test();
$tester->run_all_tests();

echo "</body></html>";
?>
