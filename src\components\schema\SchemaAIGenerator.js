import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  Dashicon,
  SelectControl,
  TextControl,
  ToggleControl,
  Notice,
  Spinner
} from '@wordpress/components';

// Services
import SchemaService from '../../services/SchemaService';

const SchemaAIGenerator = ({ schemaTypes, onGenerate }) => {
  // États
  const [isLoading, setIsLoading] = useState(false);
  const [contentSources, setContentSources] = useState([]);
  const [selectedSource, setSelectedSource] = useState('');
  const [selectedSchemaType, setSelectedSchemaType] = useState('');
  const [generatedSchema, setGeneratedSchema] = useState(null);
  const [options, setOptions] = useState({
    includeImages: true,
    includeMetadata: true,
    enhanceDescription: true
  });
  const [error, setError] = useState('');

  // Effet pour charger les sources de contenu
  useEffect(() => {
    const loadContentSources = async () => {
      setIsLoading(true);
      setError('');

      try {
        // Appeler l'API WordPress pour récupérer les contenus
        const response = await fetch('/wp-json/wp/v2/posts?_fields=id,title,type&per_page=20');

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des contenus');
        }

        const posts = await response.json();

        // Transformer les données pour l'interface
        const formattedSources = posts.map(post => ({
          id: post.id,
          title: post.title.rendered,
          type: 'post'
        }));

        // Récupérer également les pages
        const pagesResponse = await fetch('/wp-json/wp/v2/pages?_fields=id,title,type&per_page=20');

        if (pagesResponse.ok) {
          const pages = await pagesResponse.json();

          // Ajouter les pages aux sources
          formattedSources.push(...pages.map(page => ({
            id: page.id,
            title: page.title.rendered,
            type: 'page'
          })));
        }

        setContentSources(formattedSources);
      } catch (err) {
        console.error('Erreur lors du chargement des sources de contenu:', err);
        setError(__('Erreur lors du chargement des sources de contenu. Veuillez rafraîchir la page.', 'boss-seo'));

        // Utiliser des données fictives en cas d'erreur pour le développement
        setContentSources([
          { id: 1, title: __('Page d\'accueil', 'boss-seo'), type: 'page' },
          { id: 2, title: __('À propos', 'boss-seo'), type: 'page' },
          { id: 3, title: __('Contact', 'boss-seo'), type: 'page' },
          { id: 4, title: __('Guide SEO 2023', 'boss-seo'), type: 'post' },
          { id: 5, title: __('Optimisation des images', 'boss-seo'), type: 'post' }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    loadContentSources();
  }, []);

  // Fonction pour générer un schéma avec l'IA
  const handleGenerateSchema = async () => {
    if (!selectedSource) {
      setError(__('Veuillez sélectionner une source de contenu', 'boss-seo'));
      return;
    }

    if (!selectedSchemaType) {
      setError(__('Veuillez sélectionner un type de schéma', 'boss-seo'));
      return;
    }

    setError('');
    setIsLoading(true);

    try {
      // Trouver la source sélectionnée
      const source = contentSources.find(s => s.id === selectedSource);

      // Trouver le type de schéma sélectionné
      const schemaType = schemaTypes.find(t => t.id === selectedSchemaType);

      // Appeler le service pour générer un schéma avec l'IA
      const response = await SchemaService.generateSchemaWithAI(
        parseInt(selectedSource),
        selectedSchemaType,
        false // Ne pas sauvegarder automatiquement
      );

      if (!response.success) {
        throw new Error(response.message || __('Erreur lors de la génération du schéma', 'boss-seo'));
      }

      // Transformer les données pour l'interface
      const generatedSchemaData = {
        id: null,
        name: `${schemaType.name} - ${source.title}`,
        type: selectedSchemaType,
        active: true,
        validated: true,
        usage: 0,
        lastUpdated: new Date().toLocaleDateString(),
        properties: response.schema.properties || response.schema
      };

      setGeneratedSchema(generatedSchemaData);
    } catch (err) {
      console.error('Erreur lors de la génération du schéma:', err);
      setError(err.message || __('Erreur lors de la génération du schéma. Veuillez réessayer.', 'boss-seo'));

      // Générer un schéma fictif en cas d'erreur pour le développement
      const source = contentSources.find(s => s.id === selectedSource);
      const schemaType = schemaTypes.find(t => t.id === selectedSchemaType);

      // Schéma fictif de base
      let mockSchema = {
        id: null,
        name: `${schemaType.name} - ${source.title}`,
        type: selectedSchemaType,
        active: true,
        validated: true,
        usage: 0,
        lastUpdated: new Date().toLocaleDateString(),
        properties: {}
      };

      // Propriétés de base en fonction du type
      if (selectedSchemaType === 'Article') {
        mockSchema.properties = {
          headline: source.title,
          description: `Ceci est une description générée automatiquement pour ${source.title}.`,
          author: {
            '@type': 'Person',
            name: 'John Doe'
          },
          publisher: {
            '@type': 'Organization',
            name: 'Mon Site Web'
          },
          datePublished: new Date().toISOString().split('T')[0],
          dateModified: new Date().toISOString().split('T')[0]
        };
      } else {
        mockSchema.properties = {
          name: source.title,
          description: `Ceci est une description générée automatiquement pour ${source.title}.`
        };
      }

      setGeneratedSchema(mockSchema);
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour utiliser le schéma généré
  const handleUseSchema = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Si l'utilisateur veut sauvegarder directement le schéma
      const response = await SchemaService.generateSchemaWithAI(
        parseInt(selectedSource),
        selectedSchemaType,
        true // Sauvegarder automatiquement
      );

      if (!response.success) {
        throw new Error(response.message || __('Erreur lors de la sauvegarde du schéma', 'boss-seo'));
      }

      // Passer le schéma sauvegardé au composant parent
      onGenerate({
        ...generatedSchema,
        id: response.schema_id
      });
    } catch (err) {
      console.error('Erreur lors de la sauvegarde du schéma:', err);
      setError(err.message || __('Erreur lors de la sauvegarde du schéma. Veuillez réessayer.', 'boss-seo'));

      // En cas d'erreur, passer quand même le schéma au composant parent
      onGenerate(generatedSchema);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="boss-grid boss-grid-cols-1 lg:boss-grid-cols-3 boss-gap-6">
      {/* Panneau de configuration */}
      <div className="lg:boss-col-span-2">
        <Card className="boss-mb-6">
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Générateur IA de schémas structurés', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            {error && (
              <Notice status="error" isDismissible={false} className="boss-mb-4">
                {error}
              </Notice>
            )}

            <div className="boss-mb-6">
              <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                {__('1. Sélectionnez une source de contenu', 'boss-seo')}
              </h3>

              {isLoading && !contentSources.length ? (
                <div className="boss-flex boss-justify-center boss-items-center boss-p-6">
                  <Spinner />
                </div>
              ) : (
                <SelectControl
                  value={selectedSource}
                  options={[
                    { label: __('-- Sélectionner une page ou un article --', 'boss-seo'), value: '' },
                    ...contentSources.map(source => ({
                      label: `${source.title} (${source.type})`,
                      value: source.id
                    }))
                  ]}
                  onChange={setSelectedSource}
                />
              )}
            </div>

            <div className="boss-mb-6">
              <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                {__('2. Sélectionnez un type de schéma', 'boss-seo')}
              </h3>

              <SelectControl
                value={selectedSchemaType}
                options={[
                  { label: __('-- Sélectionner un type de schéma --', 'boss-seo'), value: '' },
                  ...schemaTypes.map(type => ({
                    label: type.name,
                    value: type.id
                  }))
                ]}
                onChange={setSelectedSchemaType}
              />
            </div>

            <div className="boss-mb-6">
              <h3 className="boss-text-md boss-font-semibold boss-mb-3">
                {__('3. Options de génération', 'boss-seo')}
              </h3>

              <div className="boss-space-y-3">
                <ToggleControl
                  label={__('Inclure les images', 'boss-seo')}
                  help={__('Utiliser les images du contenu dans le schéma', 'boss-seo')}
                  checked={options.includeImages}
                  onChange={(value) => setOptions({ ...options, includeImages: value })}
                />

                <ToggleControl
                  label={__('Inclure les métadonnées', 'boss-seo')}
                  help={__('Utiliser les métadonnées du contenu (auteur, date, etc.)', 'boss-seo')}
                  checked={options.includeMetadata}
                  onChange={(value) => setOptions({ ...options, includeMetadata: value })}
                />

                <ToggleControl
                  label={__('Améliorer les descriptions', 'boss-seo')}
                  help={__('Générer des descriptions optimisées pour le SEO', 'boss-seo')}
                  checked={options.enhanceDescription}
                  onChange={(value) => setOptions({ ...options, enhanceDescription: value })}
                />
              </div>
            </div>

            <div className="boss-flex boss-justify-center">
              <Button
                isPrimary
                className="boss-px-6"
                onClick={handleGenerateSchema}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Spinner />
                    <span className="boss-ml-2">{__('Génération en cours...', 'boss-seo')}</span>
                  </>
                ) : (
                  <>
                    <Dashicon icon="admin-generic" className="boss-mr-2" />
                    {__('Générer le schéma avec l\'IA', 'boss-seo')}
                  </>
                )}
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Panneau de prévisualisation */}
      <div>
        <Card>
          <CardHeader className="boss-border-b boss-border-gray-200">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Résultat', 'boss-seo')}
            </h2>
          </CardHeader>
          <CardBody>
            {isLoading ? (
              <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
                <Spinner />
              </div>
            ) : generatedSchema ? (
              <div>
                <div className="boss-mb-4">
                  <h3 className="boss-font-medium boss-mb-2">{generatedSchema.name}</h3>
                  <div className="boss-text-sm boss-text-boss-gray boss-mb-4">
                    {__('Type:', 'boss-seo')} {schemaTypes.find(t => t.id === generatedSchema.type)?.name}
                  </div>

                  <div className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4 boss-bg-gray-50 boss-mb-4 boss-text-sm boss-overflow-auto boss-max-h-60">
                    <pre className="boss-whitespace-pre-wrap">
                      {JSON.stringify(generatedSchema.properties, null, 2)}
                    </pre>
                  </div>
                </div>

                <Button
                  isPrimary
                  className="boss-w-full"
                  onClick={handleUseSchema}
                >
                  {__('Utiliser ce schéma', 'boss-seo')}
                </Button>
              </div>
            ) : (
              <div className="boss-text-center boss-p-6 boss-text-boss-gray">
                <Dashicon icon="admin-generic" className="boss-text-4xl boss-mb-3" />
                <p>
                  {__('Configurez les options et cliquez sur "Générer le schéma avec l\'IA" pour créer un schéma structuré automatiquement.', 'boss-seo')}
                </p>
              </div>
            )}
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default SchemaAIGenerator;
